<x-filament-panels::page>
    <div class="space-y-6">
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Thông tin phiên bản
            </h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Phiên bản hiện tại
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $currentVersion }}
                    </p>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Trạng thái
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        @if($updateInfo)
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <PERSON><PERSON> bản cập nhật mới
                            </span>
                        @else
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                Đã cập nhật mới nhất
                            </span>
                        @endif
                    </p>
                </div>
            </div>
        </div>

        @if($updateInfo)
        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Thông tin bản cập nhật
            </h3>
            <div class="space-y-4">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Phiên bản mới
                        </label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                            {{ $updateInfo['latest_version'] }}
                        </p>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                            Kích thước
                        </label>
                        <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                            {{ isset($updateInfo['size']) ? number_format($updateInfo['size'] / 1024 / 1024, 2) . ' MB' : 'Không xác định' }}
                        </p>
                    </div>
                </div>
                @if(isset($updateInfo['description']))
                <div>
                    <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
                        Mô tả
                    </label>
                    <p class="mt-1 text-sm text-gray-900 dark:text-gray-100">
                        {{ $updateInfo['description'] }}
                    </p>
                </div>
                @endif
            </div>
        </div>
        @endif

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Yêu cầu hệ thống
            </h3>
            <div class="space-y-4">
                <div class="border rounded-lg p-4 {{ $requirements['php_version']['status'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50' }}">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">PHP Version</h4>
                            <p class="text-sm text-gray-600">Yêu cầu: {{ $requirements['php_version']['required'] }} | Hiện tại: {{ $requirements['php_version']['current'] }}</p>
                        </div>
                        <div class="flex items-center">
                            @if($requirements['php_version']['status'])
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="border rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-3">PHP Extensions</h4>
                    <div class="grid grid-cols-2 gap-3">
                        @foreach($requirements['extensions'] as $extension => $info)
                        <div class="flex items-center justify-between p-2 rounded {{ $info['status'] ? 'bg-green-50' : 'bg-red-50' }}">
                            <span class="text-sm font-medium">{{ strtoupper($extension) }}</span>
                            <div class="flex items-center">
                                @if($info['status'])
                                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <div class="border rounded-lg p-4">
                    <h4 class="font-medium text-gray-900 mb-3">Quyền ghi file</h4>
                    <div class="grid gap-2">
                        @foreach($requirements['permissions'] as $permission => $info)
                        <div class="flex items-center justify-between p-2 rounded {{ $info['status'] ? 'bg-green-50' : 'bg-red-50' }}">
                            <span class="text-sm font-medium">
                                @if($permission === 'storage_writable')
                                    Storage Directory
                                @elseif($permission === 'bootstrap_cache_writable')
                                    Bootstrap Cache
                                @else
                                    {{ ucfirst(str_replace('_', ' ', $permission)) }}
                                @endif
                            </span>
                            <div class="flex items-center">
                                @if($info['status'])
                                    <svg class="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                    </svg>
                                @else
                                    <svg class="w-4 h-4 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                    </svg>
                                @endif
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>

                <div class="border rounded-lg p-4 {{ $requirements['disk_space']['status'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50' }}">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">Dung lượng ổ đĩa</h4>
                            <p class="text-sm text-gray-600">Yêu cầu: {{ $requirements['disk_space']['required'] }} | Hiện tại: {{ $requirements['disk_space']['current'] }}</p>
                        </div>
                        <div class="flex items-center">
                            @if($requirements['disk_space']['status'])
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                    </div>
                </div>

                <div class="border rounded-lg p-4 {{ $requirements['can_update'] ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50' }}">
                    <div class="flex items-center justify-between">
                        <div>
                            <h4 class="font-medium text-gray-900">Khả năng cập nhật</h4>
                            <p class="text-sm text-gray-600">
                                @if($requirements['can_update'])
                                    Hệ thống đáp ứng đủ yêu cầu để thực hiện cập nhật
                                @else
                                    Hệ thống không đáp ứng đủ yêu cầu để thực hiện cập nhật
                                @endif
                            </p>
                        </div>
                        <div class="flex items-center">
                            @if($requirements['can_update'])
                                <svg class="w-5 h-5 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                            @else
                                <svg class="w-5 h-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                </svg>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
                Lịch sử cập nhật
            </h3>
            @if(count($updateHistory) > 0)
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Phiên bản
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Trạng thái
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                Thời gian
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @foreach(array_reverse($updateHistory) as $update)
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ $update['version'] }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                @if($update['status'] === 'installed')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Đã cài đặt
                                    </span>
                                @elseif($update['status'] === 'downloaded')
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        Đã tải xuống
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                        Thất bại
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                                {{ \Carbon\Carbon::parse($update['timestamp'])->format('d/m/Y H:i:s') }}
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            @else
            <p class="text-sm text-gray-500 dark:text-gray-400">
                Chưa có lịch sử cập nhật nào.
            </p>
            @endif
        </div>
    </div>
</x-filament-panels::page> 
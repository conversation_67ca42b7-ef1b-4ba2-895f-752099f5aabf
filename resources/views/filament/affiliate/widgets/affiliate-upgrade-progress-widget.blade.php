<x-filament-widgets::widget>
    @php
        $data = $this->getData();
    @endphp

    @if(!$data['has_affiliate'])
        <div class="p-4 text-center text-gray-500">
            Bạn chưa đăng ký làm affiliate
        </div>
    @else
        <div class="p-6 bg-white rounded-lg shadow">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">
                    Tiến độ tăng cấp bậc
                </h3>
                <div class="text-sm text-gray-500">
                    Tổng hoa hồng: {{ \App\Support\Helper::formatCurrency($data['total_commission']) }}
                </div>
            </div>

            <div class="mb-4">
                <div class="flex items-center justify-between mb-2">
                    <span class="text-sm font-medium text-gray-700">
                        Cấp bậc hiện tại: {{ $data['current_tier']->name }}
                    </span>
                    <span class="text-sm text-gray-500">
                        {{ $data['current_tier']->commission_rate }}% hoa hồng
                    </span>
                </div>

                @if($data['can_upgrade'])
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">
                            Cấp bậc tiếp theo: {{ $data['next_tier']->name }}
                        </span>
                        <span class="text-sm text-gray-500">
                            {{ $data['next_tier']->commission_rate }}% hoa hồng
                        </span>
                    </div>

                    <div class="w-full bg-gray-200 rounded-full h-2.5 mb-2">
                        <div class="bg-blue-600 h-2.5 rounded-full" style="width: {{ $data['progress_percentage'] }}%"></div>
                    </div>

                    <div class="flex justify-between text-xs text-gray-500">
                        <span>{{ \App\Support\Helper::formatCurrency($data['current_tier']->min_commission_required) }}</span>
                        <span>{{ \App\Support\Helper::formatCurrency($data['next_tier']->min_commission_required) }}</span>
                    </div>

                    <div class="mt-3 text-sm text-gray-600">
                        Còn thiếu: {{ \App\Support\Helper::formatCurrency($data['remaining_amount']) }} để tăng cấp
                    </div>
                @else
                    <div class="text-sm text-green-600 font-medium">
                        🎉 Bạn đã đạt cấp bậc cao nhất!
                    </div>
                @endif
            </div>
        </div>
    @endif
</x-filament-widgets::widget> 
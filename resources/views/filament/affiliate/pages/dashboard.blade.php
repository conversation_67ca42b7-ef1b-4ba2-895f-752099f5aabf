<x-filament-panels::page>
    <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium">Thông tin giới thiệu</h2>
            </div>
            
            @php
                $user = auth()->user();
                $affiliate = \App\Models\Affiliate::where('user_id', $user->id)->first();
            @endphp
            
            @if($affiliate)
                <div class="mt-4 space-y-4">
                    <div class="flex justify-between">
                        <span class="font-medium">Mã giới thiệu:</span>
                        <span class="font-mono">{{ $affiliate->referral_code }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="font-medium">Cấp bậc:</span>
                        <span>{{ $affiliate->tier->name }}</span>
                    </div>
                    
                    <div class="flex justify-between">
                        <span class="font-medium">Tỷ lệ hoa hồng:</span>
                        <span>{{ $affiliate->tier->commission_rate }}%</span>
                    </div>
                    
                    <div class="flex justify-between items-center">
                        <span class="font-medium">Link giới thiệu:</span>
                        <div class="flex items-center space-x-2" x-data="{ copied: false }">
                            <a href="{{ $affiliate->referral_url }}" target="_blank" class="text-primary-600 hover:underline">
                                {{ $affiliate->referral_url }}
                            </a>
                            <button type="button"
                                class="inline-flex items-center px-2 py-1 text-xs font-medium bg-gray-100 hover:bg-gray-200 rounded focus:outline-none focus:ring-2 focus:ring-primary-500"
                                x-on:click="
                                    window.navigator.clipboard.writeText('{{ $affiliate->referral_url }}');
                                    copied = true;
                                    setTimeout(() => copied = false, 1500);
                                "
                                title="Copy link">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1 text-gray-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16h8M8 12h8m-7 8h6a2 2 0 002-2V6a2 2 0 00-2-2H8a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                </svg>
                                <span x-show="!copied">Copy</span>
                                <span x-show="copied" x-transition>Đã copy!</span>
                            </button>
                        </div>
                    </div>
                </div>
            @else
                <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium text-yellow-800">
                                Bạn chưa đăng ký làm affiliate
                            </h3>
                            <div class="mt-2 text-sm text-yellow-700">
                                <p>Để bắt đầu kiếm hoa hồng, bạn cần đăng ký làm affiliate trước.</p>
                            </div>
                        </div>
                    </div>
                </div>
            @endif
        </x-filament::section>
        
        <x-filament::section>
            <div class="flex items-center justify-between">
                <h2 class="text-lg font-medium">Hướng dẫn</h2>
            </div>
            
            <div class="mt-4 space-y-3 text-sm">
                <div class="flex items-start">
                    <div class="flex-shrink-0 size-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        1
                    </div>
                    <div>
                        <p class="font-medium">Chia sẻ link giới thiệu</p>
                        <p class="text-gray-600">Sử dụng link giới thiệu để thu hút khách hàng</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="flex-shrink-0 size-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        2
                    </div>
                    <div>
                        <p class="font-medium">Nhận hoa hồng</p>
                        <p class="text-gray-600">Khi khách hàng mua hàng qua link của bạn</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="flex-shrink-0 size-6 bg-primary-100 text-primary-600 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5">
                        3
                    </div>
                    <div>
                        <p class="font-medium">Rút tiền</p>
                        <p class="text-gray-600">Rút hoa hồng về tài khoản ngân hàng</p>
                    </div>
                </div>
            </div>
        </x-filament::section>
    </div>

    @if (method_exists($this, 'filtersForm'))
        {{ $this->filtersForm }}
    @endif

    <x-filament-widgets::widgets
        :columns="$this->getColumns()"
        :data="
            [
                ...(property_exists($this, 'filters') ? ['filters' => $this->filters] : []),
                ...$this->getWidgetData(),
            ]
        "
        :widgets="$this->getVisibleWidgets()"
    />
</x-filament-panels::page> 

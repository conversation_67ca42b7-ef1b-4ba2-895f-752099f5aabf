import '../css/app.css'

import Auth from '@/components/Layouts/Auth.vue'
import Master from '@/components/Layouts/Master.vue'
import User from '@/components/Layouts/User.vue'
import VueCountdown from '@chenfengyuan/vue-countdown'
import { createInertiaApp, usePage } from '@inertiajs/vue3'
import { resolvePageComponent } from 'laravel-vite-plugin/inertia-helpers'
import { DefineComponent, createApp, h } from 'vue'
import VueLazyload from 'vue-lazyload'
import VueTippy from 'vue-tippy'
import { ZiggyVue } from '../../vendor/tightenco/ziggy'
import './echo'

const appName = import.meta.env.VITE_APP_NAME || 'Laravel'

createInertiaApp({
    title: (title) => {
        const app = usePage().props.app

        return `${title || app.title} - ${app.name || appName}`
    },
    resolve: async (name) => {
        const page = await resolvePageComponent(
            `./Pages/${name}.vue`,
            import.meta.glob<DefineComponent>('./Pages/**/*.vue'),
        )

        const layout = name.startsWith('Auth/')
            ? Auth
            : name.startsWith('User/')
              ? User
              : Master

        page.default.layout = page.default.layout || layout

        return page
    },
    setup({ el, App, props, plugin }) {
        createApp({ render: () => h(App, props) })
            .use(plugin)
            .use(ZiggyVue)
            .use(VueLazyload)
            .use(VueTippy)
            .component(VueCountdown.name as string, VueCountdown)
            .mount(el)
    },
    progress: {
        color: 'hsl(var(--primary))',
    },
})

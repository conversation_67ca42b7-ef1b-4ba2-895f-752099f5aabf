<script setup lang="ts">
import { Card } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { Account, AccountCategory } from '@/types'
import { Link } from '@inertiajs/vue3'

defineProps<{
    category: AccountCategory
    account: Account
}>()
</script>

<template>
    <Card>
        <div class="relative">
            <Link :href="route('accounts.show', account.id)">
                <img
                    v-lazy="account.image"
                    :alt="`Tài khoản ${category.name} #${account.id}`"
                    class="aspect-video object-fill w-full h-full rounded-t-lg"
                />
            </Link>

            <span
                class="absolute top-2 start-2 text-xs bg-primary/90 text-primary-foreground font-medium px-2 py-0.5 rounded-lg"
            >
                #{{ account.id }}
            </span>
            <div
                v-if="account.description"
                class="absolute bottom-0 w-full truncate text-white bg-gray-950/40 px-3 py-1 text-sm"
                :title="account.description"
            >
                {{ account.description }}
            </div>
        </div>
        <div class="p-3">
            <dl class="grid gap-2">
                <div
                    v-for="attribute in account.attributes"
                    :key="attribute.id"
                    class="flex justify-between gap-8"
                >
                    <dt class="text-xs sm:text-sm text-nowrap">
                        {{ attribute.attribute_set.name }}
                    </dt>
                    <dd class="text-xs sm:text-sm font-medium truncate">
                        {{ attribute.display_value }}
                    </dd>
                </div>
            </dl>
            <Link
                :href="route('accounts.show', account.id)"
                class="block mt-3 sm:mt-4 bg-primary text-primary-foreground text-center w-full sm:w-auto font-semibold rounded-lg px-3 py-1.5"
            >
                <span
                    v-if="account.original_price > account.price"
                    class="me-1 text-sm line-through"
                >
                    {{ formatCurrency(account.original_price) }}
                </span>
                {{ formatCurrency(account.price) }}
            </Link>
        </div>
    </Card>
</template>

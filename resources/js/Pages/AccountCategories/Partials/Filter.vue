<script setup lang="ts">
import { FilterForm } from '@/components'
import FilterInput from '@/components/Forms/Filter/FilterInput.vue'
import FilterSelect from '@/components/Forms/Filter/FilterSelect.vue'
import FilterWrapper from '@/components/Forms/Filter/FilterWrapper.vue'
import { SelectGroup, SelectItem } from '@/components/ui/select'
import { AttributeSet } from '@/types'
import { InertiaForm } from '@inertiajs/vue3'
import { computed } from 'vue'
import { FilterFormType, PriceRangesType } from '../types'

const props = defineProps<{
    form: InertiaForm<FilterFormType>
    attributeSets: AttributeSet[]
    priceRanges: PriceRangesType
}>()

defineEmits(['reset'])

const isFiltering = computed(() => {
    const data = Object(props.form.data())

    return !!(
        data.search !== '' ||
        data.price !== '' ||
        data.sort_by !== '' ||
        Object.keys(data.attributes).length
    )
})
</script>

<template>
    <FilterForm
        :is-filtering="isFiltering"
        @reset="$emit('reset')"
        class="grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 mb-6 gap-2 sm:gap-4"
    >
        <FilterWrapper label="Tìm kiếm">
            <FilterInput
                v-model="form.search"
                placeholder="Nhập ID, mô tả tài khoản..."
            />
        </FilterWrapper>

        <FilterWrapper
            v-for="attributeSet in attributeSets"
            :key="attributeSet.id"
            :label="attributeSet.name"
        >
            <FilterSelect
                v-if="attributeSet.type === 'dropdown'"
                v-model="form.attributes[attributeSet.slug]"
                placeholder="Chọn thuộc tính"
            >
                <SelectGroup>
                    <SelectItem
                        v-for="attribute in attributeSet.attributes"
                        :key="attribute.id"
                        :value="attribute.slug"
                    >
                        {{ attribute.name }}
                    </SelectItem>
                </SelectGroup>
            </FilterSelect>

            <FilterInput
                v-else-if="attributeSet.type === 'text'"
                v-model="form.attributes[attributeSet.slug]"
                :placeholder="`Nhập ${attributeSet.name.toLowerCase()}...`"
            />
        </FilterWrapper>
        <FilterWrapper label="Giá tiền">
            <FilterSelect v-model="form.price" placeholder="Chọn mức giá">
                <SelectGroup>
                    <SelectItem value="null">Tất cả</SelectItem>
                    <SelectItem
                        v-for="(priceRange, key) in priceRanges"
                        :key="key"
                        :value="String(key)"
                    >
                        {{ priceRange }}
                    </SelectItem>
                </SelectGroup>
            </FilterSelect>
        </FilterWrapper>
        <FilterWrapper label="Sắp xếp theo">
            <FilterSelect v-model="form.sort_by" placeholder="Sắp xếp theo">
                <SelectGroup>
                    <SelectItem value="null">Mặc định</SelectItem>
                    <SelectItem value="price-asc">Giá thấp đến cao</SelectItem>
                    <SelectItem value="price-desc">Giá cao đến thấp</SelectItem>
                    <SelectItem value="newest">Mới nhất</SelectItem>
                    <SelectItem value="oldest">Cũ nhất</SelectItem>
                </SelectGroup>
            </FilterSelect>
        </FilterWrapper>
    </FilterForm>
</template>

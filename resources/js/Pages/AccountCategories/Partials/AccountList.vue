<script setup lang="ts">
import { Account, AccountCategory } from '@/types'

defineProps<{
    category: AccountCategory
    accounts: Account[]
}>()
</script>

<template>
    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4">
        <AccountItem
            v-for="account in accounts"
            :key="account.id"
            :category="category"
            :account
        />
    </div>
</template>

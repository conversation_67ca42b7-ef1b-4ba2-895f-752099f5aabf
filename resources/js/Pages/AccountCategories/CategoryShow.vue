<script setup lang="ts">
import AccountCategoryList from '@/components/AccountCategory/AccountCategoryList.vue'
import MagnifyingGlassIcon from '@/components/Icons/MagnifyingGlassIcon.vue'
import { Input } from '@/components/ui/input'
import { AccountCategory, AttributeSet } from '@/types'
import { Head, router, useForm } from '@inertiajs/vue3'
import { pickBy, throttle } from 'lodash'
import { computed, watch } from 'vue'
import { CategoryFilterFormType, PriceRangesType } from './types'

const props = defineProps<{
    category: AccountCategory
    children?: AccountCategory[]
    attributeSets: AttributeSet[]
    filters: Record<string, string>
    priceRanges: PriceRangesType
}>()

const form = useForm<CategoryFilterFormType>({
    category_search: props.filters.category_search || '',
})

const request = (data: Record<string, string>) => {
    form.transform(() => data).get(
        route('categories.show', props.category.slug),
        {
            preserveState: true,
            only: ['children'],
        },
    )
}

const reset = () => {
    router.get(route('categories.show', props.category.slug))
}

watch(
    () => form.data(),
    throttle(() => request(pickBy(Object(form.data())))),
    { deep: true },
)

const shouldShowSearch = computed(() => {
    return (
        (props.children && props.children.length > 0) ||
        props.filters.category_search
    )
})

const shouldShowCategories = computed(() => {
    return props.children && props.children.length > 0
})
</script>

<template>
    <Head :title="`${category.name}`" />

    <div class="container py-4">
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <div
                    v-if="category.image"
                    class="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden flex-shrink-0"
                >
                    <img
                        v-lazy="category.image"
                        :alt="category.name"
                        class="w-full h-full object-fill"
                    />
                </div>
                <div class="flex-1">
                    <h1
                        class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white"
                    >
                        {{ category.name }}
                    </h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span
                            v-if="category.game"
                            class="text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full"
                        >
                            {{ category.game.name }}
                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{ category.remaining_accounts_count || 0 }} tài
                            khoản
                        </span>
                    </div>
                </div>
            </div>

            <div
                v-if="category.description"
                class="text-gray-600 dark:text-gray-400 text-lg leading-relaxed"
                v-html="category.description"
            ></div>
        </div>

        <div v-if="shouldShowSearch" class="mb-6">
            <div class="relative max-w-md">
                <div
                    class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                    <MagnifyingGlassIcon class="h-5 w-5 text-gray-400" />
                </div>
                <Input
                    v-model="form.category_search"
                    type="search"
                    placeholder="Tìm kiếm danh mục..."
                    class="pl-10"
                />
            </div>
        </div>

        <div v-if="shouldShowCategories" class="mb-6">
            <AccountCategoryList :categories="children || []" />
        </div>

        <div v-else-if="filters.category_search">
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400">
                    <p class="text-lg font-medium mb-2">
                        Không tìm thấy danh mục nào
                    </p>
                    <p class="text-sm">Hãy thử lại với từ khóa khác</p>
                </div>
            </div>
        </div>

        <div v-else>
            <div class="text-center py-12">
                <div class="text-gray-500 dark:text-gray-400">
                    <p class="text-lg font-medium mb-2">
                        Không có danh mục con nào
                    </p>
                    <p class="text-sm">
                        Hãy quay lại sau hoặc liên hệ admin để được hỗ trợ
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import AdWidget from '@/components/AdWidget.vue'
import Pagination from '@/components/CustomPagination.vue'
import EmptyState from '@/components/EmptyState.vue'
import {
    Account,
    AccountCategory,
    AttributeSet,
    LengthAwarePaginator,
} from '@/types'
import { Head, Link, router, useForm } from '@inertiajs/vue3'
import { pickBy, throttle } from 'lodash'
import { watch } from 'vue'
import AccountItem from './Partials/AccountItem.vue'
import Filter from './Partials/Filter.vue'
import { FilterFormType, PriceRangesType } from './types'

const props = defineProps<{
    category: AccountCategory
    children?: AccountCategory[]
    attributeSets: AttributeSet[]
    accounts: LengthAwarePaginator<Account>
    filters: Record<string, string>
    priceRanges: PriceRangesType
}>()

const form = useForm<FilterFormType>({
    search: props.filters.search || '',
    price: props.filters.price || '',
    sort_by: props.filters.sort_by || '',
    attributes: Object(props.filters.attributes) || {},
})

const request = (data: Record<string, string>) => {
    form.transform(() => data).get(
        route('categories.show', props.category.slug),
        {
            preserveState: true,
            only: ['accounts'],
        },
    )
}

const reset = () => {
    router.get(route('categories.show', props.category.slug))
}

watch(
    () => form.data(),
    throttle(() => request(pickBy(Object(form.data())))),
    { deep: true },
)
</script>

<template>
    <Head :title="`${category.name}`" />

    <div class="container py-4">
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <div
                    v-if="category.image"
                    class="w-20 h-20 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden flex-shrink-0"
                >
                    <img
                        v-lazy="category.image"
                        :alt="category.name"
                        class="w-full h-full object-cover"
                    />
                </div>
                <div class="flex-1">
                    <h1
                        class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white"
                    >
                        {{ category.name }}
                    </h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span
                            v-if="category.game"
                            class="text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full"
                        >
                            {{ category.game.name }}
                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{ category.remaining_accounts_count || 0 }} tài
                            khoản
                        </span>
                    </div>
                </div>
            </div>

            <div
                v-if="category.description"
                class="text-gray-600 dark:text-gray-400 text-lg leading-relaxed"
                v-html="category.description"
            ></div>
        </div>

        <div v-if="children && children.length > 0" class="mb-6">
            <h2
                class="text-xl font-semibold text-gray-900 dark:text-white mb-4"
            >
                Danh mục con
            </h2>
            <div
                class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4"
            >
                <div
                    v-for="child in children"
                    :key="child.id"
                    class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden"
                >
                    <Link :href="route('categories.show', child.slug)">
                        <img
                            v-lazy="child.image"
                            :alt="child.name"
                            class="w-full h-32 object-cover"
                        />
                        <div class="p-4">
                            <h3
                                class="font-medium text-gray-900 dark:text-white truncate"
                            >
                                {{ child.name }}
                            </h3>
                            <p
                                class="text-sm text-gray-500 dark:text-gray-400 mt-1"
                            >
                                {{ child.remaining_accounts_count || 0 }} tài
                                khoản
                            </p>
                        </div>
                    </Link>
                </div>
            </div>
        </div>

        <div>
            <Filter :form :attributeSets :priceRanges @reset="reset" />

            <div v-if="accounts.data.length > 0">
                <div
                    class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4"
                >
                    <AccountItem
                        v-for="account in accounts.data"
                        :key="account.id"
                        :category="category"
                        :account
                    />
                </div>

                <Pagination :meta="accounts.meta" class="mt-8" />

                <AdWidget position="account_list" />
            </div>

            <div v-else>
                <EmptyState
                    title="Không có tài khoản nào"
                    description="Hãy quay lại sau hoặc thử lại với bộ lọc khác"
                />
            </div>
        </div>
    </div>
</template>

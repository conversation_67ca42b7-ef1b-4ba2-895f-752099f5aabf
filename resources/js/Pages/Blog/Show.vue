<script setup lang="ts">
import AdWidget from '@/components/AdWidget.vue'
import ClockIcon from '@/components/Icons/ClockIcon.vue'
import EyeIcon from '@/components/Icons/EyeIcon.vue'
import type { Category, Post } from '@/types'
import { Head, Link } from '@inertiajs/vue3'
import { computed } from 'vue'
import Sidebar from './Partials/Sidebar.vue'

const props = defineProps<{
    categories: Category[]
    post: Post
}>()

const totalPosts = computed(() => {
    return props.categories.reduce(
        (acc, category) => acc + category.posts_count,
        0,
    )
})
</script>

<template>
    <Head :title="post.title" />
    <div class="container py-4">
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-8">
            <div class="col-span-2">
                <div>
                    <h2 class="text-2xl font-bold mb-4">{{ post.title }}</h2>
                    <div class="flex flex-wrap items-center gap-4 mb-6">
                        <div
                            class="text-muted-foreground text-sm flex items-center gap-1"
                        >
                            <img
                                v-lazy="post.user.avatar_url"
                                :alt="post.user.name"
                                class="size-5 rounded-full"
                            />
                            <span>{{ post.user.name }}</span>
                        </div>
                        <div
                            class="text-muted-foreground text-sm flex items-center gap-1"
                        >
                            <ClockIcon class="size-5" />
                            <time datetime="{{ post.created_at }}">
                                {{ post.created_at }}
                            </time>
                        </div>
                        <div
                            class="text-muted-foreground text-sm flex items-center gap-1"
                        >
                            <EyeIcon class="size-5" />
                            {{ post.views }} lượt xem
                        </div>
                        <Link
                            v-if="post.category"
                            :href="route('blog.show', post.category.slug)"
                            class="px-2 py-1 bg-primary text-primary-foreground text-xs font-semibold rounded-lg"
                        >
                            {{ post.category.name }}
                        </Link>
                    </div>
                    <img
                        v-lazy="post.image"
                        :alt="post.title"
                        class="w-full object-fill rounded-lg mb-6"
                    />
                </div>
                <div
                    class="prose prose-p:mb-2 prose-h2:mt-6 prose-h2:mb-3 prose-li:my-1 w-full dark:prose-invert"
                    v-html="post.content"
                ></div>

                <AdWidget position="blog_detail" />
            </div>
            <div class="col-span-1">
                <Sidebar :categories="categories" :total-posts="totalPosts" />
            </div>
        </div>
    </div>
</template>

<script setup lang="ts">
import Pagination from '@/components/CustomPagination.vue'
import ClockIcon from '@/components/Icons/ClockIcon.vue'
import SectionHeading from '@/components/SectionHeading.vue'
import { Card } from '@/components/ui/card'
import type { Category, LengthAwarePaginator, Post } from '@/types'
import { Head, Link } from '@inertiajs/vue3'
import { computed } from 'vue'
import Sidebar from './Partials/Sidebar.vue'

const props = defineProps<{
    category?: Category
    categories: Category[]
    posts: LengthAwarePaginator<Post>
}>()

const totalPosts = computed(() => {
    return props.categories.reduce(
        (acc, category) => acc + category.posts_count,
        0,
    )
})
</script>

<template>
    <Head :title="category ? category.name : 'Tin tức'" />

    <div class="container py-4">
        <SectionHeading :heading="category ? category.name : 'Tin tức'" />
        <div class="grid grid-cols-1 sm:grid-cols-3 gap-x-4 gap-y-8">
            <div class="col-span-1 sm:col-span-2">
                <template v-if="posts.data.length">
                    <div class="space-y-4">
                        <Card
                            class="group flex"
                            v-for="(post, index) in posts.data"
                            :key="index"
                        >
                            <Link
                                :href="route('blog.show', post.slug)"
                                class="block w-32 sm:w-56 aspect-video shrink-0 rounded-s-lg overflow-hidden"
                            >
                                <img
                                    v-lazy="post.image"
                                    :alt="post.title"
                                    class="object-cover w-full h-full transition-all group-hover:scale-110"
                                />
                            </Link>

                            <div
                                class="max-w-full w-full grid items-stretch p-3 gap-y-2"
                            >
                                <div class="w-full overflow-hidden">
                                    <h3 class="truncate mb-0.5">
                                        <Link
                                            :href="
                                                route('blog.show', post.slug)
                                            "
                                            :title="post.title"
                                            class="text-lg font-semibold text-gray-950 dark:text-white hover:text-primary transition-all"
                                        >
                                            {{ post.title }}
                                        </Link>
                                    </h3>
                                    <p
                                        class="text-sm text-muted-foreground truncate"
                                    >
                                        {{ post.description }}
                                    </p>
                                </div>
                                <div
                                    class="flex items-center gap-x-3 gap-y-2 flex-wrap text-sm"
                                >
                                    <div
                                        class="flex items-center gap-1 text-muted-foreground"
                                    >
                                        <img
                                            v-lazy="post.user.avatar_url"
                                            :alt="post.user.name"
                                            class="size-5 rounded-full"
                                        />
                                        {{ post.user.name }}
                                    </div>
                                    <div
                                        class="flex items-center gap-1 text-muted-foreground"
                                    >
                                        <ClockIcon class="size-5" />
                                        {{ post.created_at }}
                                    </div>
                                    <Link
                                        v-if="post.category"
                                        :href="
                                            route(
                                                'blog.show',
                                                post.category.slug,
                                            )
                                        "
                                        class="px-2 py-1 bg-primary text-primary-foreground text-xs font-semibold rounded-lg"
                                    >
                                        {{ post.category.name }}
                                    </Link>
                                </div>
                            </div>
                        </Card>
                    </div>
                    <Pagination :meta="posts.meta" class="mt-4" />
                </template>
                <template v-else>
                    <div class="text-center text-muted-foreground">
                        Không có bài viết nào
                    </div>
                </template>
            </div>
            <div class="col-span-1">
                <Sidebar :categories="categories" :total-posts="totalPosts" />
            </div>
        </div>
    </div>
</template>

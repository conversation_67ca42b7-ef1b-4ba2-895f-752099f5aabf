<script setup lang="ts">
import AdWidget from '@/components/AdWidget.vue'
import ChevronRightIcon from '@/components/Icons/ChevronRightIcon.vue'
import type { Category } from '@/types'
import { Link } from '@inertiajs/vue3'
import { computed } from 'vue'

defineProps<{
    categories: Category[]
    totalPosts: number
}>()

const slug = computed(() => route().params.slug)
</script>

<template>
    <h3 class="text-lg font-medium mb-3"><PERSON>h mục</h3>
    <ul class="grid gap-2">
        <li>
            <Link
                :href="route('blog.index')"
                class="text-sm group hover:text-primary transition-all flex items-center gap-1.5"
                :class="{ 'text-primary': slug === undefined }"
            >
                <ChevronRightIcon
                    class="size-5 group-hover:translate-x-1 transition-all"
                />
                Tất cả
                <span class="font-medium">({{ totalPosts }})</span>
            </Link>
        </li>
        <li v-for="(category, index) in categories" :key="index">
            <Link
                :href="route('blog.show', category.slug)"
                class="text-sm group hover:text-primary transition-all flex items-center gap-1.5"
                :class="{
                    'text-primary': slug === category.slug,
                }"
            >
                <ChevronRightIcon
                    class="size-5 group-hover:translate-x-1 transition-all"
                />
                {{ category.name }}
                <span class="font-medium">({{ category.posts_count }})</span>
            </Link>
        </li>
    </ul>

    <!-- Blog Sidebar Widget -->
    <AdWidget position="blog_sidebar" />
</template>

<script setup lang="ts">
import SectionHeading from '@/components/SectionHeading.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    <PERSON><PERSON>,
    <PERSON>alog<PERSON>ontent,
    <PERSON><PERSON>Footer,
    <PERSON>alogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { useToast } from '@/components/ui/toast'
import { cn } from '@/lib/utils'
import '@/libs/jquery.rotate.js'
import { Wheel } from '@/types'
import { Head, Link, useForm, usePage } from '@inertiajs/vue3'
import { ref } from 'vue'

const props = defineProps<{
    wheel: Wheel
}>()

const spinning = ref(false)
const wheelElement = ref(null)
const isDescriptionModalOpen = ref(false)

const form = useForm<{
    spins: number
}>({
    spins: 1,
})

const { toast } = useToast()

const spin = () => {
    if (spinning.value) {
        return
    }

    spinning.value = true

    form.post(route('wheels.spin', props.wheel.slug), {
        preserveScroll: true,
        preserveState: true,
        except: ['wheel'],
        onSuccess: () => {
            const { flash, position } = usePage().props

            if (flash.error) {
                spinning.value = false
                toast({
                    title: 'Thất bại',
                    description: flash.error,
                    variant: 'destructive',
                })
                return
            }

            // @ts-ignore
            $(wheelElement.value).rotate({
                angle: 0,
                animateTo: Number(position) + 1800,
                duration: 5000,
                callback: function () {
                    spinning.value = false

                    // @ts-ignore
                    toast({
                        title: 'Chúc mừng',
                        description: flash.success,
                        variant: 'success',
                    })
                },
            })
        },
    })
}
</script>

<template>
    <Head :title="wheel.name" />

    <div class="container my-4">
        <SectionHeading :heading="wheel.name" />

        <div class="grid grid-cols-1 lg:grid-cols-5 gap-4">
            <div class="col-span-1 md:col-span-3">
                <Card class="p-4">
                    <div class="relative">
                        <img
                            :src="wheel.image"
                            :alt="wheel.name"
                            ref="wheelElement"
                            class="w-full"
                        />
                        <div
                            @click="spin"
                            class="absolute size-20 sm:size-40 bg-no-repeat bg-contain bg-center start-[calc(50%-2.5rem)] top-[calc(50%-2.5rem)] sm:start-[calc(50%-5rem)] sm:top-[calc(50%-5rem)] bg-[url('/images/spin-btn.png')] hover:cursor-pointer"
                            role="button"
                        ></div>
                    </div>
                </Card>
            </div>
            <div class="col-span-1 md:col-span-2">
                <Card class="mb-4 pt-6">
                    <CardContent>
                        <div
                            class="grid grid-cols-1 gap-2"
                            :class="
                                cn(
                                    wheel.description
                                        ? 'grid-cols-3'
                                        : 'grid-cols-2',
                                )
                            "
                        >
                            <Button
                                v-if="wheel.description"
                                @click="isDescriptionModalOpen = true"
                            >
                                Thể lệ
                            </Button>
                            <Button as-child>
                                <Link
                                    :href="
                                        route('user.wheel-spins', {
                                            wheel: wheel.id,
                                        })
                                    "
                                >
                                    Lịch sử
                                </Link>
                            </Button>
                            <Button as-child>
                                <Link
                                    :href="route('user.withdraw-items.index')"
                                >
                                    Rút thưởng
                                </Link>
                            </Button>
                        </div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader>
                        <CardTitle class="text-lg">Lịch sử quay</CardTitle>
                    </CardHeader>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Người dùng</TableHead>
                                <TableHead>Giải thưởng</TableHead>
                                <TableHead>Thời gian</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            <template v-if="wheel.wheel_spins.length">
                                <TableRow
                                    v-for="transaction in wheel.wheel_spins"
                                    :key="transaction.id"
                                >
                                    <TableCell>{{
                                        transaction.user.name
                                    }}</TableCell>
                                    <TableCell>{{
                                        transaction.result
                                    }}</TableCell>
                                    <TableCell>{{
                                        transaction.created_at
                                    }}</TableCell>
                                </TableRow>
                            </template>
                            <TableRow v-else>
                                <TableCell
                                    colspan="3"
                                    className="h-16 text-center"
                                >
                                    Không có dữ liệu
                                </TableCell>
                            </TableRow>
                        </TableBody>
                    </Table>
                </Card>
            </div>
        </div>
    </div>

    <Dialog v-model:open="isDescriptionModalOpen" v-if="wheel.description">
        <DialogContent class="max-w-2xl">
            <DialogHeader>
                <DialogTitle>Thông tin thể lệ vòng quay</DialogTitle>
            </DialogHeader>

            <div
                v-if="wheel.description"
                class="prose"
                v-html="wheel.description"
            />

            <DialogFooter>
                <Button
                    type="button"
                    class="w-full"
                    variant="outline"
                    @click="isDescriptionModalOpen = false"
                >
                    Đóng
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

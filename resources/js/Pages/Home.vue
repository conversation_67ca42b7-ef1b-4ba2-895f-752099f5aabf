<script setup lang="ts">
import AccountCategoryList from '@/components/AccountCategory/AccountCategoryList.vue'
import AdWidget from '@/components/AdWidget.vue'
import FlashSaleList from '@/components/FlashSale/FlashSaleList.vue'
import SectionHeading from '@/components/SectionHeading.vue'
import type { Depositor } from '@/components/TopDepositor/TopDepositorItem.vue'
import TopDepositorList from '@/components/TopDepositor/TopDepositorList.vue'
import {
    Carousel,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel'
import WheelList from '@/components/Wheel/WheelList.vue'
import type { AccountCategory, FlashSale, Game, Wheel } from '@/types'
import { Link } from '@inertiajs/vue3'
import { ChevronRight } from 'lucide-vue-next'
import { computed } from 'vue'

const props = defineProps<{
    categories: AccountCategory[]
    wheels: Wheel[]
    games: Game[]
    bannerSliders: {
        label: string | null
        image: string
        url: string | null
        open_in_new_tab: boolean
    }[]
    topDepositors: Depositor[]
    flashSale?: FlashSale
    homeSectionsOrder: {
        section: string
        label: string
        is_visible: boolean
        custom_title: string | null
    }[]
}>()

const groupedCategories = computed(() => {
    const parentCategories = props.categories.filter((cat) => !cat.parent_id)
    const childCategories = props.categories.filter((cat) => cat.parent_id)

    return parentCategories
        .map((parent) => ({
            parent,
            children: childCategories.filter(
                (child) => child.parent_id == parent.id,
            ),
        }))
        .filter((group) => group.children.length > 0)
})

const parentCategoriesWithoutChildren = computed(() => {
    const parentCategories = props.categories.filter((cat) => !cat.parent_id)
    const childCategories = props.categories.filter((cat) => cat.parent_id)

    return parentCategories.filter((parent) => {
        const hasChildren = childCategories.some(
            (child) => child.parent_id == parent.id,
        )
        return !hasChildren
    })
})
</script>

<template>
    <div class="container py-4">
        <template
            v-for="sectionConfig in homeSectionsOrder"
            :key="sectionConfig.section"
        >
            <section
                v-if="
                    sectionConfig.section === 'banner_sliders' &&
                    sectionConfig.is_visible &&
                    bannerSliders.length
                "
                class="mb-8"
            >
                <div class="grid grid-cols-1 lg:grid-cols-12 gap-2 sm:gap-4">
                    <div
                        :class="{
                            'col-span-12 lg:col-span-8 xl:col-span-9':
                                topDepositors.length,
                            'col-span-12': !topDepositors.length,
                        }"
                    >
                        <div
                            class="h-full overflow-hidden max-w-full rounded-lg ring-1 ring-gray-950/10 dark:ring-white/10"
                        >
                            <Carousel>
                                <CarouselContent>
                                    <CarouselItem
                                        v-for="(
                                            bannerSlider, index
                                        ) in bannerSliders"
                                        :key="index"
                                    >
                                        <component
                                            :is="bannerSlider.url ? 'a' : 'div'"
                                            :href="bannerSlider.url || null"
                                            :target="
                                                bannerSlider.open_in_new_tab
                                                    ? '_blank'
                                                    : '_self'
                                            "
                                        >
                                            <img
                                                v-lazy="bannerSlider.image"
                                                :alt="
                                                    bannerSlider.label ||
                                                    $page.props.app.name
                                                "
                                                class="h-full w-full object-fill"
                                            />
                                        </component>
                                    </CarouselItem>
                                </CarouselContent>
                            </Carousel>
                        </div>
                    </div>
                    <div
                        class="col-span-12 lg:col-span-4 xl:col-span-3"
                        v-if="topDepositors.length"
                    >
                        <TopDepositorList :topDepositors="topDepositors" />
                    </div>
                </div>
            </section>

            <section
                v-else-if="
                    sectionConfig.section === 'ad_home_banner' &&
                    sectionConfig.is_visible
                "
                class="mb-8"
            >
                <AdWidget position="home_banner" />
            </section>

            <section
                v-else-if="
                    sectionConfig.section === 'flash_sale' &&
                    sectionConfig.is_visible &&
                    flashSale &&
                    flashSale.accounts &&
                    flashSale.accounts.length > 0
                "
                class="mb-8"
            >
                <FlashSaleList :flash-sale="flashSale" />

                <div class="mt-4 text-center">
                    <Link
                        :href="route('flash-sales.index')"
                        class="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                    >
                        Xem tất cả
                        <ChevronRight class="w-4 h-4 ml-1" />
                    </Link>
                </div>
            </section>

            <section
                v-else-if="
                    sectionConfig.section === 'wheels' &&
                    sectionConfig.is_visible &&
                    wheels.length
                "
                class="mb-8"
            >
                <WheelList :wheels="wheels" />
            </section>

            <template
                v-else-if="
                    sectionConfig.section === 'account_categories' &&
                    sectionConfig.is_visible
                "
            >
                <section
                    v-for="group in groupedCategories"
                    :key="group.parent.id"
                    class="mb-8"
                >
                    <SectionHeading :heading="group.parent.name" />

                    <AccountCategoryList :categories="group.children" />

                    <div class="mt-4 text-center">
                        <Link
                            :href="route('categories.show', group.parent.slug)"
                            class="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
                        >
                            Xem tất cả {{ group.parent.name }}
                            <svg
                                class="inline w-4 h-4 ml-1"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                            >
                                <path
                                    stroke-linecap="round"
                                    stroke-linejoin="round"
                                    stroke-width="2"
                                    d="M9 5l7 7-7 7"
                                />
                            </svg>
                        </Link>
                    </div>
                </section>

                <section
                    v-if="parentCategoriesWithoutChildren.length > 0"
                    class="mb-8"
                >
                    <AccountCategoryList
                        :categories="parentCategoriesWithoutChildren"
                    />
                </section>
            </template>
        </template>
    </div>
</template>

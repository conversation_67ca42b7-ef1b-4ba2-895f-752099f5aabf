<script setup lang="ts">
import { Game } from '@/types'
import { Head, <PERSON> } from '@inertiajs/vue3'

defineProps<{
    games: Game[]
}>()
</script>

<template>
    <Head title="Danh sách game" />

    <div class="container py-4">
        <div class="mb-6">
            <h1 class="text-2xl font-bold text-gray-900 dark:text-white">
                Danh sách game
            </h1>
            <p class="text-gray-600 dark:text-gray-400 mt-2">
                Chọn game để xem các loại tài khoản có sẵn
            </p>
        </div>

        <div
            class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4"
        >
            <div
                v-for="game in games"
                :key="game.id"
                class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300 border border-gray-200 dark:border-gray-700"
            >
                <div v-if="game.image" class="bg-gray-200 dark:bg-gray-700">
                    <img
                        v-lazy="game.image"
                        :alt="game.name"
                        class="w-full h-full object-fill"
                    />
                </div>

                <div class="p-4">
                    <div class="flex items-center justify-between mb-2">
                        <h3
                            class="text-sm font-semibold text-gray-900 dark:text-white truncate"
                        >
                            {{ game.name }}
                        </h3>
                    </div>

                    <div class="flex items-center justify-between mb-3">
                        <span
                            v-if="game.publisher"
                            class="text-xs text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded"
                        >
                            {{ game.publisher.name }}
                        </span>
                        <span class="text-xs text-gray-500 dark:text-gray-400">
                            {{
                                game.categories_count ||
                                game.categories?.length ||
                                0
                            }}
                            danh mục
                        </span>
                    </div>

                    <p
                        v-if="game.description"
                        class="text-gray-600 dark:text-gray-400 text-xs mb-4 line-clamp-2"
                    >
                        {{ game.description }}
                    </p>

                    <Link
                        :href="route('games.show', game.slug)"
                        class="w-full inline-flex items-center justify-center px-3 py-2 bg-primary text-white text-sm font-medium rounded-md hover:bg-primary/90 transition-colors duration-200"
                    >
                        Xem chi tiết
                        <svg
                            class="ml-2 w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                        >
                            <path
                                stroke-linecap="round"
                                stroke-linejoin="round"
                                stroke-width="2"
                                d="M9 5l7 7-7 7"
                            />
                        </svg>
                    </Link>
                </div>
            </div>
        </div>

        <div v-if="games.length === 0" class="text-center py-12">
            <svg
                class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                Không có game nào
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Hãy quay lại sau.
            </p>
        </div>
    </div>
</template>

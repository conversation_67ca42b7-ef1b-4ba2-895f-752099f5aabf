<script setup lang="ts">
import { Game } from '@/types'
import { Head, <PERSON> } from '@inertiajs/vue3'

const props = defineProps<{
    game: Game
}>()
</script>

<template>
    <Head :title="`${game.name} - <PERSON>h mục t<PERSON>`" />

    <div class="container py-4">
        <div class="mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <div
                    v-if="game.image"
                    class="size-24 bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden flex-shrink-0"
                >
                    <img
                        v-lazy="game.image"
                        :alt="game.name"
                        class="w-full h-full object-cover"
                    />
                </div>
                <div class="flex-1">
                    <h1
                        class="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white"
                    >
                        {{ game.name }}
                    </h1>
                    <div class="flex items-center space-x-2 mt-1">
                        <span
                            v-if="game.publisher"
                            class="text-sm text-gray-600 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-3 py-1 rounded-full"
                        >
                            {{ game.publisher.name }}
                        </span>
                        <span class="text-sm text-gray-500 dark:text-gray-400">
                            {{
                                game.categories_count ||
                                game.categories?.length ||
                                0
                            }}
                            danh mục
                        </span>
                    </div>
                </div>
            </div>

            <p
                v-if="game.description"
                class="text-gray-600 dark:text-gray-400 text-lg leading-relaxed"
            >
                {{ game.description }}
            </p>
        </div>

        <div class="mb-6">
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <div
                    v-for="category in game.categories"
                    :key="category.slug"
                    class="rounded-lg border bg-card text-card-foreground shadow-xs group border-gray-200 dark:border-gray-700"
                >
                    <Link :href="route('categories.show', category.slug)">
                        <img
                            v-if="category.image"
                            :src="category.image"
                            :alt="category.name"
                            class="transition-all aspect-video w-full object-cover rounded-t-lg"
                        />
                        <div
                            v-else
                            class="transition-all aspect-video w-full bg-gray-200 dark:bg-gray-700 rounded-t-lg flex items-center justify-center"
                        >
                            <svg
                                class="w-12 h-12 text-gray-500 dark:text-gray-400"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                            >
                                <path
                                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                                />
                            </svg>
                        </div>
                    </Link>

                    <div class="text-center p-3">
                        <h4
                            class="font-medium truncate group-hover:text-primary transition-all mb-2"
                        >
                            <Link
                                :href="route('categories.show', category.slug)"
                                :title="category.name"
                                class="hover:text-primary transition-all"
                            >
                                {{ category.name }}
                            </Link>
                        </h4>

                        <div class="text-muted-foreground text-sm">
                            {{
                                category.children_count ||
                                category.children?.length ||
                                0
                            }}
                            danh mục con
                        </div>

                        <div class="grid mt-4">
                            <Link
                                :href="route('categories.show', category.slug)"
                                class="inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-hidden focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2 w-full"
                            >
                                Xem danh mục con
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div
            v-if="!game.categories || game.categories.length === 0"
            class="text-center py-12"
        >
            <svg
                class="mx-auto h-12 w-12 text-gray-400 dark:text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
            </svg>
            <h3 class="mt-2 text-sm font-medium text-gray-900 dark:text-white">
                Không có danh mục nào
            </h3>
            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                Hãy quay lại sau.
            </p>
        </div>

        <div class="mt-8 text-center">
            <Link
                :href="route('games.index')"
                class="inline-flex items-center text-sm font-medium text-primary hover:text-primary/80 transition-colors"
            >
                <svg
                    class="mr-2 w-4 h-4"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M15 19l-7-7 7-7"
                    />
                </svg>
                Quay lại danh sách game
            </Link>
        </div>
    </div>
</template>

<script setup lang="ts">
import AdWidget from '@/components/AdWidget.vue'
import ShoppingBagIcon from '@/components/Icons/ShoppingBagIcon.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import AccountGallery from '@/Pages/Accounts/Partials/AccountGallery.vue'
import PurchaseModal from '@/Pages/Accounts/Partials/PurchaseModal.vue'
import RelatedAccounts from '@/Pages/Accounts/Partials/RelatedAccounts.vue'
import { Account } from '@/types'
import '@fancyapps/ui/dist/fancybox/fancybox.css'
import { Head } from '@inertiajs/vue3'
import { ref } from 'vue'
import AccountDetail from './Partials/AccountDetail.vue'

defineProps<{
    account: Account
    relatedAccounts: Account[]
}>()

const isPurchaseModalOpen = ref(false)
</script>

<template>
    <Head :title="`Tài khoản ${account.category.name} #${account.id}`" />

    <div class="container py-4">
        <section>
            <div class="grid grid-cols-1 lg:grid-cols-12 gap-2 sm:gap-4">
                <div class="col-span-1 lg:col-span-7">
                    <AccountGallery
                        :images="account.images"
                        :alt="`Tài khoản ${account.category.name} #${account.id}`"
                    />
                </div>

                <div
                    class="flex flex-col-reverse sm:flex-col gap-2 sm:gap-4 col-span-1 lg:col-span-5"
                >
                    <AccountDetail :account="account" />

                    <Button
                        type="button"
                        size="lg"
                        class="w-full"
                        @click="isPurchaseModalOpen = true"
                        :disabled="account.status !== 'selling'"
                    >
                        <ShoppingBagIcon class="size-5 me-2" />
                        <span
                            v-text="
                                account.status === 'selling'
                                    ? 'Mua ngay'
                                    : 'Đã bán'
                            "
                        />
                    </Button>
                </div>
            </div>

            <Card v-if="account.content" class="mt-4">
                <CardContent class="pt-6">
                    <div
                        class="prose max-w-full dark:text-white"
                        v-html="account.content"
                    />
                </CardContent>
            </Card>
        </section>

        <RelatedAccounts :accounts="relatedAccounts" />

        <AdWidget position="account_detail" />
    </div>

    <PurchaseModal v-model="isPurchaseModalOpen" :account="account" />
</template>

<script setup lang="ts">
import Countdown from '@/components/Countdown.vue'
import BoltIcon from '@/components/Icons/BoltIcon.vue'
import { Card } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { Account, FlashSale } from '@/types'
import { Link, usePage } from '@inertiajs/vue3'
import { computed } from 'vue'

defineProps<{
    account: Account
}>()

const flashSale = usePage().props.flashSale as FlashSale | null

const time = computed(() => {
    if (!flashSale) {
        return 0
    }

    return Number(new Date(flashSale.end_at)) - Number(new Date())
})
</script>

<template>
    <Card class="overflow-auto">
        <div class="p-4 border-b dark:border-gray-800">
            <h2 class="font-semibold text-lg sm:mb-2">
                <Link :href="route('categories.show', account.category.slug)">
                    <PERSON><PERSON><PERSON> <PERSON> {{ account.category.name }}
                </Link>
            </h2>
            <div class="font-semibold">M<PERSON> số #{{ account.id }}</div>
        </div>

        <div>
            <div v-if="flashSale" class="p-4 pb-0">
                <div
                    class="flex flex-wrap gap-3 justify-between border-2 border-primary text-primary rounded-lg p-3"
                >
                    <div class="flex items-center gap-1.5">
                        <BoltIcon class="size-6 animate-pulse" />
                        <h5 class="font-semibold">Flash Sale</h5>
                    </div>
                    <Countdown
                        :time="time"
                        v-slot="{ days, hours, minutes, seconds }"
                    >
                        <div class="flex items-center gap-1">
                            <span
                                class="font-bold bg-primary text-primary-foreground rounded-md px-1"
                            >
                                {{ days }}
                            </span>
                            <span class="font-bold">:</span>
                            <span
                                class="font-bold bg-primary text-primary-foreground rounded-md px-1"
                            >
                                {{ hours }}
                            </span>
                            <span class="font-bold">:</span>
                            <span
                                class="font-bold bg-primary text-primary-foreground rounded-md px-1"
                            >
                                {{ minutes }}
                            </span>
                            <span class="font-bold">:</span>
                            <span
                                class="font-bold bg-primary text-primary-foreground rounded-md px-1"
                            >
                                {{ seconds }}
                            </span>
                        </div>
                    </Countdown>
                </div>
            </div>
            <h3 class="font-semibold p-4 pb-0 mb-2">Thông tin tài khoản</h3>
            <dl>
                <div
                    v-for="(attribute, index) in account.attributes"
                    :key="index"
                    class="flex justify-between odd:bg-muted px-4 py-3 text-sm"
                >
                    <dt>
                        {{ attribute.attribute_set.name }}
                    </dt>
                    <dd class="font-medium">
                        {{ attribute.name }}
                    </dd>
                </div>
            </dl>
        </div>

        <div class="p-4">
            <div
                class="shadow-xs rounded-lg p-4 flex justify-between items-center bg-muted border border-border"
            >
                <span class="font-semibold">Giá tiền</span>
                <div class="flex items-center gap-2">
                    <div
                        v-if="account.original_price > account.price"
                        class="text-gray-500 text-sm line-through dark:text-gray-400"
                    >
                        {{ formatCurrency(account.original_price) }}
                    </div>
                    <div class="font-semibold text-lg text-primary">
                        {{ formatCurrency(account.price) }}
                    </div>
                </div>
            </div>
        </div>
    </Card>
</template>

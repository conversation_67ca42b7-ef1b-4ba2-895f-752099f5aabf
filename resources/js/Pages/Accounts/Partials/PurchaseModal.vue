<script setup lang="ts">
import { DescriptionItem, DescriptionList } from '@/components'
import InputError from '@/components/Forms/InputError.vue'
import { Button } from '@/components/ui/button'
import {
    <PERSON><PERSON>,
    DialogContent,
    <PERSON><PERSON>Footer,
    <PERSON>alogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { <PERSON>bs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useToast } from '@/components/ui/toast'
import { formatCurrency } from '@/lib/utils'
import { Account } from '@/types'
import { Link, useForm, usePage } from '@inertiajs/vue3'
import { computed, ref } from 'vue'

const model = defineModel<boolean>()

const props = defineProps<{
    account: Account
}>()

const user = computed(() => usePage().props.auth.user)
const showDiscountForm = ref(false)
const applyDiscountError = ref('')
const applyDiscountLoading = ref(false)
const discountAmount = ref(null)
const totalAmount = ref(props.account.price)
const purchaseForm = useForm({
    discount_code: '',
})
const { toast } = useToast()

const purchase = () => {
    purchaseForm.post(route('accounts.purchase', { id: props.account.id }), {
        preserveState: true,
        preserveScroll: true,
        method: 'post',
        except: ['relatedAccounts'],
        onSuccess: () => {
            const { error, success } = usePage().props.flash

            if (error) {
                toast({
                    title: 'Thất bại',
                    description: error,
                    variant: 'destructive',
                })

                model.value = false

                return
            }

            if (success) {
                toast({
                    title: 'Thành công',
                    description: success,
                    variant: 'success',
                })
                purchaseForm.reset()
            }
        },
    })
}

const applyDiscount = async () => {
    applyDiscountLoading.value = true

    const response = await fetch(
        route('accounts.apply-discount', props.account.id),
        {
            method: 'POST',
            body: JSON.stringify({ code: purchaseForm.discount_code }),
            headers: {
                'X-CSRF-TOKEN': usePage().props.app.csrf_token,
                'Content-Type': 'application/json',
            },
        },
    )

    const data = await response.json()

    if (response.ok) {
        showDiscountForm.value = false
        discountAmount.value = data.data.discount_amount
        totalAmount.value = data.data.total_amount
        applyDiscountError.value = ''
    } else {
        applyDiscountError.value = data.code?.join(', ') || data.message
    }

    applyDiscountLoading.value = false
}

const removeDiscount = () => {
    purchaseForm.discount_code = ''
    discountAmount.value = null
    totalAmount.value = props.account.price
    showDiscountForm.value = true
}
</script>

<template>
    <Dialog v-model:open="model">
        <DialogContent>
            <DialogHeader>
                <DialogTitle>
                    Xác nhận mua tài khoản #{{ account.id }}
                </DialogTitle>
            </DialogHeader>

            <Tabs :default-value="0">
                <TabsList class="grid w-full grid-cols-2">
                    <TabsTrigger value="purchase"> Thanh toán </TabsTrigger>
                    <TabsTrigger value="account">
                        Thông tin tài khoản
                    </TabsTrigger>
                </TabsList>
                <TabsContent value="purchase" class="my-4">
                    <DescriptionList>
                        <DescriptionItem
                            label="Danh mục"
                            :value="account.category.name"
                            :inline="true"
                        />
                        <DescriptionItem label="Giá tiền" :inline="true">
                            <template v-slot:value>
                                <div class="grow">
                                    <span class="font-medium">{{
                                        formatCurrency(account.price)
                                    }}</span>
                                    <span
                                        class="text-sm text-gray-500 dark:text-gray-400 line-through ms-2"
                                        v-if="
                                            account.original_price >
                                            account.price
                                        "
                                    >
                                        {{
                                            formatCurrency(
                                                account.original_price,
                                            )
                                        }}
                                    </span>
                                </div>
                            </template>
                        </DescriptionItem>
                        <div
                            class="text-sm first:pt-0 last:pb-0 py-4 sm:gap-4 items-center grid grid-cols-3"
                        >
                            <dt class="font-medium leading-6">
                                Giảm giá
                                <button
                                    @click="
                                        purchaseForm.discount_code &&
                                        discountAmount
                                            ? removeDiscount()
                                            : (showDiscountForm =
                                                  !showDiscountForm)
                                    "
                                    type="button"
                                    class="text-primary underline text-xs"
                                    v-text="
                                        purchaseForm.discount_code &&
                                        discountAmount
                                            ? 'Xóa mã giảm giá'
                                            : 'Áp dụng mã giảm giá'
                                    "
                                />
                            </dt>
                            <dd class="mt-1 flex leading-6 sm:mt-0 col-span-2">
                                <div class="grow flex items-center gap-2">
                                    <span>{{
                                        formatCurrency(discountAmount || 0)
                                    }}</span>
                                    <div
                                        class="text-sm text-gray-500 dark:text-gray-400"
                                        v-if="
                                            purchaseForm.discount_code &&
                                            discountAmount
                                        "
                                    >
                                        ({{ purchaseForm.discount_code }})
                                    </div>
                                </div>
                            </dd>
                            <div class="col-span-3" v-if="showDiscountForm">
                                <div class="flex w-full items-center gap-1.5">
                                    <Input
                                        type="text"
                                        placeholder="Mã giảm giá"
                                        v-model="purchaseForm.discount_code"
                                        :disabled="applyDiscountLoading"
                                    />
                                    <Button
                                        type="submit"
                                        variant="outline"
                                        @click="applyDiscount"
                                        :loading="applyDiscountLoading"
                                        :overlay="true"
                                    >
                                        Áp dụng
                                    </Button>
                                </div>
                                <InputError
                                    :message="applyDiscountError"
                                    class="mt-2"
                                />
                            </div>
                        </div>
                        <DescriptionItem
                            label="Tổng thanh toán"
                            :value="formatCurrency(totalAmount)"
                            :inline="true"
                        />
                    </DescriptionList>
                </TabsContent>
                <TabsContent value="account" class="my-4">
                    <DescriptionList>
                        <DescriptionItem
                            v-for="(attribute, index) in account.attributes"
                            :key="index"
                            :label="attribute.attribute_set.name"
                            :value="attribute.name"
                            :inline="true"
                        />
                    </DescriptionList>
                </TabsContent>
            </Tabs>

            <DialogFooter class="grid grid-cols-2 gap-2">
                <Button type="button" variant="outline" @click="model = false">
                    Hủy bỏ
                </Button>

                <template v-if="user">
                    <Button
                        type="button"
                        v-if="user.balance >= account.price"
                        @click="purchase"
                        :loading="purchaseForm.processing"
                    >
                        Mua ngay
                    </Button>
                    <Link v-else as-child :href="route('user.deposit.index')">
                        <Button class="w-full"> Nạp tiền </Button>
                    </Link>
                </template>

                <Link v-else :href="route('login')" as-child>
                    <Button class="w-full"> Đăng nhập </Button>
                </Link>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

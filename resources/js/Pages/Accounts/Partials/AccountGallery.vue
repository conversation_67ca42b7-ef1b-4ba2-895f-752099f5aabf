<script setup lang="ts">
import { Card, CardContent } from '@/components/ui/card'
import {
    Carousel,
    CarouselApi,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel'
import { Fancybox } from '@fancyapps/ui'
import { watchOnce } from '@vueuse/core'
import { onMounted, ref } from 'vue'

defineProps<{
    images: string[]
    alt: string
}>()

const emblaMainApi = ref<CarouselApi>()
const emblaThumbnailApi = ref<CarouselApi>()
const selectedIndex = ref(0)

function onSelect() {
    if (!emblaMainApi.value || !emblaThumbnailApi.value) return
    selectedIndex.value = emblaMainApi.value.selectedScrollSnap()
    emblaThumbnailApi.value.scrollTo(emblaMainApi.value.selectedScrollSnap())
}

function onThumbClick(index: number) {
    if (!emblaMainApi.value) return
    emblaMainApi.value.scrollTo(index)
}

watchOnce(emblaMainApi, (emblaMainApi) => {
    if (!emblaMainApi) return

    onSelect()
    emblaMainApi.on('select', onSelect)
    emblaMainApi.on('reInit', onSelect)
})

onMounted(() => {
    Fancybox.bind('[data-fancybox="gallery"]', {
        Thumbs: {
            type: 'classic',
        },
    })
})
</script>

<template>
    <Card class="overflow-hidden">
        <Carousel @init-api="(val) => (emblaMainApi = val)">
            <CarouselContent>
                <CarouselItem v-for="(image, index) in images" :key="index">
                    <a
                        :data-src="image"
                        data-fancybox="gallery"
                        :data-caption="alt"
                    >
                        <img
                            v-lazy="image"
                            :alt="alt"
                            class="w-full aspect-video object-fill rounded-lg"
                        />
                    </a>
                </CarouselItem>
            </CarouselContent>
        </Carousel>

        <CardContent>
            <Carousel
                @init-api="(val) => (emblaThumbnailApi = val)"
                class="mt-4"
            >
                <CarouselContent class="justify-center gap-2">
                    <CarouselItem
                        v-for="(image, index) in images"
                        :key="index"
                        class="ps-0 basis-1/4 sm:basis-1/5 cursor-pointer"
                        @click="onThumbClick(index)"
                    >
                        <div
                            :class="index === selectedIndex ? '' : 'opacity-50'"
                        >
                            <img
                                v-lazy="image"
                                :alt="alt"
                                class="w-full aspect-video object-fill rounded-lg"
                            />
                        </div>
                    </CarouselItem>
                </CarouselContent>
            </Carousel>
        </CardContent>
    </Card>
</template>

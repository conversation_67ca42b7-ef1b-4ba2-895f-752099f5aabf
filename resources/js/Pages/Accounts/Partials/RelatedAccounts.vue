<script setup lang="ts">
import SectionHeading from '@/components/SectionHeading.vue'
import AccountItem from '@/Pages/AccountCategories/Partials/AccountItem.vue'
import { Account } from '@/types'

defineProps<{
    accounts: Account[]
}>()
</script>

<template>
    <section class="mt-12" v-if="accounts.length > 0">
        <SectionHeading heading="Tài khoản liên quan" />

        <div
            class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4"
        >
            <AccountItem
                v-for="account in accounts"
                :key="account.id"
                :category="account.category"
                :account="account"
            />
        </div>
    </section>
</template>

<script setup lang="ts">
import FlashSaleAccountItem from '@/components/FlashSale/FlashSaleAccountItem.vue'
import SectionHeading from '@/components/SectionHeading.vue'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import type { FlashSale } from '@/types'
import { Head } from '@inertiajs/vue3'
import { Calendar, Clock, Tag } from 'lucide-vue-next'

interface Props {
    flashSale: FlashSale
}

defineProps<Props>()

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    })
}

const getStatusBadge = (flashSale: FlashSale) => {
    const now = new Date()
    const startAt = new Date(flashSale.start_at)
    const endAt = new Date(flashSale.end_at)

    if (now < startAt) {
        return { text: 'Sắp diễn ra', variant: 'secondary' as const }
    } else if (now > endAt) {
        return { text: 'Đ<PERSON> kết thúc', variant: 'destructive' as const }
    } else {
        const hoursLeft = Math.ceil(
            (endAt.getTime() - now.getTime()) / (1000 * 60 * 60),
        )
        if (hoursLeft <= 24) {
            return {
                text: `Còn ${hoursLeft}h`,
                variant: 'destructive' as const,
            }
        }
        return { text: 'Đang diễn ra', variant: 'default' as const }
    }
}

const getTimeRemaining = (endAt: string) => {
    const now = new Date()
    const end = new Date(endAt)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'Đã kết thúc'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
        return `${days} ngày ${hours} giờ`
    } else if (hours > 0) {
        return `${hours} giờ ${minutes} phút`
    } else {
        return `${minutes} phút`
    }
}
</script>

<template>
    <Head :title="flashSale.name" />

    <div class="container py-6">
        <div class="mb-8">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <h1
                        class="text-3xl font-bold text-gray-900 dark:text-white"
                    >
                        {{ flashSale.name }}
                    </h1>
                    <p
                        v-if="flashSale.description"
                        class="mt-2 text-gray-600 dark:text-gray-400"
                    >
                        {{ flashSale.description }}
                    </p>
                </div>
                <Badge
                    :variant="getStatusBadge(flashSale).variant"
                    class="text-sm"
                >
                    {{ getStatusBadge(flashSale).text }}
                </Badge>
            </div>

            <div class="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card>
                    <CardContent class="p-4">
                        <div
                            class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
                        >
                            <Calendar class="h-4 w-4" />
                            <span>Bắt đầu</span>
                        </div>
                        <p class="mt-1 font-medium">
                            {{ formatDate(flashSale.start_at) }}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent class="p-4">
                        <div
                            class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
                        >
                            <Clock class="h-4 w-4" />
                            <span>Kết thúc</span>
                        </div>
                        <p class="mt-1 font-medium">
                            {{ formatDate(flashSale.end_at) }}
                        </p>
                    </CardContent>
                </Card>

                <Card>
                    <CardContent class="p-4">
                        <div
                            class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
                        >
                            <Clock class="h-4 w-4" />
                            <span>Thời gian còn lại</span>
                        </div>
                        <p class="mt-1 font-medium text-primary">
                            {{ getTimeRemaining(flashSale.end_at) }}
                        </p>
                    </CardContent>
                </Card>
            </div>
        </div>

        <div>
            <SectionHeading heading="Sản phẩm trong Flash Sale" class="mb-6" />

            <div
                v-if="flashSale.accounts && flashSale.accounts.length > 0"
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6"
            >
                <FlashSaleAccountItem
                    v-for="item in flashSale.accounts"
                    :key="item.account.id"
                    :item="item"
                    :compact="false"
                />
            </div>

            <div v-else class="text-center py-12">
                <div class="mx-auto max-w-md">
                    <Tag class="mx-auto h-12 w-12 text-gray-400" />
                    <h3
                        class="mt-4 text-lg font-medium text-gray-900 dark:text-white"
                    >
                        Không có sản phẩm nào
                    </h3>
                    <p class="mt-2 text-gray-600 dark:text-gray-400">
                        Flash sale này hiện tại không có sản phẩm nào.
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>

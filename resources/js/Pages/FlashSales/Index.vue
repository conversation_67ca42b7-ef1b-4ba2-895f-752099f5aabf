<script setup lang="ts">
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import type { FlashSale, LengthAwarePaginator } from '@/types'
import { Head, Link, router } from '@inertiajs/vue3'
import { ArrowRight, Calendar, Clock, Tag } from 'lucide-vue-next'
import { ref } from 'vue'

interface Props {
    flashSales: LengthAwarePaginator<FlashSale>
    filters: {
        filter: string
    }
}

const props = defineProps<Props>()

const currentFilter = ref(props.filters.filter)

const filterOptions = [
    { value: 'all', label: 'Tất cả' },
    { value: 'available', label: 'Đang diễn ra' },
    { value: 'ending_soon', label: '<PERSON>ắ<PERSON> hết hạn' },
    { value: 'upcoming', label: 'Sắp diễn ra' },
]

const handleFilterChange = (value: string) => {
    currentFilter.value = value
    router.get(
        route('flash-sales.index'),
        { filter: value },
        {
            preserveState: true,
            preserveScroll: true,
        },
    )
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    })
}

const getStatusBadge = (flashSale: FlashSale) => {
    const now = new Date()
    const startAt = new Date(flashSale.start_at)
    const endAt = new Date(flashSale.end_at)

    if (now < startAt) {
        return { text: 'Sắp diễn ra', variant: 'secondary' as const }
    } else if (now > endAt) {
        return { text: 'Đã kết thúc', variant: 'destructive' as const }
    } else {
        const hoursLeft = Math.ceil(
            (endAt.getTime() - now.getTime()) / (1000 * 60 * 60),
        )
        if (hoursLeft <= 24) {
            return {
                text: `Còn ${hoursLeft}h`,
                variant: 'destructive' as const,
            }
        }
        return { text: 'Đang diễn ra', variant: 'default' as const }
    }
}

const getTimeRemaining = (endAt: string) => {
    const now = new Date()
    const end = new Date(endAt)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'Đã kết thúc'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
        return `${days} ngày ${hours} giờ`
    } else if (hours > 0) {
        return `${hours} giờ ${minutes} phút`
    } else {
        return `${minutes} phút`
    }
}
</script>

<template>
    <Head title="Flash Sale" />

    <div class="container py-6">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                Flash Sale
            </h1>
            <p class="mt-2 text-gray-600 dark:text-gray-400">
                Khám phá các chương trình flash sale hấp dẫn với giá ưu đãi đặc
                biệt
            </p>
        </div>

        <!-- Filter -->
        <div class="mb-6">
            <div class="flex items-center gap-4">
                <span
                    class="text-sm font-medium text-gray-700 dark:text-gray-300"
                    >Lọc theo:</span
                >
                <Select
                    :model-value="currentFilter"
                    @update:model-value="handleFilterChange"
                >
                    <SelectTrigger class="w-48">
                        <SelectValue placeholder="Chọn trạng thái" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem
                            v-for="option in filterOptions"
                            :key="option.value"
                            :value="option.value"
                        >
                            {{ option.label }}
                        </SelectItem>
                    </SelectContent>
                </Select>
            </div>
        </div>

        <!-- Flash Sales Grid -->
        <div
            v-if="flashSales.data.length > 0"
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
        >
            <Card
                v-for="flashSale in flashSales.data"
                :key="flashSale.id"
                class="overflow-hidden hover:shadow-lg transition-shadow"
            >
                <CardHeader class="pb-3">
                    <div class="flex items-start justify-between">
                        <div class="flex-1">
                            <CardTitle class="text-lg line-clamp-2">{{
                                flashSale.name
                            }}</CardTitle>
                            <CardDescription
                                v-if="flashSale.description"
                                class="mt-1 line-clamp-2"
                            >
                                {{ flashSale.description }}
                            </CardDescription>
                        </div>
                        <Badge
                            :variant="getStatusBadge(flashSale).variant"
                            class="ml-2"
                        >
                            {{ getStatusBadge(flashSale).text }}
                        </Badge>
                    </div>
                </CardHeader>

                <CardContent class="space-y-4">
                    <!-- Thời gian -->
                    <div class="space-y-2 text-sm">
                        <div
                            class="flex items-center gap-2 text-gray-600 dark:text-gray-400"
                        >
                            <Calendar class="h-4 w-4" />
                            <span
                                >Bắt đầu:
                                {{ formatDate(flashSale.start_at) }}</span
                            >
                        </div>
                        <div
                            class="flex items-center gap-2 text-gray-600 dark:text-gray-400"
                        >
                            <Clock class="h-4 w-4" />
                            <span
                                >Kết thúc:
                                {{ formatDate(flashSale.end_at) }}</span
                            >
                        </div>
                        <div
                            class="flex items-center gap-2 text-primary font-medium"
                        >
                            <Clock class="h-4 w-4" />
                            <span>{{
                                getTimeRemaining(flashSale.end_at)
                            }}</span>
                        </div>
                    </div>

                    <!-- Số lượng sản phẩm -->
                    <div
                        class="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400"
                    >
                        <Tag class="h-4 w-4" />
                        <span
                            >{{ flashSale.accounts?.length || 0 }} sản
                            phẩm</span
                        >
                    </div>

                    <!-- Action Button -->
                    <Link
                        :href="route('flash-sales.show', flashSale.id)"
                        class="w-full"
                    >
                        <Button class="w-full">
                            Xem chi tiết
                            <ArrowRight class="ml-2 h-4 w-4" />
                        </Button>
                    </Link>
                </CardContent>
            </Card>
        </div>

        <!-- Empty State -->
        <div v-else class="text-center py-12">
            <div class="mx-auto max-w-md">
                <Tag class="mx-auto h-12 w-12 text-gray-400" />
                <h3
                    class="mt-4 text-lg font-medium text-gray-900 dark:text-white"
                >
                    Không có flash sale nào
                </h3>
                <p class="mt-2 text-gray-600 dark:text-gray-400">
                    Hiện tại không có flash sale nào phù hợp với bộ lọc của bạn.
                </p>
            </div>
        </div>

        <!-- Pagination -->
        <div
            v-if="flashSales.data.length > 0 && flashSales.meta.last_page > 1"
            class="mt-8"
        >
            <!-- Add pagination component here if needed -->
        </div>
    </div>
</template>

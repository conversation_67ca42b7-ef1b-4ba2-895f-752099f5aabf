<script setup lang="ts">
import SectionHeading from '@/components/SectionHeading.vue'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import type { FlashSale, LengthAwarePaginator } from '@/types'
import { Head, Link, router } from '@inertiajs/vue3'
import { Calendar, Clock, Filter, Tag, Zap } from 'lucide-vue-next'
import { ref } from 'vue'

interface Props {
    flashSales: LengthAwarePaginator<FlashSale>
    filters: {
        filter: string
    }
}

const props = defineProps<Props>()

const currentFilter = ref(props.filters.filter)

const filterOptions = [
    { value: 'all', label: 'Tất cả', icon: 'all' },
    { value: 'available', label: 'Đang diễn ra', icon: 'active' },
    { value: 'ending_soon', label: '<PERSON>ắ<PERSON> hết hạn', icon: 'urgent' },
    { value: 'upcoming', label: 'Sắp diễn ra', icon: 'upcoming' },
]

const handleFilterChange = (value: string) => {
    currentFilter.value = value
    router.get(
        route('flash-sales.index'),
        { filter: value },
        {
            preserveState: true,
            preserveScroll: true,
        },
    )
}

const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('vi-VN', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit',
    })
}

const getStatusBadge = (flashSale: FlashSale) => {
    const now = new Date()
    const startAt = new Date(flashSale.start_at)
    const endAt = new Date(flashSale.end_at)

    if (now < startAt) {
        return { text: 'Sắp diễn ra', variant: 'secondary' as const }
    } else if (now > endAt) {
        return { text: 'Đã kết thúc', variant: 'destructive' as const }
    } else {
        const hoursLeft = Math.ceil(
            (endAt.getTime() - now.getTime()) / (1000 * 60 * 60),
        )
        if (hoursLeft <= 24) {
            return {
                text: `Còn ${hoursLeft}h`,
                variant: 'destructive' as const,
            }
        }
        return { text: 'Đang diễn ra', variant: 'default' as const }
    }
}

const getTimeRemaining = (endAt: string) => {
    const now = new Date()
    const end = new Date(endAt)
    const diff = end.getTime() - now.getTime()

    if (diff <= 0) return 'Đã kết thúc'

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))

    if (days > 0) {
        return `${days} ngày ${hours} giờ`
    } else if (hours > 0) {
        return `${hours} giờ ${minutes} phút`
    } else {
        return `${minutes} phút`
    }
}

const getFilterIcon = (filterValue: string) => {
    switch (filterValue) {
        case 'available':
            return '🔥'
        case 'ending_soon':
            return '⏰'
        case 'upcoming':
            return '📅'
        default:
            return '✨'
    }
}
</script>

<template>
    <Head title="Flash Sale" />

    <Master>
        <div class="container py-6">
            <div class="text-center mb-8">
                <SectionHeading heading="Flash Sale" class="mb-4" />
                <p class="text-muted-foreground text-lg max-w-2xl mx-auto">
                    Khám phá những ưu đãi đặc biệt với thời gian giới hạn. Nhanh
                    tay sở hữu những tài khoản game chất lượng với giá tốt nhất!
                </p>
            </div>

            <div class="mb-8">
                <div class="flex flex-wrap gap-2">
                    <Button
                        v-for="option in filterOptions"
                        :key="option.value"
                        :variant="
                            currentFilter === option.value
                                ? 'default'
                                : 'outline'
                        "
                        class="gap-2"
                        @click="handleFilterChange(option.value)"
                    >
                        <span class="text-lg">{{
                            getFilterIcon(option.value)
                        }}</span>
                        {{ option.label }}
                    </Button>
                </div>
            </div>

            <div
                v-if="flashSales.data.length > 0"
                class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
                <Card
                    v-for="flashSale in flashSales.data"
                    :key="flashSale.id"
                    class="overflow-hidden border"
                >
                    <CardHeader
                        class="pb-4 bg-gradient-to-r from-primary/10 to-primary/5 border-b"
                    >
                        <div class="flex items-start justify-between">
                            <div class="flex-1 min-w-0">
                                <CardTitle
                                    class="text-lg font-bold line-clamp-2 text-foreground"
                                >
                                    {{ flashSale.name }}
                                </CardTitle>
                                <CardDescription
                                    v-if="flashSale.description"
                                    class="mt-2 line-clamp-2 text-muted-foreground"
                                >
                                    {{ flashSale.description }}
                                </CardDescription>
                            </div>
                            <Badge
                                :variant="getStatusBadge(flashSale).variant"
                                class="ml-3 flex-shrink-0"
                            >
                                {{ getStatusBadge(flashSale).text }}
                            </Badge>
                        </div>
                    </CardHeader>

                    <CardContent class="p-6 space-y-5">
                        <div class="space-y-3">
                            <div class="flex items-center gap-3 text-sm">
                                <div
                                    class="flex items-center gap-2 text-muted-foreground"
                                >
                                    <Calendar class="h-4 w-4" />
                                    <span>Bắt đầu</span>
                                </div>
                                <span class="font-medium text-foreground">
                                    {{ formatDate(flashSale.start_at) }}
                                </span>
                            </div>
                            <div class="flex items-center gap-3 text-sm">
                                <div
                                    class="flex items-center gap-2 text-muted-foreground"
                                >
                                    <Clock class="h-4 w-4" />
                                    <span>Kết thúc</span>
                                </div>
                                <span class="font-medium text-foreground">
                                    {{ formatDate(flashSale.end_at) }}
                                </span>
                            </div>
                        </div>

                        <div
                            class="bg-primary/5 rounded-lg p-4 border border-primary/10"
                        >
                            <div
                                class="flex items-center gap-2 text-primary font-semibold mb-2"
                            >
                                <Clock class="h-4 w-4" />
                                <span class="text-sm">Thời gian còn lại</span>
                            </div>
                            <div class="text-lg font-bold text-primary">
                                {{ getTimeRemaining(flashSale.end_at) }}
                            </div>
                        </div>

                        <div
                            class="flex items-center gap-2 text-sm text-muted-foreground bg-muted/50 rounded-lg p-3"
                        >
                            <Tag class="h-4 w-4" />
                            <span>
                                <span class="font-semibold text-foreground">
                                    {{ flashSale.accounts?.length || 0 }}
                                </span>
                                sản phẩm đang bán
                            </span>
                        </div>

                        <Button as-child>
                            <Link
                                :href="route('flash-sales.show', flashSale.id)"
                                class="w-full"
                            >
                                Xem chi tiết
                            </Link>
                        </Button>
                    </CardContent>
                </Card>
            </div>

            <div v-else class="text-center py-16">
                <div class="mx-auto max-w-md">
                    <div
                        class="mx-auto w-20 h-20 bg-muted rounded-full flex items-center justify-center mb-6"
                    >
                        <Zap class="h-10 w-10 text-muted-foreground" />
                    </div>
                    <h3 class="text-xl font-semibold text-foreground mb-3">
                        Không có flash sale nào
                    </h3>
                    <p class="text-muted-foreground mb-6">
                        Hiện tại không có flash sale nào phù hợp với bộ lọc của
                        bạn. Hãy thử thay đổi bộ lọc hoặc quay lại sau.
                    </p>
                    <Button
                        variant="outline"
                        @click="handleFilterChange('all')"
                        class="gap-2"
                    >
                        <Filter class="h-4 w-4" />
                        Xem tất cả
                    </Button>
                </div>
            </div>

            <div
                v-if="
                    flashSales.data.length > 0 && flashSales.meta.last_page > 1
                "
                class="mt-12 flex justify-center"
            ></div>
        </div>
    </Master>
</template>

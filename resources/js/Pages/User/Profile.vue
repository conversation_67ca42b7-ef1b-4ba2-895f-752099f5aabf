<script setup lang="ts">
import { DescriptionItem, DescriptionList } from '@/components'
import AdWidget from '@/components/AdWidget.vue'
import Pagination from '@/components/CustomPagination.vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { LengthAwarePaginator, User } from '@/types'
import { Head, usePage } from '@inertiajs/vue3'
import { computed } from 'vue'

defineProps<{
    authenticationLog: LengthAwarePaginator<{
        ip_address: string
        browser: string
        location: string
        login_at: string
    }>
}>()
const user = computed(() => usePage().props.auth.user as User)
</script>

<template>
    <Head title="Thông tin tài khoản" />

    <Card class="mb-4">
        <CardHeader>
            <CardTitle class="text-lg">Thông tin tài khoản</CardTitle>
        </CardHeader>

        <CardContent>
            <DescriptionList>
                <DescriptionItem label="Họ và tên" :value="user.name" />
                <DescriptionItem label="Tên tài khoản" :value="user.username" />
                <DescriptionItem label="Email" :value="user.email" />
                <DescriptionItem
                    label="Số dư"
                    :value="user.formatted_balance"
                />
                <DescriptionItem label="Ngày tạo" :value="user.created_at" />

                <template v-if="user.game_items">
                    <DescriptionItem
                        v-for="item in user.game_items"
                        :key="item.id"
                        :label="item.name"
                        :value="item.formatted_quantity"
                    />
                </template>
            </DescriptionList>
        </CardContent>
    </Card>

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Nhật ký hoạt động</CardTitle>
        </CardHeader>

        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>IP</TableHead>
                    <TableHead>Trình duyệt</TableHead>
                    <TableHead>Vị trí</TableHead>
                    <TableHead>Đăng nhập lúc</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <template v-if="authenticationLog.data.length > 0">
                    <TableRow
                        v-for="(log, index) in authenticationLog.data"
                        :key="index"
                    >
                        <TableCell>{{ log.ip_address }}</TableCell>
                        <TableCell>{{ log.browser }}</TableCell>
                        <TableCell>{{ log.location }}</TableCell>
                        <TableCell>{{ log.login_at }}</TableCell>
                    </TableRow>
                </template>
                <TableRow v-else>
                    <TableCell colspan="4" className="h-16 text-center">
                        Không có dữ liệu
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </Card>

    <Pagination
        class="mt-4"
        v-if="authenticationLog.meta.total > 0"
        :meta="authenticationLog.meta"
    />

    <!-- User Profile Widget -->
    <AdWidget position="user_profile" />
</template>

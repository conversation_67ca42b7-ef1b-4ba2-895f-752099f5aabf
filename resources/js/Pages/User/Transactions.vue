<script setup lang="ts">
import {
    FilterForm,
    FilterInput,
    FilterSelect,
    FilterWrapper,
} from '@/components'
import AdWidget from '@/components/AdWidget.vue'
import Pagination from '@/components/CustomPagination.vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { SelectGroup, SelectItem } from '@/components/ui/select'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { LengthAwarePaginator, Transaction } from '@/types'
import { Head, router, useForm } from '@inertiajs/vue3'
import { pickBy, throttle } from 'lodash'
import { computed, watch } from 'vue'

const props = defineProps<{
    transactions: LengthAwarePaginator<Transaction>
    transactionTypes: Record<string, string>
    filters: Record<string, string>
}>()

const form = useForm<{
    type: string
    from_date: string
    to_date: string
}>({
    type: props.filters.type || '',
    from_date: props.filters.from_date || '',
    to_date: props.filters.to_date || '',
})

watch(
    () => form.data(),
    throttle(() => {
        form.transform(() => pickBy(form.data())).get(
            route('user.transactions'),
            {
                preserveState: true,
                only: ['transactions'],
            },
        )
    }),
    { deep: true },
)

const isFiltering = computed(() => {
    const data = form.data()

    return data.type !== '' || data.from_date !== '' || data.to_date !== ''
})
</script>

<template>
    <Head title="Lịch sử giao dịch" />

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Lịch sử giao dịch</CardTitle>
        </CardHeader>

        <CardContent>
            <FilterForm
                :isFiltering
                @reset="router.get(route('user.transactions'))"
            >
                <FilterWrapper label="Giao dịch">
                    <FilterSelect
                        v-model="form.type"
                        placeholder="Chọn loại giao dịch"
                    >
                        <SelectGroup>
                            <SelectItem
                                v-for="(value, key) in transactionTypes"
                                :key="key"
                                :value="key"
                            >
                                {{ value }}
                            </SelectItem>
                        </SelectGroup>
                    </FilterSelect>
                </FilterWrapper>
                <FilterWrapper label="Đến ngày">
                    <FilterInput type="date" v-model="form.from_date" />
                </FilterWrapper>
                <FilterWrapper label="Từ ngày">
                    <FilterInput type="date" v-model="form.to_date" />
                </FilterWrapper>
            </FilterForm>
        </CardContent>

        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>Giao dịch</TableHead>
                    <TableHead>Số tiền</TableHead>
                    <TableHead>Số dư cuối</TableHead>
                    <TableHead>Mô tả</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <template v-if="transactions.data.length > 0">
                    <TableRow
                        v-for="(transaction, index) in transactions.data"
                        :key="index"
                    >
                        <TableCell>{{ transaction.created_at }}</TableCell>
                        <TableCell>#{{ transaction.id }}</TableCell>
                        <TableCell>{{ transaction.type }}</TableCell>
                        <TableCell
                            :class="{
                                'text-green-600': transaction.is_positive,
                                'text-red-600': !transaction.is_positive,
                            }"
                        >
                            {{
                                (transaction.is_positive ? '+' : '-') +
                                transaction.formatted_amount
                            }}
                        </TableCell>
                        <TableCell>{{
                            transaction.formatted_balance
                        }}</TableCell>
                        <TableCell>{{ transaction.description }}</TableCell>
                    </TableRow>
                </template>
                <TableRow v-else>
                    <TableCell colspan="6" className="h-16 text-center">
                        Không có dữ liệu
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </Card>

    <Pagination
        class="mt-4"
        v-if="transactions.meta.total > 0"
        :meta="transactions.meta"
    />

    <!-- User Transactions Widget -->
    <AdWidget position="user_transactions" />
</template>

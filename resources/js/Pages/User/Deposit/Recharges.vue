<script setup lang="ts">
import { FilterForm } from '@/components'
import Pagination from '@/components/CustomPagination.vue'
import FilterInput from '@/components/Forms/Filter/FilterInput.vue'
import FilterSelect from '@/components/Forms/Filter/FilterSelect.vue'
import FilterWrapper from '@/components/Forms/Filter/FilterWrapper.vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { SelectGroup, SelectItem } from '@/components/ui/select'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { LengthAwarePaginator } from '@/types'
import { Head, router, useForm } from '@inertiajs/vue3'
import { pickBy, throttle } from 'lodash'
import { computed, watch } from 'vue'

export type Recharge = {
    id: string
    type: string
    amount: string
    declared_amount: string
    serial: string
    pin: string
    created_at: string
    status: string
}

const props = defineProps<{
    recharges: LengthAwarePaginator<Recharge>
    telecoms: Record<string, string>
    amounts: Record<number, string>
    filters: Record<string, string>
}>()

const form = useForm<{
    search: string
    type: string
    amount: string
    from_date: string
    to_date: string
}>({
    search: props.filters.search || '',
    type: props.filters.type || '',
    amount: props.filters.amount || '',
    from_date: props.filters.from_date || '',
    to_date: props.filters.to_date || '',
})

watch(
    () => form.data(),
    throttle(() => {
        form.transform(() => pickBy(form.data())).get(
            route('user.deposit.recharges.index'),
            {
                preserveState: true,
                only: ['recharges', 'filters'],
            },
        )
    }),
    { deep: true },
)

const isFiltering = computed(() => {
    const data = form.data()

    return (
        data.search !== '' ||
        data.type !== '' ||
        data.amount !== '' ||
        data.from_date !== '' ||
        data.to_date !== ''
    )
})
</script>

<template>
    <Head title="Lịch sử nạp thẻ" />

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Lịch sử nạp thẻ</CardTitle>
        </CardHeader>

        <CardContent>
            <FilterForm
                :is-filtering="isFiltering"
                @reset="router.get(route('user.deposit.recharges.index'))"
            >
                <FilterWrapper label="Tìm kiếm">
                    <FilterInput
                        v-model="form.search"
                        placeholder="Tìm kiếm theo ID, số serial, ..."
                    />
                </FilterWrapper>
                <FilterWrapper label="Loại thẻ">
                    <FilterSelect
                        v-model="form.type"
                        placeholder="Chọn loại thẻ"
                    >
                        <SelectGroup>
                            <SelectItem
                                v-for="(telecom, key) in telecoms"
                                :key="key"
                                :value="key"
                            >
                                {{ telecom }}
                            </SelectItem>
                        </SelectGroup>
                    </FilterSelect>
                </FilterWrapper>
                <FilterWrapper label="Mệnh giá">
                    <FilterSelect
                        v-model="form.amount"
                        placeholder="Chọn mệnh giá"
                    >
                        <SelectGroup>
                            <SelectItem
                                v-for="(amount, index) in amounts"
                                :key="index"
                                :value="String(index)"
                            >
                                {{ amount }}
                            </SelectItem>
                        </SelectGroup>
                    </FilterSelect>
                </FilterWrapper>
                <FilterWrapper label="Đến ngày">
                    <FilterInput type="date" v-model="form.from_date" />
                </FilterWrapper>
                <FilterWrapper label="Từ ngày">
                    <FilterInput type="date" v-model="form.to_date" />
                </FilterWrapper>
            </FilterForm>
        </CardContent>

        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>Loại thẻ</TableHead>
                    <TableHead>Số serial</TableHead>
                    <TableHead>Mệnh giá</TableHead>
                    <TableHead>Thực nhận</TableHead>
                    <TableHead>Nạp lúc</TableHead>
                    <TableHead>Trạng thái</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <template v-if="recharges.data.length > 0">
                    <TableRow
                        v-for="(recharge, index) in recharges.data"
                        :key="index"
                    >
                        <TableCell>{{ recharge.id }}</TableCell>
                        <TableCell>{{ recharge.type }}</TableCell>
                        <TableCell>{{ recharge.serial }}</TableCell>
                        <TableCell>{{ recharge.declared_amount }}</TableCell>
                        <TableCell>{{ recharge.amount }}</TableCell>
                        <TableCell>{{ recharge.created_at }}</TableCell>
                        <TableCell>{{ recharge.status }}</TableCell>
                    </TableRow>
                </template>
                <TableRow v-else>
                    <TableCell colspan="7" className="h-16 text-center">
                        Không có dữ liệu
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </Card>

    <Pagination
        class="mt-4"
        v-if="recharges.meta.total > 0"
        :meta="recharges.meta"
    />
</template>

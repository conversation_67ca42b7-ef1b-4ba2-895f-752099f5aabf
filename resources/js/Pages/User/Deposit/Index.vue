<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import QRCode from '@/Pages/User/Deposit/QRCode.vue'
import { Head } from '@inertiajs/vue3'
import type { Component } from 'vue'
import { shallowRef } from 'vue'
import RechargeForm from './Partials/RechargeForm.vue'

const props = defineProps<{
    recharge_type: string
    auto_pay_enabled: boolean
}>()

const tabs = shallowRef<
    {
        label: string
        disabled: boolean
        component?: Component
    }[]
>([
    {
        label: 'Nạp thẻ cào',
        disabled: props.recharge_type === 'off',
        component: RechargeForm,
    },
    {
        label: 'Chuyển khoản ngân hàng',
        disabled: !props.auto_pay_enabled,
        component: QRCode,
    },
])

const filterTabs = tabs.value.filter((tab) => !tab.disabled)
</script>

<template>
    <Head title="Nạp tiền" />

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Nạp tiền</CardTitle>
        </CardHeader>
        <CardContent>
            <Tabs :default-value="0">
                <TabsList class="grid w-full grid-cols-2">
                    <TabsTrigger
                        v-for="(tab, index) in filterTabs"
                        :index="index"
                        :value="index"
                        class="truncate block"
                    >
                        {{ tab.label }}
                    </TabsTrigger>
                </TabsList>
                <TabsContent
                    v-for="(tab, index) in filterTabs"
                    :key="index"
                    :value="index"
                >
                    <component :is="tab.component" />
                </TabsContent>
            </Tabs>
        </CardContent>
    </Card>
</template>

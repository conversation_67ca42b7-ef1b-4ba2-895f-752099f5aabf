<script setup lang="ts">
import CopyToClipboard from '@/components/CopyToClipboard.vue'
import InputError from '@/components/Forms/InputError.vue'
import CheckCircleIcon from '@/components/Icons/CheckCircleIcon.vue'
import Spinner from '@/components/Spinner.vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { BankAccount } from '@/types'
import { useForm, usePage } from '@inertiajs/vue3'
import J<PERSON>on<PERSON>tti from 'js-confetti'
import { ref } from 'vue'

const form = useForm<{
    amount: string
}>({
    amount: '',
})

const state = ref<'default' | 'pending' | 'success'>('default')
const successMessage = ref<string | null>(null)
const imageLoaded = ref<boolean>(false)

type Data = {
    amount: string
    description: string
    image: string
    bank_account: BankAccount
}

const data = ref<Data | null>()

const submit = async () => {
    form.post(route('user.deposit.qr-code'), {
        preserveScroll: true,
        only: ['data'],
        onSuccess: ({ props }) => {
            if (!props.errors.length) {
                state.value = 'pending'
                form.reset()
                data.value = props.data as Data
            }
        },
    })
}

// @ts-ignore
Echo.private(`App.Models.User.${usePage().props.auth?.user?.id}`).listen(
    'DepositWebhookProcessed',
    ({ deposit, message }: { deposit: object; message: string }) => {
        state.value = 'success'
        successMessage.value = message

        const jsConfetti = new JSConfetti()
        jsConfetti.addConfetti()
    },
)
</script>

<template>
    <form @submit.prevent="submit" class="space-y-6" v-if="state === 'default'">
        <div class="grid gap-2">
            <Label for="amount">Số tiền</Label>
            <Input
                type="number"
                id="amount"
                v-model="form.amount"
                placeholder="Nhập số tiền cần nạp"
                :disabled="form.processing"
                required
            />
            <small class="block text-muted-foreground">
                Số tiền nạp tối thiểu là 10,000 VND. Sau đó nhấn nút bên dưới để
                hiển thị thông tin chuyển khoản.
            </small>
            <InputError :message="form.errors.amount" />
        </div>
        <Button type="submit" class="w-full" :loading="form.processing">
            Tạo mã chuyển khoản
        </Button>
    </form>
    <template v-if="state !== 'default'">
        <div class="max-w-sm mx-auto" v-if="state === 'pending' && data">
            <div
                class="flex flex-col justify-center items-center border rounded-lg dark:border-white/10"
            >
                <div class="p-4">
                    <div class="mb-4">
                        <h3
                            class="mb-4 text-lg text-center font-semibold italic text-primary"
                        >
                            Quét mã QR để nạp tiền
                        </h3>
                        <img
                            :src="data.image"
                            :alt="data.description"
                            class="w-full h-full"
                            @load="imageLoaded = true"
                        />
                        <div
                            role="status"
                            v-if="!imageLoaded"
                            class="flex justify-center"
                        >
                            <Spinner class="size-32 text-primary" />
                            <span class="sr-only">Đang tải...</span>
                        </div>
                    </div>
                    <div class="w-full flex items-center justify-evenly gap-2">
                        <img
                            src="/images/banks/napas247.png"
                            alt="Napas 247"
                            class="h-6"
                        />
                        <img
                            src="/images/banks/vietqr.png"
                            alt="VietQR"
                            class="h-6"
                        />
                        <img
                            v-if="data.bank_account.logo"
                            :src="data.bank_account.logo"
                            :alt="data.bank_account.bank_name"
                            class="h-6"
                        />
                    </div>
                </div>
                <Separator label="Hoặc nhập thủ công" />
                <dl class="p-4 text-center w-full">
                    <div>
                        <dt class="text-gray-500 dark:text-gray-400">
                            Số tiền
                        </dt>
                        <dd class="text-lg font-bold text-primary mt-1">
                            {{ data.amount }}
                        </dd>
                    </div>
                    <div class="mt-4 space-y-2">
                        <div class="flex justify-center items-center gap-1">
                            <dt
                                class="text-sm text-gray-500 dark:text-gray-400"
                            >
                                Chủ tài khoản:
                            </dt>
                            <dd class="font-semibold text-primary">
                                {{ data.bank_account.account_holder }}
                            </dd>
                        </div>
                        <div class="flex justify-center items-center gap-1">
                            <dt
                                class="text-sm text-gray-500 dark:text-gray-400"
                            >
                                Số tài khoản:
                            </dt>
                            <dd
                                class="font-semibold text-primary flex items-center gap-1"
                            >
                                {{ data.bank_account.account_number }}
                                <CopyToClipboard
                                    :content="data.bank_account.account_number"
                                />
                            </dd>
                        </div>
                        <div class="flex justify-center items-center gap-1">
                            <dt
                                class="text-sm text-gray-500 dark:text-gray-400"
                            >
                                Ngân hàng:
                            </dt>
                            <dd class="font-semibold text-primary">
                                {{ data.bank_account.bank_name }}
                            </dd>
                        </div>
                        <div>
                            <dt
                                class="text-sm text-gray-500 dark:text-gray-400 mb-2"
                            >
                                Nội dung chuyển tiền:
                            </dt>
                            <dd
                                class="relative bg-gray-100 p-2 rounded-lg flex items-center justify-center gap-1 dark:bg-gray-800"
                            >
                                <span class="font-semibold text-primary">
                                    {{ data.description }}
                                </span>
                                <CopyToClipboard
                                    :content="data.description"
                                    class="absolute top-2 end-2"
                                />
                            </dd>
                        </div>
                    </div>
                </dl>
            </div>
        </div>
        <div
            class="flex flex-col justify-center items-center"
            v-else-if="state === 'success'"
        >
            <CheckCircleIcon class="text-green-600 size-14 sm:size-20" />

            <h3
                class="my-2 text-lg sm:text-xl text-center font-bold text-green-600"
            >
                Nạp tiền thành công
            </h3>

            <p class="text-center text-gray-500 dark:text-gray-400 sm:mb-4">
                {{ successMessage }}
            </p>
        </div>

        <Button
            type="button"
            color="gray"
            class="w-full mt-4"
            @click="state = 'default'"
            v-text="state === 'pending' ? 'Quay lại' : 'Nạp tiếp'"
        />
    </template>
</template>

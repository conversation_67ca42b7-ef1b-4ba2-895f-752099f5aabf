<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { useToast } from '@/components/ui/toast'
import { cn } from '@/lib/utils'
import { useForm, usePage } from '@inertiajs/vue3'
import { computed, watch } from 'vue'

export type RechargeTelecoms = {
    name: string
    value: string
    logo: string | null
    amounts: {
        value: number
        formatted_value: string
        fee: number
    }[]
}[]

const props = usePage().props

const rechargeTelecoms = props.recharge_telecoms as RechargeTelecoms

const amounts = computed(() => {
    return rechargeTelecoms.filter((item) => item.value === form.type)[0][
        'amounts'
    ]
})

const form = useForm({
    type: '',
    amount: '',
    serial: '',
    code: '',
})

watch(
    () => form.type,
    () => form.reset('amount'),
)

const { toast } = useToast()

const submit = () => {
    form.post(route('user.deposit.recharges.store'), {
        onSuccess: () => {
            const flash = usePage().props.flash

            if (flash.error) {
                toast({
                    title: 'Thất bại',
                    description: flash.error,
                    variant: 'destructive',
                })
            }

            if (flash.success) {
                toast({
                    title: 'Thành công',
                    description: flash.success,
                })
                form.reset()
            }
        },
    })
}
</script>

<template>
    <form class="space-y-6" @submit.prevent="submit">
        <div class="grid gap-2">
            <Label for="type">Loại thẻ</Label>
            <div
                class="grid grid-cols-3 lg:grid-cols-5 xl:grid-cols-6 gap-2"
                v-if="rechargeTelecoms.length"
            >
                <div
                    class="col-span-1"
                    v-for="(rechargeTelecom, index) in rechargeTelecoms"
                    :key="index"
                >
                    <button
                        type="button"
                        class="shadow-xs border-2 hover:bg-accent rounded-lg w-full h-full p-3"
                        :class="
                            cn(
                                form.type === rechargeTelecom.value
                                    ? 'border-primary'
                                    : 'border-muted',
                            )
                        "
                        @click="form.type = rechargeTelecom.value"
                        :title="rechargeTelecom.name"
                    >
                        <img
                            v-if="rechargeTelecom.logo"
                            :src="rechargeTelecom.logo"
                            :alt="rechargeTelecom.name"
                            class="w-full object-fill"
                        />
                        <span v-else class="font-medium">
                            {{ rechargeTelecom.name }}
                        </span>
                    </button>
                </div>
            </div>
            <p v-else class="text-sm text-muted-foreground">
                Không có loại thẻ nào
            </p>
            <InputError :message="form.errors.type" />
        </div>

        <div class="grid gap-2">
            <Label for="amount">Mệnh giá</Label>
            <Select v-model="form.amount" id="amount">
                <SelectTrigger>
                    <SelectValue
                        :placeholder="
                            form.type
                                ? 'Chọn mệnh giá'
                                : 'Vui lòng chọn loại thẻ '
                        "
                    />
                </SelectTrigger>
                <SelectContent v-if="form.type">
                    <SelectGroup>
                        <SelectLabel>Chọn mệnh giá</SelectLabel>
                        <SelectItem
                            v-for="(amount, index) in amounts"
                            :key="index"
                            :value="String(amount.value)"
                        >
                            {{ amount.formatted_value }}
                        </SelectItem>
                    </SelectGroup>
                </SelectContent>
            </Select>
            <InputError :message="form.errors.amount" />
        </div>

        <div class="grid items-start grid-cols-1 lg:grid-cols-2 gap-6">
            <div class="grid gap-2">
                <Label for="serial">Số serial</Label>
                <Input v-model="form.serial" id="serial" />
                <InputError :message="form.errors.serial" />
            </div>

            <div class="grid gap-2">
                <Label for="code">Mã thẻ</Label>
                <Input v-model="form.code" id="code" />
                <InputError :message="form.errors.code" />
            </div>
        </div>

        <Button type="submit" class="w-full" :loading="form.processing">
            Nạp thẻ
        </Button>
    </form>
</template>

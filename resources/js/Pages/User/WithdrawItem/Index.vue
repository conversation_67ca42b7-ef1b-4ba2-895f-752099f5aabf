<script setup lang="ts">
import Pagination from '@/components/CustomPagination.vue'
import InputError from '@/components/Forms/InputError.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectGroup,
    SelectItem,
    SelectLabel,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { useToast } from '@/components/ui/toast'
import { GameItem, ItemWithdraw, LengthAwarePaginator } from '@/types'
import { Head, router, useForm } from '@inertiajs/vue3'
import { computed, ref, watch } from 'vue'

const props = defineProps<{
    withdraws: LengthAwarePaginator<ItemWithdraw>
    gameItems: GameItem[]
}>()

const gameItemId = ref<string>(route().params.gameItem || '')

const selectedGameItem = computed(() =>
    props.gameItems.find((item) => String(item.id) === gameItemId.value),
)

const getCustomFields = (item: GameItem | undefined) => {
    return item?.custom_fields
        ? Object.fromEntries(
              item.custom_fields.map((field) => [
                  field.id,
                  field.default_value ?? '',
              ]),
          )
        : {}
}

type CustomFieldsErrors = Record<string, string>

const form = useForm<{
    value: string
    custom_fields: Record<string, string>
    errors: CustomFieldsErrors
}>({
    value: '',
    custom_fields: getCustomFields(selectedGameItem.value),
    errors: {} as CustomFieldsErrors,
})

const { toast } = useToast()

const submit = () => {
    form.post(route('user.withdraw-items.store', gameItemId.value), {
        preserveScroll: true,
        preserveState: true,
        onSuccess: ({ props }) => {
            const { error, success } = props.flash

            if (error) {
                toast({
                    title: 'Thất bại',
                    description: error,
                    variant: 'destructive',
                })
            } else if (success) {
                toast({
                    title: 'Thành công',
                    description: success,
                    variant: 'success',
                })
                form.reset()
            }
        },
    })
}

watch(selectedGameItem, (newItem) => {
    form.custom_fields = getCustomFields(newItem)
})

watch(
    () => gameItemId.value,
    (gameItem) => {
        router.visit(route('user.withdraw-items.index', { gameItem }))
    },
)
</script>

<template>
    <Head
        :title="
            selectedGameItem ? `Rút ${selectedGameItem.name}` : 'Rút Vật Phẩm'
        "
    />

    <Card class="mb-4">
        <CardHeader>
            <CardTitle class="text-lg">
                {{
                    selectedGameItem
                        ? `Rút ${selectedGameItem.name}`
                        : 'Rút Vật Phẩm'
                }}
            </CardTitle>
        </CardHeader>

        <CardContent>
            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid gap-2">
                    <Label for="game_item">Vật phẩm</Label>
                    <Select v-model="gameItemId" id="game_item">
                        <SelectTrigger>
                            <SelectValue placeholder="Chọn vật phẩm" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectGroup>
                                <SelectItem
                                    v-for="item in gameItems"
                                    :key="item.id"
                                    :value="String(item.id)"
                                >
                                    {{ item.name }}
                                </SelectItem>
                            </SelectGroup>
                        </SelectContent>
                    </Select>
                </div>

                <template v-if="selectedGameItem">
                    <div class="flex gap-2">
                        <span>Số vật phẩm hiện có:</span>
                        <p class="font-medium text-primary">
                            {{ selectedGameItem.formatted_quantity }}
                        </p>
                    </div>

                    <div class="grid gap-2">
                        <Label for="value">Gói rút</Label>
                        <Select v-model="form.value" id="value">
                            <SelectTrigger>
                                <SelectValue placeholder="Chọn vật phẩm" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                    <SelectItem
                                        v-for="withdrawPackage in selectedGameItem.withdraw_packages"
                                        :key="withdrawPackage.value"
                                        :value="String(withdrawPackage.value)"
                                    >
                                        {{ withdrawPackage.name }}
                                    </SelectItem>
                                </SelectGroup>
                            </SelectContent>
                        </Select>
                        <InputError :message="form.errors.value" />
                    </div>

                    <template v-if="selectedGameItem.custom_fields">
                        <template
                            v-for="(
                                field, index
                            ) in selectedGameItem.custom_fields"
                            :key="index"
                        >
                            <div
                                class="grid gap-2"
                                v-if="
                                    field.type === 'text' ||
                                    field.type === 'number'
                                "
                            >
                                <Label :for="`custom_fields[${field.id}]`">
                                    {{ field.name }}
                                </Label>
                                <Input
                                    :type="field.type"
                                    :id="`custom_fields[${field.id}]`"
                                    :placeholder="field.description"
                                    :required="field.required"
                                    v-model="form.custom_fields[field.id]"
                                />
                                <InputError
                                    :message="
                                        form.errors[`custom_fields.${field.id}`]
                                    "
                                />
                            </div>
                            <div
                                class="grid gap-2"
                                v-else-if="field.type === 'select'"
                            >
                                <Label :for="`custom_fields[${field.id}]`">
                                    {{ field.name }}
                                </Label>
                                <Select
                                    :id="`custom_fields[${field.id}]`"
                                    v-model="form.custom_fields[field.id]"
                                >
                                    <SelectTrigger v-if="field.description">
                                        <SelectValue
                                            :placeholder="field.description"
                                        />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectGroup>
                                            <SelectLabel
                                                v-if="field.description"
                                            >
                                                {{ field.description }}
                                            </SelectLabel>
                                            <SelectItem
                                                v-for="option in field.options"
                                                :key="option"
                                                :value="option"
                                            >
                                                {{ option }}
                                            </SelectItem>
                                        </SelectGroup>
                                    </SelectContent>
                                </Select>
                                <InputError
                                    :message="
                                        form.errors[`custom_fields.${field.id}`]
                                    "
                                />
                            </div>
                        </template>
                    </template>
                </template>

                <Button
                    type="submit"
                    class="w-full"
                    :loading="form.processing"
                    :disabled="!gameItemId || !form.value"
                >
                    Rút ngay
                </Button>
            </form>
        </CardContent>
    </Card>

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Lịch sử rút vật phẩm</CardTitle>
        </CardHeader>

        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>Vật phẩm</TableHead>
                    <TableHead>Số lượng</TableHead>
                    <TableHead>Trạng thái</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <template v-if="withdraws.data.length > 0">
                    <TableRow
                        v-for="(withdraw, index) in withdraws.data"
                        :key="index"
                    >
                        <TableCell>{{ withdraw.created_at }}</TableCell>
                        <TableCell>{{ withdraw.id }}</TableCell>
                        <TableCell>{{ withdraw.game_item.name }}</TableCell>
                        <TableCell>{{ withdraw.quantity }}</TableCell>
                        <TableCell>{{ withdraw.status }}</TableCell>
                    </TableRow>
                </template>
                <TableRow v-else>
                    <TableCell colspan="5" className="h-16 text-center">
                        Không có dữ liệu
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </Card>

    <Pagination
        class="mt-4"
        v-if="withdraws.meta.total > 0"
        :meta="withdraws.meta"
    />
</template>

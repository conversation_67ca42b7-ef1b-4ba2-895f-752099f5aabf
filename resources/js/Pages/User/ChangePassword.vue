<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/toast'
import { Head, useForm } from '@inertiajs/vue3'
import { ref } from 'vue'

const passwordInput = ref<HTMLInputElement | null>(null)
const currentPasswordInput = ref<HTMLInputElement | null>(null)

const form = useForm({
    current_password: '',
    password: '',
    password_confirmation: '',
})

const { toast } = useToast()

const updatePassword = () => {
    form.put(route('user.change-password'), {
        preserveScroll: true,
        onSuccess: () => {
            toast({
                title: 'Thành công',
                description: '<PERSON><PERSON>t khẩu đã được cập nhật',
            })
            form.reset()
        },
        onError: () => {
            if (form.errors.password) {
                form.reset('password', 'password_confirmation')
                passwordInput.value?.focus()
            }
            if (form.errors.current_password) {
                form.reset('current_password')
                currentPasswordInput.value?.focus()
            }
        },
    })
}
</script>

<template>
    <Head title="Đổi mật khẩu" />

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Đổi mật khẩu</CardTitle>
        </CardHeader>

        <CardContent>
            <form @submit.prevent="updatePassword" class="space-y-6">
                <div class="grid gap-2">
                    <Label for="current_password">Mật khẩu hiện tại</Label>
                    <Input
                        v-model="form.current_password"
                        type="password"
                        id="current_password"
                        ref="currentPasswordInput"
                        required
                    />
                    <InputError :message="form.errors.current_password" />
                </div>

                <div class="grid gap-2">
                    <Label for="password">Mật khẩu mới</Label>
                    <Input
                        v-model="form.password"
                        type="password"
                        id="password"
                        ref="passwordInput"
                        required
                    />
                    <InputError :message="form.errors.password" />
                </div>

                <div class="grid gap-2">
                    <Label for="password_confirmation"
                        >Xác nhận mật khẩu mới</Label
                    >
                    <Input
                        v-model="form.password_confirmation"
                        type="password"
                        id="password_confirmation"
                        required
                    />
                    <InputError :message="form.errors.password_confirmation" />
                </div>

                <Button type="submit" :loading="form.processing" class="w-full">
                    Cập nhật mật khẩu
                </Button>
            </form>
        </CardContent>
    </Card>
</template>

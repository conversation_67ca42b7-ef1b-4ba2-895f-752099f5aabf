<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import ZaloIcon from '@/components/Icons/ZaloIcon.vue'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import AlertTitle from '@/components/ui/alert/AlertTitle.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/toast'
import { User } from '@/types'
import { Head, useForm, usePage } from '@inertiajs/vue3'
import { AlertTriangleIcon } from 'lucide-vue-next'
import { computed } from 'vue'

interface Affiliate {
    id: number
    full_name: string
    phone: string
    email: string
    status: string
    status_label: string
    referral_code: string
    referral_url: string
    total_commission: number
    available_commission: number
    total_clicks: number
    total_conversions: number
    conversion_rate: number
    created_at: string
    approved_at?: string
    tier: {
        name: string
        commission_rate: number
    }
}

const props = defineProps<{
    affiliate?: Affiliate
}>()

const { user } = usePage().props.auth as { user: User }

const form = useForm({
    full_name: props.affiliate?.full_name || user.display_name,
    phone: props.affiliate?.phone || '',
    email: props.affiliate?.email || user.email,
})

const { toast } = useToast()

const isRegistered = computed(() => !!props.affiliate)
const isPending = computed(() => props.affiliate?.status === 'pending')
const isApproved = computed(() => props.affiliate?.status === 'approved')
const isRejected = computed(() => props.affiliate?.status === 'rejected')
const isSuspended = computed(() => props.affiliate?.status === 'suspended')

const submit = () => {
    form.post(route('user.affiliate.store'), {
        preserveScroll: true,
        onSuccess: (props) => {
            if (props.props.flash.success) {
                toast({
                    title: 'Thành công',
                    description: props.props.flash.success,
                })
            }

            if (props.props.flash.error) {
                toast({
                    title: 'Lỗi',
                    description: props.props.flash.error,
                    variant: 'destructive',
                })
            }
        },
        onError: () => {
            toast({
                title: 'Lỗi',
                description: 'Có lỗi xảy ra, vui lòng thử lại.',
                variant: 'destructive',
            })
        },
    })
}
</script>

<template>
    <Head title="Tiếp thị liên kết" />

    <Card v-if="isRegistered" class="mb-6">
        <CardHeader>
            <CardTitle class="text-lg">Thông tin Tiếp thị liên kết</CardTitle>
        </CardHeader>
        <CardContent class="text-sm">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="font-medium">Mã giới thiệu:</span>
                        <span class="font-mono">{{
                            affiliate?.referral_code
                        }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Cấp bậc:</span>
                        <span>{{ affiliate?.tier.name }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Tỷ lệ hoa hồng:</span>
                        <span>{{ affiliate?.tier.commission_rate }}%</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Tổng hoa hồng:</span>
                        <span
                            class="text-green-600 dark:text-green-400 font-medium"
                        >
                            {{ affiliate?.total_commission?.toLocaleString() }}
                            VNĐ
                        </span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Hoa hồng khả dụng:</span>
                        <span
                            class="text-blue-600 dark:text-blue-400 font-medium"
                        >
                            {{
                                affiliate?.available_commission?.toLocaleString()
                            }}
                            VNĐ
                        </span>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="flex justify-between">
                        <span class="font-medium">Lượt click:</span>
                        <span>{{ affiliate?.total_clicks }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Lượt chuyển đổi:</span>
                        <span>{{ affiliate?.total_conversions }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Tỷ lệ chuyển đổi:</span>
                        <span>{{ affiliate?.conversion_rate }}%</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Ngày đăng ký:</span>
                        <span>{{ affiliate?.created_at }}</span>
                    </div>

                    <div
                        class="flex justify-between"
                        v-if="affiliate?.approved_at"
                    >
                        <span class="font-medium">Ngày duyệt:</span>
                        <span>{{ affiliate.approved_at }}</span>
                    </div>

                    <div class="flex justify-between">
                        <span class="font-medium">Link giới thiệu:</span>
                        <a
                            :href="affiliate?.referral_url"
                            target="_blank"
                            class="text-blue-600 dark:text-blue-400 hover:underline text-sm"
                        >
                            Xem link
                        </a>
                    </div>
                </div>
            </div>

            <Alert v-if="isPending" class="mt-6">
                <AlertTriangleIcon class="size-4 text-yellow-500!" />
                <AlertTitle class="text-yellow-500">
                    Đơn đăng ký đang chờ duyệt
                </AlertTitle>
                <AlertDescription>
                    Đơn đăng ký tiếp thị liên kết của bạn đang chờ admin xét
                    duyệt. Vui lòng chờ thông báo.
                </AlertDescription>
            </Alert>

            <Alert v-if="isRejected || isSuspended" class="mt-6">
                <AlertTriangleIcon class="size-4 text-red-500!" />
                <AlertTitle class="text-red-500">
                    Tài khoản tiếp thị liên kết bị
                    {{ isRejected ? 'từ chối' : 'tạm dừng' }}
                </AlertTitle>
                <AlertDescription>
                    Vui lòng liên hệ admin để được hỗ trợ hoặc cập nhật thông
                    tin bên dưới.
                </AlertDescription>
            </Alert>
        </CardContent>
    </Card>

    <Card v-if="!isRegistered" class="mb-6">
        <CardHeader>
            <CardTitle class="text-lg">
                Đăng ký làm Tiếp thị liên kết
            </CardTitle>
        </CardHeader>
        <CardContent>
            <form @submit.prevent="submit" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="grid gap-2">
                        <Label for="full_name">Họ và tên</Label>
                        <Input
                            v-model="form.full_name"
                            id="full_name"
                            required
                        />
                        <InputError :message="form.errors.full_name" />
                    </div>

                    <div class="grid gap-2">
                        <Label for="phone">Số điện thoại</Label>
                        <Input
                            v-model="form.phone"
                            id="phone"
                            type="tel"
                            required
                        />
                        <InputError :message="form.errors.phone" />
                    </div>
                </div>

                <div class="grid gap-2">
                    <Label for="email">Email</Label>
                    <Input
                        v-model="form.email"
                        id="email"
                        type="email"
                        required
                    />
                    <InputError :message="form.errors.email" />
                </div>

                <Button type="submit" :loading="form.processing" class="w-full">
                    {{
                        isRegistered
                            ? 'Cập nhật thông tin'
                            : 'Đăng ký làm Tiếp thị liên kết'
                    }}
                </Button>
            </form>
        </CardContent>
    </Card>

    <div
        class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/50 dark:to-indigo-950/50 rounded-lg p-4 border border-blue-200 dark:border-blue-800"
    >
        <div class="flex items-start gap-3">
            <div
                class="flex-shrink-0 w-10 h-10 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center"
            >
                <ZaloIcon class="size-5" />
            </div>
            <div class="flex-1">
                <h3 class="font-semibold text-gray-900 dark:text-gray-100 mb-1">
                    Tham gia nhóm Zalo để nhận thông báo sớm nhất! 🚀
                </h3>
                <p class="text-gray-600 dark:text-gray-400 text-sm mb-3">
                    Kết nối với cộng đồng tiếp thị liên kết, nhận hỗ trợ 24/7 và
                    cập nhật thông tin mới nhất về chương trình tiếp thị liên
                    kết.
                </p>
                <Button
                    as="a"
                    href="https://zalo.me/g/cmhxkg023"
                    target="_blank"
                    class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 text-white"
                >
                    Tham gia nhóm
                </Button>
            </div>
        </div>
    </div>

    <Card class="mt-6">
        <CardHeader>
            <CardTitle class="text-lg">Hướng dẫn</CardTitle>
        </CardHeader>
        <CardContent>
            <div class="space-y-4">
                <div class="flex items-start">
                    <div
                        class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5"
                    >
                        1
                    </div>
                    <div>
                        <p class="font-medium mb-1">Đăng ký làm affiliate</p>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            Hoàn thành thông tin và chờ admin duyệt
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div
                        class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5"
                    >
                        2
                    </div>
                    <div>
                        <p class="font-medium mb-1">Tham gia nhóm Zalo</p>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            Tham gia nhóm để nhận thông báo và hỗ trợ nhanh nhất
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div
                        class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5"
                    >
                        3
                    </div>
                    <div>
                        <p class="font-medium mb-1">Chia sẻ link giới thiệu</p>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            Sử dụng link giới thiệu để thu hút khách hàng
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div
                        class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5"
                    >
                        4
                    </div>
                    <div>
                        <p class="font-medium mb-1">Nhận hoa hồng</p>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            Khi khách hàng mua hàng qua link của bạn
                        </p>
                    </div>
                </div>

                <div class="flex items-start">
                    <div
                        class="flex-shrink-0 w-6 h-6 bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400 rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5"
                    >
                        5
                    </div>
                    <div>
                        <p class="font-medium mb-1">Rút tiền</p>
                        <p class="text-gray-600 dark:text-gray-400 text-sm">
                            Rút hoa hồng về tài khoản ngân hàng
                        </p>
                    </div>
                </div>
            </div>
        </CardContent>
    </Card>
</template>

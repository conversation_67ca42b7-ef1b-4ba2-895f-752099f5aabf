<script setup lang="ts">
import {
    FilterForm,
    FilterInput,
    FilterSelect,
    FilterWrapper,
} from '@/components'
import CopyToClipboard from '@/components/CopyToClipboard.vue'
import Pagination from '@/components/CustomPagination.vue'
import EyeIcon from '@/components/Icons/EyeIcon.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Dialog,
    DialogContent,
    DialogFooter,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import { SelectGroup, SelectItem } from '@/components/ui/select'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { PriceRangesType } from '@/Pages/AccountCategories/types'
import { AccountCategory, LengthAwarePaginator } from '@/types'
import { Head, Link, router, useForm } from '@inertiajs/vue3'
import { pickBy, throttle } from 'lodash'
import NProgress from 'nprogress'
import { computed, ref, watch } from 'vue'

interface PurchasedAccount {
    id: number
    acc_name: string | null
    acc_pass: string | null
    price: number
    seen_at: string | null
    created_at: string
    category: AccountCategory
}

const props = defineProps<{
    categories: Record<string, string>
    accounts: LengthAwarePaginator<PurchasedAccount>
    filters: Record<string, string>
    priceRanges: PriceRangesType
}>()

const form = useForm<{
    id: string
    category_id: string
    price: string
    from_date: string
    to_date: string
}>({
    id: props.filters.id || '',
    category_id: props.filters.category_id || '',
    price: props.filters.price || '',
    from_date: props.filters.from_date || '',
    to_date: props.filters.from_date || '',
})

const isDetailModelOpen = ref(false)
const viewingAccount = ref<PurchasedAccount | null>(null)

const viewAccountDetail = (id: number) => {
    NProgress.start()

    fetch(route('user.purchased-accounts.show', id))
        .then((response) => response.json())
        .then((data: PurchasedAccount) => {
            viewingAccount.value = data

            isDetailModelOpen.value = true
        })
        .finally(() => NProgress.done())
}

watch(
    () => form.data(),
    throttle(() => {
        form.transform(() => pickBy(form.data())).get(
            route('user.purchased-accounts.index'),
            {
                preserveState: true,
                only: ['accounts'],
            },
        )
    }),
    { deep: true },
)

const isFiltering = computed(() => {
    const data = form.data()

    return (
        data.id !== '' ||
        data.category_id !== '' ||
        data.price !== '' ||
        data.from_date !== '' ||
        data.to_date !== ''
    )
})
</script>

<template>
    <Head title="Tài khoản đã mua" />

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Tài khoản đã mua</CardTitle>
        </CardHeader>

        <CardContent>
            <FilterForm
                :isFiltering
                @reset="router.get(route('user.purchased-accounts.index'))"
            >
                <FilterWrapper label="ID">
                    <FilterInput
                        type="text"
                        v-model="form.id"
                        placeholder="Mã số tài khoản"
                    />
                </FilterWrapper>
                <FilterWrapper label="Loại game">
                    <FilterSelect
                        v-model="form.category_id"
                        placeholder="Chọn loại game"
                    >
                        <SelectGroup>
                            <SelectItem
                                v-for="(category, index) in categories"
                                :key="index"
                                :value="index"
                            >
                                {{ category }}
                            </SelectItem>
                        </SelectGroup>
                    </FilterSelect>
                </FilterWrapper>
                <FilterWrapper label="Giá tiền">
                    <FilterSelect
                        v-model="form.price"
                        placeholder="Chọn giá tiền"
                    >
                        <SelectGroup>
                            <SelectItem
                                v-for="(priceRange, key) in priceRanges"
                                :key="key"
                                :value="key"
                            >
                                {{ priceRange }}
                            </SelectItem>
                        </SelectGroup>
                    </FilterSelect>
                </FilterWrapper>
                <FilterWrapper label="Từ ngày">
                    <FilterInput type="date" v-model="form.from_date" />
                </FilterWrapper>
                <FilterWrapper label="Đến ngày">
                    <FilterInput type="date" v-model="form.to_date" />
                </FilterWrapper>
            </FilterForm>
        </CardContent>

        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>Loại game</TableHead>
                    <TableHead>Tài khoản</TableHead>
                    <TableHead>Giá trị</TableHead>
                    <TableHead>Thao tác</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <template v-if="accounts.data.length > 0">
                    <TableRow
                        v-for="(account, index) in accounts.data"
                        :key="index"
                    >
                        <TableCell>{{ account.created_at }}</TableCell>
                        <TableCell>
                            <Button variant="link" class="p-0" as-child>
                                <Link
                                    :href="route('accounts.show', account.id)"
                                >
                                    #{{ account.id }}
                                </Link>
                            </Button>
                        </TableCell>
                        <TableCell>
                            <Button variant="link" class="p-0" as-child>
                                <Link
                                    :href="
                                        route('categories.show', {
                                            slug: account.category.slug,
                                        })
                                    "
                                >
                                    {{ account.category.name }}
                                </Link>
                            </Button>
                        </TableCell>
                        <TableCell>{{ account.acc_name }}</TableCell>
                        <TableCell class="text-red-600">{{
                            account.price
                        }}</TableCell>
                        <TableCell>
                            <Button
                                @click="viewAccountDetail(account.id)"
                                :variant="
                                    account.seen_at ? 'default' : 'destructive'
                                "
                                size="xs"
                            >
                                <EyeIcon class="size-4 me-1.5" />
                                <span
                                    v-text="
                                        account.seen_at ? 'Xem' : 'Kiểm tra'
                                    "
                                ></span>
                            </Button>
                        </TableCell>
                    </TableRow>
                </template>
                <TableRow v-else>
                    <TableCell colspan="6" className="h-16 text-center">
                        Không có dữ liệu
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </Card>

    <Pagination
        class="mt-4"
        v-if="accounts.meta.total > 0"
        :meta="accounts.meta"
    />

    <Dialog v-model:open="isDetailModelOpen">
        <DialogContent v-if="viewingAccount" class="max-w-xl">
            <DialogHeader>
                <DialogTitle
                    >Chi tiết tài khoản #{{ viewingAccount.id }}</DialogTitle
                >
            </DialogHeader>

            <dl class="grid grid-cols-1 gap-4 sm:grid-cols-2">
                <div class="col-span-2 sm:col-span-1">
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Danh mục game
                    </dt>
                    <dd class="font-semibold text-gray-900 dark:text-white">
                        <Button variant="link" class="p-0 text-base" as-child>
                            <Link
                                :href="
                                    route('categories.show', {
                                        slug: viewingAccount.category.slug,
                                    })
                                "
                            >
                                {{ viewingAccount.category.name }}
                            </Link>
                        </Button>
                    </dd>
                </div>
                <div class="col-span-2 sm:col-span-1">
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Mã số
                    </dt>
                    <dd class="font-semibold text-gray-900 dark:text-white">
                        <Button variant="link" class="p-0 text-base" as-child>
                            <Link
                                :href="
                                    route('accounts.show', viewingAccount.id)
                                "
                            >
                                #{{ viewingAccount.id }}
                            </Link>
                        </Button>
                    </dd>
                </div>
                <div
                    class="col-span-2 sm:col-span-1"
                    v-if="viewingAccount.acc_name"
                >
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Tài khoản
                    </dt>
                    <dd
                        class="flex gap-2 items-center font-semibold text-gray-900 dark:text-white"
                    >
                        {{ viewingAccount.acc_name }}
                        <CopyToClipboard :content="viewingAccount.acc_name" />
                    </dd>
                </div>
                <div
                    class="col-span-2 sm:col-span-1"
                    v-if="viewingAccount.acc_pass"
                >
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Mật khẩu
                    </dt>
                    <dd
                        class="flex gap-2 items-center font-semibold text-gray-900 dark:text-white"
                    >
                        {{ viewingAccount.acc_pass }}
                        <CopyToClipboard :content="viewingAccount.acc_pass" />
                    </dd>
                </div>
                <div class="col-span-2 sm:col-span-1">
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Giá trị
                    </dt>
                    <dd class="font-semibold text-gray-900 dark:text-white">
                        {{ viewingAccount.price }}
                    </dd>
                </div>
                <div class="col-span-2 sm:col-span-1">
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Thời gian mua
                    </dt>
                    <dd class="font-semibold text-gray-900 dark:text-white">
                        {{ viewingAccount.created_at }}
                    </dd>
                </div>
                <div class="col-span-2">
                    <dt
                        class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-1"
                    >
                        Đã xem thông tin lần đầu lúc
                    </dt>
                    <dd class="font-semibold text-gray-900 dark:text-white">
                        {{ viewingAccount.seen_at }}
                    </dd>
                </div>
            </dl>

            <DialogFooter>
                <Button
                    type="button"
                    variant="outline"
                    class="w-full"
                    @click="isDetailModelOpen = false"
                >
                    Đóng
                </Button>
            </DialogFooter>
        </DialogContent>
    </Dialog>
</template>

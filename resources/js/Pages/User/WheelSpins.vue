<script setup lang="ts">
import {
    FilterForm,
    FilterInput,
    FilterSelect,
    FilterWrapper,
} from '@/components'
import Pagination from '@/components/CustomPagination.vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { SelectGroup, SelectItem } from '@/components/ui/select'
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from '@/components/ui/table'
import { formatCurrency } from '@/lib/utils'
import { LengthAwarePaginator, Wheel, WheelSpin } from '@/types'
import { Head, router, useForm } from '@inertiajs/vue3'
import { pickBy, throttle } from 'lodash'
import { computed, watch } from 'vue'

const props = defineProps<{
    wheels: Wheel[]
    wheelSpins: LengthAwarePaginator<WheelSpin>
    filters: Record<string, string>
}>()

const form = useForm<{
    q: string
    wheel: string
    from_date: string
    to_date: string
}>({
    q: props.filters.q || '',
    wheel: props.filters.wheel || '',
    from_date: props.filters.from_date || '',
    to_date: props.filters.to_date || '',
})

watch(
    () => form.data(),
    throttle(() => {
        form.transform(() => pickBy(form.data())).get(
            route('user.wheel-spins'),
            {
                preserveState: true,
                only: ['wheelSpins'],
            },
        )
    }),
    { deep: true },
)

const isFiltering = computed(() => {
    const data = form.data()

    return (
        data.q !== '' ||
        data.wheel !== '' ||
        data.from_date !== '' ||
        data.to_date !== ''
    )
})
</script>

<template>
    <Head title="Lịch sử vòng quay" />

    <Card>
        <CardHeader>
            <CardTitle class="text-lg">Lịch sử vòng quay</CardTitle>
        </CardHeader>

        <CardContent>
            <FilterForm
                :isFiltering
                @reset="router.get(route('user.wheel-spins'))"
            >
                <FilterWrapper label="Tìm kiếm">
                    <FilterInput
                        v-model="form.q"
                        placeholder="ID giao dịch, kết quả, ..."
                    />
                </FilterWrapper>
                <FilterWrapper label="Vòng quay">
                    <FilterSelect
                        v-model="form.wheel"
                        placeholder="Chọn vòng quay"
                    >
                        <SelectGroup>
                            <SelectItem
                                v-for="wheel in wheels"
                                :key="wheel.id"
                                :value="String(wheel.id)"
                            >
                                {{ wheel.name }}
                            </SelectItem>
                        </SelectGroup>
                    </FilterSelect>
                </FilterWrapper>
                <FilterWrapper label="Đến ngày">
                    <FilterInput type="date" v-model="form.from_date" />
                </FilterWrapper>
                <FilterWrapper label="Từ ngày">
                    <FilterInput type="date" v-model="form.to_date" />
                </FilterWrapper>
            </FilterForm>
        </CardContent>

        <Table>
            <TableHeader>
                <TableRow>
                    <TableHead>Thời gian</TableHead>
                    <TableHead>ID</TableHead>
                    <TableHead>Vòng quay</TableHead>
                    <TableHead>Số tiền</TableHead>
                    <TableHead>Kết quả</TableHead>
                </TableRow>
            </TableHeader>
            <TableBody>
                <template v-if="wheelSpins.data.length > 0">
                    <TableRow
                        v-for="(wheelSpin, index) in wheelSpins.data"
                        :key="index"
                    >
                        <TableCell>{{ wheelSpin.created_at }}</TableCell>
                        <TableCell>{{ wheelSpin.id }}</TableCell>
                        <TableCell>{{ wheelSpin.wheel.name }}</TableCell>
                        <TableCell>{{
                            formatCurrency(wheelSpin.price)
                        }}</TableCell>
                        <TableCell>{{ wheelSpin.result }}</TableCell>
                    </TableRow>
                </template>
                <TableRow v-else>
                    <TableCell colspan="5" className="h-16 text-center">
                        Không có dữ liệu
                    </TableCell>
                </TableRow>
            </TableBody>
        </Table>
    </Card>

    <Pagination
        class="mt-4"
        v-if="wheelSpins.meta.total > 0"
        :meta="wheelSpins.meta"
    />
</template>

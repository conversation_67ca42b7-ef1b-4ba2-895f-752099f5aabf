<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import SocialList from '@/components/SocialLogin/SocialList.vue'
import { Button } from '@/components/ui/button'
import {
    <PERSON><PERSON>ontent,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Head, Link, useForm } from '@inertiajs/vue3'

const form = useForm({
    name: '',
    username: '',
    email: '',
    password: '',
    password_confirmation: '',
})

const submit = () => {
    form.post(route('register'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation')
        },
    })
}
</script>

<template>
    <Head title="Đăng ký tài khoản" />

    <CardHeader>
        <CardTitle>Đăng ký tài khoản</CardTitle>
    </CardHeader>

    <CardContent>
        <form @submit.prevent="submit" class="space-y-6">
            <div class="grid gap-2">
                <Label for="name"> Họ tên </Label>
                <Input
                    v-model="form.name"
                    id="name"
                    type="text"
                    required
                    autofocus
                />
                <InputError :message="form.errors.name" />
            </div>
            <div class="grid gap-2">
                <Label for="username"> Tên tài khoản </Label>
                <Input
                    v-model="form.username"
                    id="username"
                    type="text"
                    required
                />
                <InputError :message="form.errors.username" />
            </div>
            <div class="grid gap-2">
                <Label for="email"> Email </Label>
                <Input v-model="form.email" id="email" type="email" required />
                <InputError :message="form.errors.email" />
            </div>
            <div class="grid gap-2">
                <Label for="password"> Mật khẩu </Label>
                <Input
                    v-model="form.password"
                    id="password"
                    type="password"
                    required
                />
                <InputError :message="form.errors.password" />
            </div>
            <div class="grid gap-2">
                <Label for="password_confirmation"> Xác nhận mật khẩu </Label>
                <Input
                    v-model="form.password_confirmation"
                    id="password_confirmation"
                    type="password"
                    required
                />
                <InputError :message="form.errors.password_confirmation" />
            </div>
            <Button type="submit" :loading="form.processing" class="w-full">
                Đăng ký
            </Button>
        </form>
    </CardContent>

    <CardFooter class="flex-col">
        <p class="text-sm">
            Đã có tài khoản?
            <Button variant="link" class="p-0">
                <Link :href="route('login')">Đăng nhập ngay</Link>
            </Button>
        </p>

        <SocialList />
    </CardFooter>
</template>

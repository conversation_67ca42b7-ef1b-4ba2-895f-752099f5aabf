<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import SocialList from '@/components/SocialLogin/SocialList.vue'
import { Button } from '@/components/ui/button'
import {
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>ooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Head, Link, useForm } from '@inertiajs/vue3'

const form = useForm({
    username: '',
    password: '',
    remember: true,
})

const submit = () => {
    form.post(route('login'), {
        onFinish: () => form.reset('password'),
    })
}
</script>

<template>
    <Head title="Đăng nhập" />

    <CardHeader>
        <CardTitle>Đăng nhập</CardTitle>
    </CardHeader>

    <CardContent>
        <form @submit.prevent="submit" class="space-y-6">
            <div class="grid gap-2">
                <Label for="username">T<PERSON><PERSON></Label>
                <Input type="text" v-model="form.username" required autofocus />
                <InputError :message="form.errors.username" />
            </div>

            <div class="grid gap-2">
                <Label for="password">Mật khẩu</Label>
                <Input type="password" v-model="form.password" required />
                <InputError :message="form.errors.password" />
            </div>

            <div class="flex justify-between items-center">
                <div class="flex flex-row items-start gap-x-3 space-y-0">
                    <Checkbox id="remember" v-model="form.remember" />
                    <div class="space-y-1 leading-none">
                        <Label for="remember">Ghi nhớ</Label>
                    </div>
                </div>

                <Button variant="link" as-child class="p-0">
                    <Link :href="route('password.request')">
                        Quên mật khẩu?
                    </Link>
                </Button>
            </div>

            <Button type="submit" class="w-full" :loading="form.processing">
                Đăng nhập
            </Button>
        </form>
    </CardContent>

    <CardFooter class="flex-col">
        <p class="text-sm">
            Chưa có tài khoản?
            <Button variant="link" class="p-0">
                <Link :href="route('register')">Đăng ký ngay</Link>
            </Button>
        </p>

        <SocialList />
    </CardFooter>
</template>

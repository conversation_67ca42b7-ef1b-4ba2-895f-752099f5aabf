<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import { Button } from '@/components/ui/button'
import { Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Head, useForm } from '@inertiajs/vue3'

const props = defineProps<{
    email: string
    token: string
}>()

const form = useForm({
    token: props.token,
    email: props.email,
    password: '',
    password_confirmation: '',
})

const submit = () => {
    form.post(route('password.store'), {
        onFinish: () => {
            form.reset('password', 'password_confirmation')
        },
    })
}
</script>

<template>
    <Head title="Đặt lại mật khẩu" />

    <CardHeader>
        <CardTitle>Đặt lại mật khẩu</CardTitle>
    </CardHeader>

    <CardContent>
        <form @submit.prevent="submit" class="space-y-6">
            <div class="grid gap-2">
                <Label for="email"> Email </Label>
                <Input
                    id="email"
                    type="email"
                    v-model="form.email"
                    required
                    autocomplete="username"
                />
                <InputError :message="form.errors.email" />
            </div>

            <div class="grid gap-2">
                <Label for="password"> Mật khẩu </Label>
                <Input
                    id="password"
                    type="password"
                    v-model="form.password"
                    required
                    autofocus
                    autocomplete="new-password"
                />
                <InputError :message="form.errors.password" />
            </div>

            <div class="grid gap-2">
                <Label for="password_confirmation"> Xác nhận mật khẩu </Label>
                <Input
                    id="password_confirmation"
                    type="password"
                    v-model="form.password_confirmation"
                    required
                    autocomplete="new-password"
                />
                <InputError :message="form.errors.password_confirmation" />
            </div>

            <Button type="submit" :loading="form.processing" class="w-full">
                Đặt lại mật khẩu
            </Button>
        </form>
    </CardContent>
</template>

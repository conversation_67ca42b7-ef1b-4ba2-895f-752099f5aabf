<script setup lang="ts">
import InputError from '@/components/Forms/InputError.vue'
import { Button } from '@/components/ui/button'
import {
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/components/ui/toast'
import { Head, Link, useForm } from '@inertiajs/vue3'

const form = useForm({
    email: '',
})

const { toast } = useToast()

const submit = () => {
    form.post(route('password.email'), {
        onSuccess: ({ props }) => {
            if (props.flash.success) {
                toast({
                    title: 'Thành công',
                    description:
                        'Chúng tôi đã gửi cho bạn một email với hướng dẫn đặt lại mật khẩu.',
                })
            }

            form.reset('email')
        },
    })
}
</script>

<template>
    <Head title="Quên mật khẩu" />

    <CardHeader>
        <CardTitle>Quên mật khẩu</CardTitle>
        <CardDescription>
            Nhập địa chỉ email của bạn và chúng tôi sẽ gửi cho bạn một liên kết
            đặt lại mật khẩu.
        </CardDescription>
    </CardHeader>

    <CardContent>
        <form @submit.prevent="submit" class="space-y-6">
            <div class="grid gap-2">
                <Label for="email">Email</Label>
                <Input
                    id="email"
                    type="email"
                    v-model="form.email"
                    required
                    autofocus
                    autocomplete="username"
                />
                <InputError :message="form.errors.email" />
            </div>

            <Button type="submit" :loading="form.processing" class="w-full">
                Gửi liên kết đặt lại mật khẩu
            </Button>
        </form>
    </CardContent>

    <CardFooter class="flex-col">
        <p class="text-sm">
            <Link :href="route('login')" as-child>
                <Button variant="link"> Quay lại đăng nhập </Button>
            </Link>
        </p>
    </CardFooter>
</template>

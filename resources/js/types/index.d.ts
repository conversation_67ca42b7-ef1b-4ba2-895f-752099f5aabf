export interface User {
    id: number
    display_name: string
    username: string
    name: string
    email: string
    email_verified_at: string
    balance: number
    formatted_balance: string
    avatar_url: string
    created_at: string
    game_items: GameItem[]
}

export interface Affiliate {
    id: number
    status: string
}

export interface Publisher {
    name: string
    slug: string
}

export interface Game {
    id: number
    name: string
    slug: string
    description: string | null
    image: string | null
    is_visible: boolean
    categories_count?: number
    categories?: AccountCategory[]
    publisher?: Publisher
}

export interface AccountCategory {
    id: number
    name: string
    slug: string
    description: string
    image: string
    parent_id?: number | null
    remaining_accounts_count?: number
    sold_accounts_count?: number
    accounts?: Account[]
    parent?: AccountCategory
    children?: AccountCategory[]
    children_count?: number
    game?: Game
    publisher?: Publisher
}

export interface Account {
    id: number
    description: string
    original_price: number
    price: number
    image: string
    images: Array<string>
    content: string | null
    status: string
    attributes: Array<{
        id: string
        name: string
        slug: string
        attribute_set: {
            id: string
            name: string
            slug: string
        }
    }>
    category: AccountCategory
    flashSale?: FlashSale
}

export interface AttributeSet {
    id: string
    name: string
    slug: string
    attributes: Attribute[]
}

export interface Attribute {
    id: string
    name: string
    slug: string
}

export interface Transaction {
    id: string
    type: string
    amount: number
    balance: number
    formatted_amount: string
    formatted_balance: string
    is_positive: boolean
    description: string
    created_at: string
}

export interface Wheel {
    id: number
    name: string
    price: number
    description: string
    slug: string
    thumbnail: string
    image: string
    wheel_spins: WheelSpin[]
}

export interface WheelSpin {
    id: number
    user: User
    wheel: Wheel
    price: number
    result: string
    created_at: string
}

export interface GameItem {
    id: number
    name: string
    quantity: number | undefined
    formatted_quantity: string
    withdraw_packages: {
        name: string
        value: number
        description: string | null
    }[]
    custom_fields: CustomField[]
}

export interface ItemWithdraw {
    id: number
    quantity: number
    status: string
    game_item: GameItem
    created_at: string
}

export interface CustomField {
    id: number
    name: string
    type: string
    options: string[] | null
    description: string | null
    default_value: string | null
    required: boolean
}

export interface BankAccount {
    name: string
    bank_name: string
    account_number: string
    account_holder: string
    logo: string | null
}

export interface Category {
    id: number
    name: string
    slug: string
    posts_count: number
}

export interface Post {
    id: number
    title: string
    description: string
    content: string
    slug: string
    image: string
    created_at: string
    user: User
    category: Category
    views: string
}

export interface FlashSale {
    start_at: string
    end_at: string
    accounts?: {
        account: Account
        price: number
        discount_percent: number
    }[]
}

export interface PaginationMeta {
    current_page: number
    from: number
    last_page: number
    links: Array<{
        url: string | null
        label: string
        active: boolean
    }>
    path: string
    per_page: number
    to: number
    total: number
}

export interface LengthAwarePaginator<T> {
    data: T[]
    links: {
        first: string
        last: string
        prev: string | null
        next: string | null
    }
    meta: PaginationMeta
}

export type PageProps<
    T extends Record<string, unknown> = Record<string, unknown>,
> = T & {
    app: {
        csrf_token: string
        env: 'production' | 'local' | 'demo'
        menu: MenuItem[]
        name: string
        title: string
        logo?: string
        admin_panel_url?: string
        social_login: {
            facebook: boolean
            google: boolean
            zalo: boolean
        }
    }
    auth: {
        user?: User
        affiliate?: Affiliate
    }
    flash: {
        success?: string
        error?: string
    }
    breadcrumbs: Array<{
        title: string
        url?: string
    }>
}

export type MenuItem = {
    title: string
    url: string
    target: string
    children: MenuItem[]
}

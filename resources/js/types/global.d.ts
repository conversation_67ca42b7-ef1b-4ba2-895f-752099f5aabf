import { PageProps as InertiaPageProps } from '@inertiajs/core'
import Echo from 'laravel-echo'
import { route as ziggyRoute } from 'ziggy-js'
import { PageProps as AppPageProps } from './'

declare global {
    var route: typeof ziggyRoute

    interface Window {
        Pusher: any
        Echo: Echo
    }
}

declare module 'vue' {
    interface ComponentCustomProperties {
        route: typeof ziggyRoute
    }
}

declare module '@inertiajs/core' {
    interface PageProps extends InertiaPageProps, AppPageProps {}
}

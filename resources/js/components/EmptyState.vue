<script setup lang="ts">
import InboxIcon from '@/components/Icons/InboxIcon.vue'

defineProps<{
    title: string
    description: string
}>()
</script>

<template>
    <div class="flex h-[450px] shrink-0 items-center justify-center rounded-md">
        <div
            class="mx-auto flex max-w-[420px] flex-col items-center justify-center text-center"
        >
            <InboxIcon class="size-10 text-muted-foreground" />

            <h3 class="mt-4 text-lg font-semibold">
                {{ title }}
            </h3>
            <p class="mb-4 mt-2 text-sm text-muted-foreground">
                {{ description }}
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    Pagination,
    PaginationEllipsis,
    PaginationList,
} from '@/components/ui/pagination'
import { PaginationMeta } from '@/types'
import { Link } from '@inertiajs/vue3'

import { Button } from '@/components/ui/button'

defineProps<{
    meta: PaginationMeta
}>()
</script>

<template>
    <Pagination
        class="flex flex-col lg:flex-row justify-between items-center gap-y-2"
        :total="meta.total"
        :sibling-count="1"
        show-edges
        :default-page="meta.current_page"
    >
        <div class="text-sm text-muted-foreground">
            <PERSON><PERSON> hiển thị {{ meta.from }} đến {{ meta.to }} của
            {{ meta.total }} kết quả
        </div>

        <PaginationList
            class="flex flex-wrap justify-center items-center gap-1"
        >
            <template v-for="(item, index) in meta.links" :key="index">
                <Button
                    as-child
                    v-if="item.url"
                    :variant="item.active ? 'default' : 'outline'"
                >
                    <span v-if="item.active" v-html="item.label" />
                    <Link :href="item.url" v-html="item.label" v-else />
                </Button>
                <PaginationEllipsis v-else-if="item.label === '...'" />
                <Button variant="outline" v-else disabled v-html="item.label" />
            </template>
        </PaginationList>
    </Pagination>
</template>

<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import { Wheel } from '@/types'
import { Link } from '@inertiajs/vue3'

defineProps<{
    wheels: Wheel[]
}>()
</script>

<template>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4">
        <Card v-for="(wheel, index) in wheels" :key="index">
            <Link :href="route('wheels.show', wheel.slug)">
                <img
                    v-lazy="wheel.thumbnail"
                    :alt="wheel.name"
                    class="transition-all aspect-video w-full object-cover rounded-t-lg"
                />
            </Link>
            <div class="text-center p-3">
                <h4
                    class="font-medium truncate group-hover:text-primary transition-all mb-2"
                >
                    <Link
                        :href="route('wheels.show', wheel.slug)"
                        :title="wheel.name"
                        class="hover:text-primary transition-all"
                    >
                        {{ wheel.name }}
                    </Link>
                </h4>
                <div class="text-muted-foreground text-sm">
                    {{ formatCurrency(wheel.price) }}/lượt quay
                </div>

                <div class="grid mt-4">
                    <Button as-child class="w-full">
                        <Link :href="route('wheels.show', wheel.slug)">
                            Quay ngay
                        </Link>
                    </Button>
                </div>
            </div>
        </Card>
    </div>
</template>

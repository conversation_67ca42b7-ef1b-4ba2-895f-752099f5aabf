<script setup lang="ts">
import {
    Select,
    SelectContent,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'

defineProps<{
    placeholder?: string
}>()
</script>

<template>
    <Select>
        <SelectTrigger class="border-0 rounded-e-lg">
            <SelectValue v-if="placeholder" :placeholder="placeholder" />
        </SelectTrigger>
        <SelectContent>
            <slot />
        </SelectContent>
    </Select>
</template>

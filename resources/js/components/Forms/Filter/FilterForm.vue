<script setup lang="ts">
import ArrowPathIcon from '@/components/Icons/ArrowPathIcon.vue'
import { Button } from '@/components/ui/button'

defineProps<{
    isFiltering: boolean
}>()

defineEmits(['reset'])
</script>

<template>
    <form class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 sm:gap-4">
        <slot />

        <div>
            <Button
                v-if="isFiltering"
                type="reset"
                variant="destructive"
                @click="$emit('reset')"
            >
                <ArrowPathIcon class="size-5 me-2" />
                Đặt lại
            </Button>
        </div>
    </form>
</template>

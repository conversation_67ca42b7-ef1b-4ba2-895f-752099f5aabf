<script setup lang="ts">
import { Button } from '@/components/ui/button'
import type { Component } from 'vue'

export interface SocialItem {
    social: string
    name: string
    icon: Component
}

defineProps<SocialItem>()

const redirect = (social: string) => {
    const width = 600
    const height = 600
    const left = window.screen.width / 2 - width / 2
    const top = window.screen.height / 2 - height / 2

    window.open(
        route('socialite.redirect', social),
        'socialite',
        `width=${width},height=${height},left=${left},top=${top}`,
    )
}
</script>

<template>
    <Button as-child variant="outline">
        <a
            :href="route('socialite.redirect', social)"
            @click.prevent="redirect(social)"
            class="flex gap-2 items-center"
        >
            <component :is="icon" class="size-5" />
            {{ name }}
        </a>
    </Button>
</template>

<script setup lang="ts">
import FacebookIcon from '@/components/Icons/FacebookIcon.vue'
import GoogleIcon from '@/components/Icons/GoogleIcon.vue'
import ZaloIcon from '@/components/Icons/ZaloIcon.vue'
import type { SocialItem as SocialItemType } from '@/components/SocialLogin/SocialItem.vue'
import SocialItem from '@/components/SocialLogin/SocialItem.vue'
import { Separator } from '@/components/ui/separator'
import { usePage } from '@inertiajs/vue3'
import { computed } from 'vue'

const socials: SocialItemType[] = [
    { social: 'google', icon: GoogleIcon, name: 'Google' },
    { social: 'facebook', icon: FacebookIcon, name: 'Facebook' },
    { social: 'zalo', icon: ZaloIcon, name: '<PERSON>alo' },
]

const socialLogin: Record<string, boolean> = usePage().props.app.social_login
const filteredSocials = computed(() =>
    socials.filter((social) => socialLogin[social.social]),
)
</script>

<template>
    <div v-if="filteredSocials.length" class="mt-6 w-full">
        <Separator label="Hoặc đăng nhập bằng" />

        <div
            class="mt-6 grid gap-2 sm:gap-4"
            :class="{
                'grid-cols-1': filteredSocials.length === 1,
                'grid-cols-2': filteredSocials.length === 2,
                'grid-cols-3': filteredSocials.length === 3,
            }"
        >
            <SocialItem
                v-for="social in filteredSocials"
                :key="social.social"
                :social="social.social"
                :icon="social.icon"
                :name="social.name"
            />
        </div>
    </div>
</template>

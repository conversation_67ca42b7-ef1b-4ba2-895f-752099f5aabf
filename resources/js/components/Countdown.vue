<script setup lang="ts">
defineProps<{
    time: number
}>()

const transformSlotProps = (props: Record<string, number>) => {
    const formattedProps: Record<string, string> = {}

    Object.entries(props).forEach(([key, value]) => {
        formattedProps[key] = value < 10 ? `0${value}` : String(value)
    })

    return formattedProps
}
</script>

<template>
    <vue-countdown
        :time="time"
        v-slot="{ days, hours, minutes, seconds }"
        :transform="transformSlotProps"
    >
        <slot
            :days="days"
            :hours="hours"
            :minutes="minutes"
            :seconds="seconds"
        />
    </vue-countdown>
</template>

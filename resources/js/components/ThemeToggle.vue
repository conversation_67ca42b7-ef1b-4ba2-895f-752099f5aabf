<script setup lang="ts">
import ComputerDesktopIcon from '@/components/Icons/ComputerDesktopIcon.vue'
import MoonIcon from '@/components/Icons/MoonIcon.vue'
import SunIcon from '@/components/Icons/SunIcon.vue'
import { Button } from '@/components/ui/button'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useColorMode } from '@vueuse/core'

const mode = useColorMode()
</script>

<template>
    <DropdownMenu>
        <DropdownMenuTrigger as-child>
            <Button variant="link">
                <SunIcon
                    class="h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"
                />
                <MoonIcon
                    class="absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"
                />
                <span class="sr-only">Ch<PERSON> độ</span>
            </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
            <DropdownMenuItem
                @click="mode = 'light'"
                class="flex items-center gap-1.5 cursor-pointer"
            >
                <SunIcon class="size-5" />
                Sáng
            </DropdownMenuItem>
            <DropdownMenuItem
                @click="mode = 'dark'"
                class="flex items-center gap-1.5 cursor-pointer"
            >
                <MoonIcon class="size-5" />
                Tối
            </DropdownMenuItem>
            <DropdownMenuItem
                @click="mode = 'auto'"
                class="flex items-center gap-1.5 cursor-pointer"
            >
                <ComputerDesktopIcon class="size-5" />
                Hệ thống
            </DropdownMenuItem>
        </DropdownMenuContent>
    </DropdownMenu>
</template>

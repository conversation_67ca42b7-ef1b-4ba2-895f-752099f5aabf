<script setup lang="ts">
export interface Depositor {
    user_avatar: string
    user_display_name: string
    amount: number
}

defineProps<{
    rank: number
    depositor: Depositor
}>()
</script>

<template>
    <div class="flex items-center justify-between">
        <div class="flex items-center gap-2">
            <div
                class="shrink-0 rounded-full font-medium size-8 bg-primary/10 text-primary flex items-center justify-center"
            >
                {{ rank }}
            </div>
            <div class="flex items-center gap-1">
                <img
                    v-lazy="depositor.user_avatar"
                    :alt="depositor.user_display_name"
                    class="size-6 rounded-full block lg:hidden"
                />
                <div>
                    <h4 class="font-medium">
                        {{ depositor.user_display_name }}
                    </h4>
                </div>
            </div>
        </div>
        <div
            class="bg-primary text-primary-foreground font-medium rounded-lg px-2 py-0.5"
        >
            {{ depositor.amount }}
        </div>
    </div>
</template>

<script setup lang="ts">
import TrophyIcon from '@/components/Icons/TrophyIcon.vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import type { Depositor } from './TopDepositorItem.vue'
import TopDepositorItem from './TopDepositorItem.vue'

defineProps<{
    topDepositors: Depositor[]
}>()
</script>

<template>
    <Card>
        <CardHeader class="p-3">
            <CardTitle class="flex items-center gap-2 text-lg">
                <TrophyIcon class="size-6" />
                Top nạp tiền
            </CardTitle>
        </CardHeader>

        <CardContent class="p-3">
            <div class="grid grid-cols-1 gap-2 sm:gap-4 space-y-1">
                <TopDepositorItem
                    v-for="(depositor, index) in topDepositors"
                    :key="index"
                    :depositor="depositor"
                    :rank="index + 1"
                />
            </div>
        </CardContent>
    </Card>
</template>

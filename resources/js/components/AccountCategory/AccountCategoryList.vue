<script setup lang="ts">
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { AccountCategory } from '@/types'
import { Link } from '@inertiajs/vue3'
import { computed } from 'vue'

const props = defineProps<{
    categories: AccountCategory[]
}>()

const getTotalRemainingAccounts = (category: AccountCategory): number => {
    let total = parseInt(category.remaining_accounts_count?.toString() || '0')

    if (category.children && category.children.length > 0) {
        for (const child of category.children) {
            total += getTotalRemainingAccounts(child)
        }
    }

    return total
}

const getTotalSoldAccounts = (category: AccountCategory): number => {
    let total = parseInt(category.sold_accounts_count?.toString() || '0')

    if (category.children && category.children.length > 0) {
        for (const child of category.children) {
            total += getTotalSoldAccounts(child)
        }
    }

    return total
}

const getCategoryStats = computed(() => {
    return props.categories.map((category) => ({
        ...category,
        totalRemainingAccounts: getTotalRemainingAccounts(category),
        totalSoldAccounts: getTotalSoldAccounts(category),
    }))
})
</script>

<template>
    <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4">
        <Card v-for="(category, index) in getCategoryStats" :key="index">
            <Link :href="route('categories.show', category.slug)">
                <img
                    v-lazy="category.image"
                    :alt="category.name"
                    class="transition-all aspect-video w-full object-cover rounded-t-lg"
                />
            </Link>
            <div class="text-center p-3">
                <h4
                    class="font-medium truncate group-hover:text-primary transition-all mb-2"
                >
                    <Link
                        :href="route('categories.show', category.slug)"
                        :title="category.name"
                        class="hover:text-primary transition-all"
                    >
                        {{ category.name }}
                    </Link>
                </h4>
                <div
                    class="flex items-center justify-center gap-1 text-sm text-muted-foreground flex-wrap"
                >
                    <div class="flex items-center gap-1">
                        <span class="font-medium">
                            {{ category.totalRemainingAccounts }}
                        </span>
                        <span>còn lại</span>
                    </div>
                    <div class="w-px h-3 bg-border"></div>
                    <div class="flex items-center gap-1">
                        <span class="font-medium">
                            {{ category.totalSoldAccounts }}
                        </span>
                        <span>đã bán</span>
                    </div>
                </div>
                <div class="grid mt-4">
                    <Button as-child class="w-full">
                        <Link :href="route('categories.show', category.slug)">
                            Xem ngay
                        </Link>
                    </Button>
                </div>
            </div>
        </Card>
    </div>
</template>

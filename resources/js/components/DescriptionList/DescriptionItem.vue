<script setup lang="ts">
withDefaults(
    defineProps<{
        inline?: boolean
        label: string
        value?: string | number
    }>(),
    {
        inline: false,
    },
)
</script>

<template>
    <div
        class="text-sm first:pt-0 last:pb-0 py-4 sm:gap-4"
        :class="{
            'items-center grid grid-cols-3': inline,
            'sm:grid sm:grid-cols-3': !inline,
        }"
    >
        <dt class="font-medium leading-6">{{ label }}</dt>
        <dd
            class="mt-1 flex leading-6 sm:mt-0"
            :class="{ 'col-span-2': inline, 'sm:col-span-2': !inline }"
        >
            <slot name="value" />
            <span class="grow" v-if="value" v-html="value" />
        </dd>
    </div>
</template>

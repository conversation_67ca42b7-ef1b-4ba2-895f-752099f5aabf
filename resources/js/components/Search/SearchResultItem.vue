<script setup lang="ts">
import { Link } from '@inertiajs/vue3'
import { computed } from 'vue'

export interface SearchResult {
    id: string
    title: string
    description: string
    type: string
    url: string
    image?: string | null
    category?: string | null
    weight: number
    metadata?: Record<string, any>
}

interface Props {
    result: SearchResult
    isActive?: boolean
    searchQuery?: string
    dataRenderIndex?: number
}

interface Emits {
    (e: 'click', result: SearchResult): void
}

const props = withDefaults(defineProps<Props>(), {
    isActive: false,
    searchQuery: '',
})

const emit = defineEmits<Emits>()

const typeIcon = computed(() => {
    switch (props.result.type) {
        case 'account':
            return '🎮'
        case 'game':
            return '🎯'
        case 'post':
            return '📰'
        case 'category':
            return '📁'
        case 'navigation':
            return '🔗'
        default:
            return '📄'
    }
})

const typeLabel = computed(() => {
    switch (props.result.type) {
        case 'account':
            return 'Tài k<PERSON>n'
        case 'game':
            return 'Game'
        case 'post':
            return 'Bài viết'
        case 'category':
            return 'Danh mục'
        case 'navigation':
            return 'Điều hướng'
        default:
            return 'Khác'
    }
})

const highlightText = (text: string, query: string): string => {
    if (!query.trim()) return text

    const regex = new RegExp(
        `(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`,
        'gi',
    )
    return text.replace(
        regex,
        '<mark class="bg-yellow-200 dark:bg-yellow-800 px-0.5 rounded">$1</mark>',
    )
}

const handleClick = () => {
    emit('click', props.result)
}
</script>

<template>
    <Link
        :href="result.url"
        class="flex items-start gap-3 p-3 hover:bg-muted/50 transition-colors cursor-pointer border-l-2 border-transparent w-full overflow-hidden"
        :class="{
            'bg-muted border-l-primary': isActive,
        }"
        :data-render-index="dataRenderIndex"
        @click="handleClick"
    >
        <div
            class="flex-shrink-0 w-10 h-10 rounded-md overflow-hidden bg-muted flex items-center justify-center"
        >
            <img
                v-if="result.image"
                :src="result.image"
                :alt="result.title"
                class="w-full h-full object-cover"
                loading="lazy"
            />
            <span v-else class="text-lg">{{ typeIcon }}</span>
        </div>

        <div class="flex-1 min-w-0 overflow-hidden">
            <div class="flex items-start gap-2 mb-1">
                <h3
                    class="font-medium text-sm text-foreground line-clamp-1 flex-1 min-w-0"
                    v-html="highlightText(result.title, searchQuery)"
                />
                <span
                    class="text-xs text-muted-foreground bg-muted px-1.5 py-0.5 rounded-full flex-shrink-0 whitespace-nowrap"
                >
                    {{ typeLabel }}
                </span>
            </div>

            <p
                class="text-xs text-muted-foreground line-clamp-2 break-words overflow-hidden"
                v-html="highlightText(result.description, searchQuery)"
            />

            <div v-if="result.category" class="mt-1">
                <span class="text-xs text-muted-foreground truncate block">
                    {{ result.category }}
                </span>
            </div>
        </div>
    </Link>
</template>

<style scoped>
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
}

.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-word;
}
</style>

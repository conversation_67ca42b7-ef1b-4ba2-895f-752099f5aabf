<script setup lang="ts">
import MagnifyingGlassIcon from '@/components/Icons/MagnifyingGlassIcon.vue'
import XMarkIcon from '@/components/Icons/XMarkIcon.vue'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { nextTick, ref, watch } from 'vue'

interface Props {
    modelValue?: string
    placeholder?: string
    loading?: boolean
    disabled?: boolean
}

interface Emits {
    (e: 'update:modelValue', value: string): void
    (e: 'search', value: string): void
    (e: 'clear'): void
    (e: 'focus'): void
    (e: 'blur'): void
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    placeholder: 'Tìm kiếm...',
    loading: false,
    disabled: false,
})

const emit = defineEmits<Emits>()

const inputRef = ref<InstanceType<typeof Input>>()
const localValue = ref(props.modelValue)

watch(
    () => props.modelValue,
    (newValue) => {
        localValue.value = newValue
    },
)

watch(localValue, (newValue) => {
    emit('update:modelValue', newValue)
})

const handleInput = (value: string | number) => {
    const stringValue = String(value)
    localValue.value = stringValue
    emit('search', stringValue)
}

const handleClear = () => {
    localValue.value = ''
    emit('update:modelValue', '')
    emit('clear')
    nextTick(() => {
        inputRef.value?.focus()
    })
}

const handleFocus = () => {
    emit('focus')
}

const handleBlur = () => {
    emit('blur')
}

const focus = () => {
    inputRef.value?.focus()
}

defineExpose({
    focus,
})
</script>

<template>
    <div class="relative">
        <div class="relative">
            <MagnifyingGlassIcon
                class="absolute left-3 top-1/2 transform -translate-y-1/2 size-4 text-muted-foreground"
                :class="{ 'animate-pulse': loading }"
            />
            <Input
                ref="inputRef"
                v-model="localValue"
                :placeholder="placeholder"
                :disabled="disabled || loading"
                class="pl-10 pr-10"
                @update:modelValue="handleInput"
                @focus="handleFocus"
                @blur="handleBlur"
            />
            <Button
                v-if="localValue"
                variant="ghost"
                size="sm"
                class="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 hover:bg-muted"
                @click="handleClear"
            >
                <XMarkIcon class="size-4" />
            </Button>
        </div>
    </div>
</template>

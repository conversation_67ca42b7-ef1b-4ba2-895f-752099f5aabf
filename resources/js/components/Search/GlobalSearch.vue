<script setup lang="ts">
import MagnifyingGlassIcon from '@/components/Icons/MagnifyingGlassIcon.vue'
import { Button } from '@/components/ui/button'
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import { useDebounceFn } from '@vueuse/core'
import axios from 'axios'
import { nextTick, onMounted, onUnmounted, ref, watch } from 'vue'
import SearchInput from './SearchInput.vue'
import { type SearchResult } from './SearchResultItem.vue'
import SearchResults from './SearchResults.vue'

interface Props {
    placeholder?: string
    limit?: number
}

const props = withDefaults(defineProps<Props>(), {
    placeholder: 'Tìm kiếm...',
    limit: 20,
})

const isOpen = ref(false)
const query = ref('')
const results = ref<SearchResult[]>([])
const loading = ref(false)
const activeIndex = ref(-1)
const searchInputRef = ref<InstanceType<typeof SearchInput>>()
const searchResultsRef = ref<InstanceType<typeof SearchResults>>()

const debouncedSearch = useDebounceFn(async (searchQuery: string) => {
    if (!searchQuery.trim()) {
        results.value = []
        loading.value = false
        return
    }

    loading.value = true

    try {
        const response = await axios.get(route('api.search'), {
            params: {
                query: searchQuery,
                limit: props.limit,
            },
        })

        if (response.data.success) {
            results.value = response.data.data.results
        } else {
            results.value = []
        }
    } catch (error) {
        results.value = []
    } finally {
        loading.value = false
        nextTick(() => {
            if (isOpen.value) {
                searchInputRef.value?.focus()
            }
        })
    }
}, 300)

const handleSearch = (searchQuery: string) => {
    query.value = searchQuery
    debouncedSearch(searchQuery)
}

const handleClear = () => {
    query.value = ''
    results.value = []
    activeIndex.value = -1
}

const openSearch = () => {
    isOpen.value = true
    nextTick(() => {
        searchInputRef.value?.focus()
    })
}

const closeSearch = () => {
    isOpen.value = false
    handleClear()
}

const handleResultClick = (_result: SearchResult) => {
    closeSearch()
}

const scrollToActiveItem = () => {
    if (activeIndex.value < 0 || !searchResultsRef.value) return

    nextTick(() => {
        searchResultsRef.value?.scrollToActiveItem()
    })
}

const handleKeyDown = (event: KeyboardEvent) => {
    if (!isOpen.value) return

    switch (event.key) {
        case 'Escape':
            event.preventDefault()
            closeSearch()
            break
        case 'ArrowDown':
            event.preventDefault()
            if (results.value.length === 0) break
            const flattenedLength =
                searchResultsRef.value?.flattenedResults?.length ||
                results.value.length
            const maxIndex = flattenedLength - 1
            if (activeIndex.value < 0) {
                activeIndex.value = 0
            } else if (activeIndex.value >= maxIndex) {
                activeIndex.value = maxIndex
            } else {
                activeIndex.value = activeIndex.value + 1
            }
            scrollToActiveItem()
            break
        case 'ArrowUp':
            event.preventDefault()
            if (results.value.length === 0) break
            if (activeIndex.value <= 0) {
                activeIndex.value = 0
            } else {
                activeIndex.value = activeIndex.value - 1
            }
            scrollToActiveItem()
            break
        case 'Enter':
            event.preventDefault()
            const flattenedResults =
                searchResultsRef.value?.flattenedResults || results.value
            if (activeIndex.value >= 0 && flattenedResults[activeIndex.value]) {
                const result = flattenedResults[activeIndex.value]
                window.location.href = result.url
                closeSearch()
            }
            break
    }
}

const handleGlobalKeyDown = (event: KeyboardEvent) => {
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault()
        openSearch()
    }
}

onMounted(() => {
    document.addEventListener('keydown', handleGlobalKeyDown)
})

onUnmounted(() => {
    document.removeEventListener('keydown', handleGlobalKeyDown)
})

watch(isOpen, (open) => {
    if (open) {
        document.addEventListener('keydown', handleKeyDown)
    } else {
        document.removeEventListener('keydown', handleKeyDown)
    }
})

defineExpose({
    openSearch,
})
</script>

<template>
    <div>
        <Button
            variant="outline"
            class="relative w-full justify-start text-sm text-muted-foreground sm:pr-12 md:w-40 lg:w-64"
            @click="openSearch"
        >
            <MagnifyingGlassIcon class="mr-2 h-4 w-4" />
            <span class="hidden lg:inline-flex">{{ placeholder }}</span>
            <span class="inline-flex lg:hidden">Tìm kiếm...</span>
            <kbd
                class="pointer-events-none absolute right-1.5 top-[calc(50%-11px)] hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex"
            >
                <span class="text-xs">⌘</span>K
            </kbd>
        </Button>

        <Dialog v-model:open="isOpen">
            <DialogContent
                class="max-w-2xl w-[95vw] sm:w-full p-0 [&>button]:hidden overflow-hidden"
            >
                <DialogHeader class="px-4 pt-4 pb-2">
                    <DialogTitle class="sr-only">Tìm kiếm</DialogTitle>
                    <SearchInput
                        ref="searchInputRef"
                        v-model="query"
                        :placeholder="placeholder"
                        :loading="loading"
                        @search="handleSearch"
                        @clear="handleClear"
                    />
                </DialogHeader>

                <div class="border-t">
                    <SearchResults
                        ref="searchResultsRef"
                        :results="results"
                        :loading="loading"
                        :query="query"
                        :active-index="activeIndex"
                        @result-click="handleResultClick"
                    />
                </div>

                <div
                    class="border-t px-4 py-2 text-xs text-muted-foreground bg-muted/50"
                >
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-4">
                            <span class="flex items-center gap-1">
                                <kbd
                                    class="px-1.5 py-0.5 text-[10px] bg-background border rounded"
                                >
                                    ↑↓
                                </kbd>
                                để điều hướng
                            </span>
                            <span class="flex items-center gap-1">
                                <kbd
                                    class="px-1.5 py-0.5 text-[10px] bg-background border rounded"
                                >
                                    ↵
                                </kbd>
                                để chọn
                            </span>
                            <span class="flex items-center gap-1">
                                <kbd
                                    class="px-1.5 py-0.5 text-[10px] bg-background border rounded"
                                >
                                    esc
                                </kbd>
                                để đóng
                            </span>
                        </div>
                    </div>
                </div>
            </DialogContent>
        </Dialog>
    </div>
</template>

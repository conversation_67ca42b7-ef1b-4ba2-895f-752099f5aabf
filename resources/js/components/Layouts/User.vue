<script setup lang="ts">
import { buttonVariants } from '@/components/ui/button'
import { cn } from '@/lib/utils'
import { Link, usePage } from '@inertiajs/vue3'
import { WaypointsIcon } from 'lucide-vue-next'
import type { Component as ComponentType } from 'vue'
import { shallowRef } from 'vue'
import ArrowDownTrayIcon from '../Icons/ArrowDownTrayIcon.vue'
import ArrowLeftEndOnRectangleIcon from '../Icons/ArrowLeftEndOnRectangleIcon.vue'
import ArrowPathIcon from '../Icons/ArrowPathIcon.vue'
import CreditCardIcon from '../Icons/CreditCardIcon.vue'
import LockClosedIcon from '../Icons/LockClosedIcon.vue'
import RectangleGroupIcon from '../Icons/RectangleGroupIcon.vue'
import UserCircleIcon from '../Icons/UserCircleIcon.vue'
import Master from './Master.vue'

const menu = shallowRef<
    {
        icon: ComponentType
        title: string
        route: string
        attributes?: Record<string, unknown>
    }[]
>([
    {
        icon: UserCircleIcon,
        title: 'Thông tin tài khoản',
        route: 'user.profile',
    },
    {
        icon: LockClosedIcon,
        title: 'Đổi mật khẩu',
        route: 'user.change-password',
    },
    {
        icon: CreditCardIcon,
        title: 'Nạp tiền',
        route: 'user.deposit.index',
    },
    {
        icon: CreditCardIcon,
        title: 'Lịch sử nạp thẻ',
        route: 'user.deposit.recharges.index',
    },
    {
        icon: RectangleGroupIcon,
        title: 'Tài khoản đã mua',
        route: 'user.purchased-accounts.index',
    },
    {
        icon: ArrowPathIcon,
        title: 'Lịch sử giao dịch',
        route: 'user.transactions',
    },
    {
        icon: ArrowDownTrayIcon,
        title: 'Rút vật phẩm',
        route: 'user.withdraw-items.index',
    },
    {
        icon: ArrowPathIcon,
        title: 'Lịch sử vòng quay',
        route: 'user.wheel-spins',
    },
    {
        icon: WaypointsIcon,
        title: 'Tiếp thị liên kết',
        route: 'user.affiliate.index',
    },
    {
        icon: ArrowLeftEndOnRectangleIcon,
        title: 'Đăng xuất',
        route: 'logout',
        attributes: {
            method: 'post',
            as: 'button',
        },
    },
])

const affiliate = usePage().props.auth.affiliate
</script>

<template>
    <Master>
        <div class="container py-4">
            <div
                class="grid grid-cols-1 sm:grid-cols-5 lg:grid-cols-4 gap-y-4 sm:gap-x-4"
            >
                <div class="sm:col-span-5 lg:col-span-1">
                    <nav class="flex flex-col space-y-1">
                        <template v-for="(item, index) in menu" :key="index">
                            <a
                                v-if="
                                    item.route === 'user.affiliate.index' &&
                                    affiliate?.status === 'approved'
                                "
                                :href="route(item.route)"
                                :class="
                                    cn(
                                        buttonVariants({ variant: 'ghost' }),
                                        route().current(item.route)
                                            ? 'bg-muted hover:bg-muted'
                                            : 'hover:bg-transparent hover:underline',
                                        'w-full justify-start gap-2',
                                    )
                                "
                                v-bind="item.attributes"
                            >
                                <component :is="item.icon" class="size-5" />
                                {{ item.title }}
                            </a>
                            <Link
                                v-else
                                :href="route(item.route)"
                                :class="
                                    cn(
                                        buttonVariants({ variant: 'ghost' }),
                                        route().current(item.route)
                                            ? 'bg-muted hover:bg-muted'
                                            : 'hover:bg-transparent hover:underline',
                                        'w-full justify-start gap-2',
                                    )
                                "
                                v-bind="item.attributes"
                            >
                                <component :is="item.icon" class="size-5" />
                                {{ item.title }}
                            </Link>
                        </template>
                    </nav>
                </div>
                <div class="sm:col-span-5 lg:col-span-3">
                    <slot />
                </div>
            </div>
        </div>
    </Master>
</template>

<script setup lang="ts">
import ClipboardDocumentCheck from '@/components/Icons/ClipboardDocumentCheck.vue'
import ClipboardIcon from '@/components/Icons/ClipboardIcon.vue'
import { ref } from 'vue'

const props = defineProps<{
    content: string
}>()

const isCopied = ref<boolean>(false)

const copyToClipboard = async () => {
    isCopied.value = true

    if (!navigator.clipboard) {
        const el = document.createElement('textarea')
        el.value = props.content
        document.body.appendChild(el)
        el.select()
        document.execCommand('copy')
        document.body.removeChild(el)
    } else {
        await navigator.clipboard.writeText(props.content)
    }

    setTimeout(() => {
        isCopied.value = false
    }, 1000)
}
</script>

<template>
    <button
        v-tippy="{
            content: isCopied ? 'Đã sao chép' : 'Sao chép',
            hideOnClick: false,
        }"
        type="button"
        class="hover:text-primary transition-all"
        @click="copyToClipboard"
    >
        <component
            :is="isCopied ? ClipboardDocumentCheck : ClipboardIcon"
            class="size-5"
        />
    </button>
</template>

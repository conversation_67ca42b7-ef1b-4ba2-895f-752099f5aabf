<script setup lang="ts">
import BoltIcon from '@/components/Icons/BoltIcon.vue'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Carousel,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel'
import { formatCurrency } from '@/lib/utils'
import { FlashSale } from '@/types'
import { Link } from '@inertiajs/vue3'
import { computed } from 'vue'
import Countdown from '../Countdown.vue'

const props = defineProps<{
    flashSale: FlashSale
}>()

const time = computed(
    () => Number(new Date(props.flashSale.end_at)) - Number(new Date()),
)
</script>

<template>
    <Card>
        <CardHeader
            class="flex-row flex-wrap justify-between gap-y-4 items-center p-3 bg-linear-to-r text-white rounded-t-lg from-primary to-pink-500"
        >
            <CardTitle class="flex items-center gap-2 text-lg sm:text-xl">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="shrink-0 size-6 sm:size-8"
                >
                    <path
                        fill-rule="evenodd"
                        d="M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z"
                        clip-rule="evenodd"
                    />
                </svg>
                Flash Sale
            </CardTitle>
            <Countdown
                :time="time"
                v-slot="{ days, hours, minutes, seconds }"
                class="flex items-center gap-2"
            >
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ days }}
                </div>
                <div class="text-xl sm:text-2xl font-bold text-white">:</div>
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ hours }}
                </div>
                <div class="text-xl sm:text-2xl font-bold text-white">:</div>
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ minutes }}
                </div>
                <div class="text-xl sm:text-2xl font-bold text-white">:</div>
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ seconds }}
                </div>
            </Countdown>
        </CardHeader>
        <CardContent class="p-3">
            <Carousel>
                <CarouselContent>
                    <CarouselItem
                        v-for="(item, index) in flashSale.accounts"
                        :key="index"
                        class="basis-1/2 md:basis-1/3 lg:basis-1/4"
                    >
                        <Card class="border-primary border-2">
                            <div class="relative">
                                <Link
                                    :href="
                                        route('accounts.show', item.account.id)
                                    "
                                >
                                    <img
                                        v-lazy="item.account.image"
                                        :alt="`Tài khoản ${item.account.category.name} #${item.account.id}`"
                                        class="aspect-video object-cover w-full h-full rounded-t-lg"
                                    />
                                </Link>

                                <span
                                    class="absolute top-2 start-2 text-xs bg-primary/90 text-primary-foreground font-medium px-2 py-0.5 rounded-lg"
                                >
                                    {{ item.account.category.name }} #{{
                                        item.account.id
                                    }}
                                </span>
                                <div
                                    v-if="item.account.description"
                                    class="absolute bottom-0 w-full truncate text-white bg-gray-950/40 px-3 py-1 text-sm"
                                    :title="item.account.description"
                                >
                                    {{ item.account.description }}
                                </div>
                            </div>
                            <div class="p-3">
                                <dl class="grid gap-2">
                                    <div
                                        v-for="attribute in item.account
                                            .attributes"
                                        :key="attribute.id"
                                        class="flex justify-between gap-8"
                                    >
                                        <dt
                                            class="text-xs sm:text-sm text-nowrap"
                                        >
                                            {{ attribute.attribute_set.name }}
                                        </dt>
                                        <dd
                                            class="text-xs sm:text-sm font-medium truncate"
                                        >
                                            {{ attribute.name }}
                                        </dd>
                                    </div>
                                </dl>
                                <div
                                    class="flex justify-between items-end mt-3"
                                >
                                    <div>
                                        <h4
                                            class="text-primary font-semibold text-lg mb-1"
                                        >
                                            {{ formatCurrency(item.price) }}
                                        </h4>
                                        <div
                                            class="text-sm line-through text-gray-500 dark:text-gray-400"
                                        >
                                            {{
                                                formatCurrency(
                                                    item.account.original_price,
                                                )
                                            }}
                                        </div>
                                    </div>
                                    <div
                                        class="bg-primary text-primary-foreground rounded-lg p-1.5 flex items-center text-sm gap-1"
                                    >
                                        <BoltIcon class="size-4" />
                                        {{ item.discount_percent }}%
                                    </div>
                                </div>
                            </div>
                            <Button as-child class="flex rounded-t-none">
                                <Link
                                    :href="
                                        route('accounts.show', item.account.id)
                                    "
                                >
                                    Xem chi tiết
                                </Link>
                            </Button>
                        </Card>
                    </CarouselItem>
                </CarouselContent>
            </Carousel>
        </CardContent>
    </Card>
</template>

<script setup lang="ts">
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card'
import {
    Carousel,
    CarouselContent,
    CarouselItem,
} from '@/components/ui/carousel'
import { FlashSale } from '@/types'
import { computed } from 'vue'
import Countdown from '../Countdown.vue'
import FlashSaleAccountItem from './FlashSaleAccountItem.vue'

const props = defineProps<{
    flashSale: FlashSale
}>()

const time = computed(
    () => Number(new Date(props.flashSale.end_at)) - Number(new Date()),
)
</script>

<template>
    <Card>
        <CardHeader
            class="flex-row flex-wrap justify-between gap-y-4 items-center p-3 bg-linear-to-r text-white rounded-t-lg from-primary to-pink-500"
        >
            <CardTitle class="flex items-center gap-2 text-lg sm:text-xl">
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                    class="shrink-0 size-6 sm:size-8"
                >
                    <path
                        fill-rule="evenodd"
                        d="M12.963 2.286a.75.75 0 0 0-1.071-.136 9.742 9.742 0 0 0-3.539 6.176 7.547 7.547 0 0 1-1.705-1.715.75.75 0 0 0-1.152-.082A9 9 0 1 0 15.68 4.534a7.46 7.46 0 0 1-2.717-2.248ZM15.75 14.25a3.75 3.75 0 1 1-7.313-1.172c.628.465 1.35.81 2.133 1a5.99 5.99 0 0 1 1.925-3.546 3.75 3.75 0 0 1 3.255 3.718Z"
                        clip-rule="evenodd"
                    />
                </svg>
                Flash Sale
            </CardTitle>
            <Countdown
                :time="time"
                v-slot="{ days, hours, minutes, seconds }"
                class="flex items-center gap-2"
            >
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ days }}
                </div>
                <div class="text-xl sm:text-2xl font-bold text-white">:</div>
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ hours }}
                </div>
                <div class="text-xl sm:text-2xl font-bold text-white">:</div>
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ minutes }}
                </div>
                <div class="text-xl sm:text-2xl font-bold text-white">:</div>
                <div class="text-xl sm:text-2xl font-bold text-white">
                    {{ seconds }}
                </div>
            </Countdown>
        </CardHeader>
        <CardContent class="p-3">
            <Carousel>
                <CarouselContent>
                    <CarouselItem
                        v-for="(item, index) in flashSale.accounts"
                        :key="index"
                        class="basis-1/2 md:basis-1/3 lg:basis-1/4"
                    >
                        <FlashSaleAccountItem :item="item" />
                    </CarouselItem>
                </CarouselContent>
            </Carousel>
        </CardContent>
    </Card>
</template>

<script setup lang="ts">
import BoltIcon from '@/components/Icons/BoltIcon.vue'
import { Button } from '@/components/ui/button'
import { Card } from '@/components/ui/card'
import { formatCurrency } from '@/lib/utils'
import type { FlashSale } from '@/types'
import { Link } from '@inertiajs/vue3'

interface Props {
    item: NonNullable<FlashSale['accounts']>[0]
    showButton?: boolean
    buttonText?: string
    compact?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    showButton: true,
    buttonText: 'Xem chi tiết',
    compact: false,
})
</script>

<template>
    <Card class="border-primary border-2 overflow-hidden">
        <div class="relative">
            <Link :href="route('accounts.show', item.account.id)">
                <img
                    v-lazy="item.account.image"
                    :alt="`Tài khoản ${item.account.category.name} #${item.account.id}`"
                    class="aspect-video object-fill w-full h-full"
                />
            </Link>

            <span
                class="absolute top-2 start-2 text-xs bg-primary/90 text-primary-foreground font-medium px-2 py-0.5 rounded-lg"
            >
                {{ item.account.category.name }} #{{ item.account.id }}
            </span>

            <div
                v-if="item.account.description && !compact"
                class="absolute bottom-0 w-full truncate text-white bg-gray-950/40 px-3 py-1 text-sm"
                :title="item.account.description"
            >
                {{ item.account.description }}
            </div>
        </div>

        <div class="p-3">
            <dl
                v-if="!compact && item.account.attributes?.length"
                class="grid gap-2 mb-3"
            >
                <div
                    v-for="attribute in item.account.attributes"
                    :key="attribute.id"
                    class="flex justify-between gap-2"
                >
                    <dt
                        class="text-xs sm:text-sm text-nowrap text-gray-600 dark:text-gray-400"
                    >
                        {{ attribute.attribute_set.name }}
                    </dt>
                    <dd class="text-xs sm:text-sm font-medium truncate">
                        {{ attribute.name }}
                    </dd>
                </div>
            </dl>

            <div
                class="flex justify-between items-end"
                :class="{ 'mt-3': !compact }"
            >
                <div>
                    <h4 class="text-primary font-semibold text-lg mb-1">
                        {{ formatCurrency(item.price) }}
                    </h4>
                    <div
                        class="text-sm line-through text-gray-500 dark:text-gray-400"
                    >
                        {{ formatCurrency(item.account.original_price) }}
                    </div>
                </div>
                <div
                    class="bg-primary text-primary-foreground rounded-lg p-1.5 flex items-center text-sm gap-1"
                >
                    <BoltIcon class="size-4" />
                    {{ item.discount_percent }}%
                </div>
            </div>
        </div>

        <Button v-if="showButton" as-child class="flex rounded-t-none">
            <Link :href="route('accounts.show', item.account.id)">
                {{ buttonText }}
            </Link>
        </Button>
    </Card>
</template>

<script setup lang="ts">
import {
    B<PERSON><PERSON>rumb,
    BreadcrumbItem,
    BreadcrumbLink,
    BreadcrumbList,
    BreadcrumbSeparator,
} from '@/components/ui/breadcrumb'
import { Link, usePage } from '@inertiajs/vue3'
import { computed } from 'vue'

const breadcrumbs = computed(() => usePage().props.breadcrumbs || [])
</script>

<template>
    <div class="container mt-4" v-if="breadcrumbs.length > 0">
        <Breadcrumb>
            <BreadcrumbList>
                <BreadcrumbItem
                    v-for="(item, index) in breadcrumbs"
                    :key="index"
                >
                    <BreadcrumbLink as-child>
                        <Link :href="item.url" v-if="item.url">
                            {{ item.title }}
                        </Link>
                        <span v-else>{{ item.title }}</span>
                    </BreadcrumbLink>
                    <BreadcrumbSeparator
                        v-if="index !== breadcrumbs.length - 1"
                    />
                </BreadcrumbItem>
            </BreadcrumbList>
        </Breadcrumb>
    </div>
</template>

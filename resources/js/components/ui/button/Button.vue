<script setup lang="ts">
import { cn } from '@/lib/utils'
import { Loader2 } from 'lucide-vue-next'
import { Primitive, type PrimitiveProps } from 'radix-vue'
import type { HTMLAttributes } from 'vue'
import { type ButtonVariants, buttonVariants } from '.'

interface Props extends PrimitiveProps {
    variant?: ButtonVariants['variant']
    size?: ButtonVariants['size']
    class?: HTMLAttributes['class']
    loading?: boolean
    overlay?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    as: 'button',
    loading: false,
    overlay: false,
})
</script>

<template>
    <Primitive
        :as="as"
        :as-child="asChild"
        :class="cn(buttonVariants({ variant, size }), props.class)"
        :disabled="loading"
    >
        <Loader2
            v-if="loading"
            class="w-4 h-4 animate-spin"
            :class="{ 'mr-2': !overlay }"
        />
        <slot v-if="!loading || !overlay" />
    </Primitive>
</template>

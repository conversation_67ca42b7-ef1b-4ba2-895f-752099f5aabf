<script setup lang="ts">
import { cn } from '@/lib/utils'
import type { HTMLAttributes } from 'vue'

const props = defineProps<{
    class?: HTMLAttributes['class']
}>()
</script>

<template>
    <tfoot
        :class="
            cn(
                'border-t bg-muted/50 font-medium last:[&>tr]:border-b-0',
                props.class,
            )
        "
    >
        <slot />
    </tfoot>
</template>

<script setup lang="ts">
import { cn } from '@/lib/utils'
import { computed } from 'vue'

interface Props {
    modelValue?: string
    rows?: number
    placeholder?: string
    disabled?: boolean
    readonly?: boolean
    required?: boolean
    id?: string
    name?: string
    class?: string
}

const props = withDefaults(defineProps<Props>(), {
    modelValue: '',
    rows: 3,
    placeholder: '',
    disabled: false,
    readonly: false,
    required: false,
    id: '',
    name: '',
    class: '',
})

const emit = defineEmits<{
    'update:modelValue': [value: string]
}>()

const updateValue = (event: Event) => {
    const target = event.target as HTMLTextAreaElement
    emit('update:modelValue', target.value)
}

const classes = computed(() => {
    return cn(
        'flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        props.class,
    )
})
</script>

<template>
    <textarea
        :id="id"
        :name="name"
        :rows="rows"
        :placeholder="placeholder"
        :disabled="disabled"
        :readonly="readonly"
        :required="required"
        :value="modelValue"
        @input="updateValue"
        :class="classes"
    />
</template>

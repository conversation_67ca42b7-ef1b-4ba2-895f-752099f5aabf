<script setup lang="ts">
import { usePage } from '@inertiajs/vue3'
import { computed } from 'vue'

interface Ad {
    id: number
    name: string
    description?: string
    position: string
    type: 'html' | 'image' | 'banner' | 'script'
    content: string
    link_url?: string
    is_active: boolean
    sort_order: number
    start_at?: string
    end_at?: string
}

interface Props {
    position: string
}

const props = defineProps<Props>()
const page = usePage()

const ads = computed(() => {
    const adsData = page.props.ads as Record<string, Ad[]>
    return adsData?.[props.position] || []
})
</script>

<template>
    <div
        v-if="ads.length > 0"
        class="widget-container"
        :class="`widget-position-${position}`"
    >
        <div
            v-for="ad in ads"
            :key="ad.id"
            class="widget-item mb-4 rounded-lg overflow-hidden"
            :class="`widget-type-${ad.type} widget-${ad.position}`"
        >
            <div v-if="ad.type === 'html'" v-html="ad.content"></div>

            <div v-else-if="ad.type === 'image'">
                <a
                    v-if="ad.link_url"
                    :href="ad.link_url"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <img
                        v-lazy="ad.content"
                        :alt="ad.name"
                        class="w-full h-auto"
                        loading="lazy"
                    />
                </a>
                <img
                    v-else
                    v-lazy="ad.content"
                    :alt="ad.name"
                    class="w-full h-auto"
                    loading="lazy"
                />
            </div>

            <div v-else-if="ad.type === 'banner'">
                <a
                    v-if="ad.link_url"
                    :href="ad.link_url"
                    target="_blank"
                    rel="noopener noreferrer"
                >
                    <div
                        class="banner-widget w-full h-48 relative overflow-hidden"
                    >
                        <img
                            v-lazy="ad.content"
                            :alt="ad.name"
                            class="w-full h-full object-cover"
                            loading="lazy"
                        />
                    </div>
                </a>
                <div
                    v-else
                    class="banner-widget w-full h-48 relative overflow-hidden"
                >
                    <img
                        v-lazy="ad.content"
                        :alt="ad.name"
                        class="w-full h-full object-cover"
                        loading="lazy"
                    />
                </div>
            </div>

            <div v-else-if="ad.type === 'script'" v-html="ad.content"></div>
        </div>
    </div>
</template>

<style scoped>
@reference "../../css/app.css";

.widget-position-header {
    @apply mb-4;
}

.widget-position-sidebar {
    @apply mb-4;
}

.widget-position-footer {
    @apply mt-8;
}

.widget-position-home_banner {
    @apply my-8;
}

.widget-position-account_list {
    @apply my-4;
}

.widget-position-account_detail {
    @apply my-4;
}

.widget-position-blog_sidebar {
    @apply mb-4;
}

.widget-position-blog_detail {
    @apply my-4;
}

.widget-position-user_profile {
    @apply my-4;
}

.widget-position-user_transactions {
    @apply my-4;
}

.widget-position-games_list {
    @apply my-4;
}

.widget-position-wheels_list {
    @apply my-4;
}

.widget-position-mobile_bottom {
    @apply fixed right-0 bottom-0 left-0 z-50 border-t border-gray-200 bg-white p-2 dark:border-gray-700 dark:bg-gray-800;
}

.widget-type-html {
    @apply border border-gray-200 bg-gray-50 p-4 dark:border-gray-600 dark:bg-gray-700;
}

@media (max-width: 768px) {
    .widget-position-sidebar {
        @apply hidden;
    }

    .widget-position-mobile_bottom {
        @apply block;
    }
}

@media (min-width: 769px) {
    .widget-position-mobile_bottom {
        @apply hidden;
    }
}
</style>

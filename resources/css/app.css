@import 'tippy.js/dist/tippy.css' layer(base);

@import 'tailwindcss';

@plugin '@tailwindcss/typography';
@plugin 'tailwindcss-animate';

@variant dark (&:is(.dark *));

@theme {
    --font-sans:
        var(--primary-font), ui-sans-serif, system-ui, sans-serif,
        'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol',
        'Noto Color Emoji';

    --color-border: hsl(var(--border));
    --color-input: hsl(var(--input));
    --color-ring: hsl(var(--ring));
    --color-background: hsl(var(--background));
    --color-foreground: hsl(var(--foreground));

    --color-primary: hsl(var(--primary));
    --color-primary-foreground: hsl(var(--primary-foreground));

    --color-secondary: hsl(var(--secondary));
    --color-secondary-foreground: hsl(var(--secondary-foreground));

    --color-destructive: hsl(var(--destructive));
    --color-destructive-foreground: hsl(var(--destructive-foreground));

    --color-success: hsl(var(--success));
    --color-success-foreground: hsl(var(--success-foreground));

    --color-muted: hsl(var(--muted));
    --color-muted-foreground: hsl(var(--muted-foreground));

    --color-accent: hsl(var(--accent));
    --color-accent-foreground: hsl(var(--accent-foreground));

    --color-popover: hsl(var(--popover));
    --color-popover-foreground: hsl(var(--popover-foreground));

    --color-card: hsl(var(--card));
    --color-card-foreground: hsl(var(--card-foreground));

    --radius-xl: calc(var(--radius) + 4px);
    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --animate-accordion-down: accordion-down 0.2s ease-out;
    --animate-accordion-up: accordion-up 0.2s ease-out;
    --animate-collapsible-down: collapsible-down 0.2s ease-in-out;
    --animate-collapsible-up: collapsible-up 0.2s ease-in-out;

    @keyframes accordion-down {
        from {
            height: 0;
        }
        to {
            height: var(--radix-accordion-content-height);
        }
    }
    @keyframes accordion-up {
        from {
            height: var(--radix-accordion-content-height);
        }
        to {
            height: 0;
        }
    }
    @keyframes collapsible-down {
        from {
            height: 0;
        }
        to {
            height: var(--radix-collapsible-content-height);
        }
    }
    @keyframes collapsible-up {
        from {
            height: var(--radix-collapsible-content-height);
        }
        to {
            height: 0;
        }
    }
}

@utility container {
    margin-inline: auto;
    padding-inline: 2rem;
    @media (width >= theme(--breakpoint-sm)) {
        max-width: none;
    }
    @media (width >= 1400px) {
        max-width: 1400px;
    }
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@layer utilities {
    .container {
        @apply mx-auto px-4 sm:px-6 lg:px-8;
        max-width: var(--appearance-container-max-width, 1280px);
    }
}

@layer base {
    :root {
        --background: 0 0% 100%;
        --foreground: 222.2 84% 4.9%;

        --muted: 210 40% 96.1%;
        --muted-foreground: 215.4 16.3% 46.9%;

        --popover: 0 0% 100%;
        --popover-foreground: 222.2 84% 4.9%;

        --card: 0 0% 100%;
        --card-foreground: 222.2 84% 4.9%;

        --border: 240 5.9% 90%;
        --input: 240 5.9% 90%;

        --primary: var(--primary-color-hsl, 346.8 77.2% 49.8%);
        --primary-foreground: var(--primary-foreground-hsl, 355.7 100% 97.3%);

        --secondary: 210 40% 96.1%;
        --secondary-foreground: 222.2 47.4% 11.2%;

        --accent: 210 40% 96.1%;
        --accent-foreground: 222.2 47.4% 11.2%;

        --success: 120 40% 60%;
        --success-foreground: 120 40% 98%;

        --destructive: 0 84.2% 60.2%;
        --destructive-foreground: 210 40% 98%;

        --ring: var(--primary-color-hsl, 346.8 77.2% 49.8%);

        --radius: 0.5rem;
    }

    .dark {
        --background: 222.2 84% 4.9%;
        --foreground: 210 40% 98%;

        --muted: 217.2 32.6% 17.5%;
        --muted-foreground: 215 20.2% 65.1%;

        --popover: 222.2 84% 4.9%;
        --popover-foreground: 210 40% 98%;

        --card: 222.2 84% 4.9%;
        --card-foreground: 210 40% 98%;

        --border: 240 3.7% 15.9%;
        --input: 240 3.7% 15.9%;

        --primary: var(--primary-color-hsl, 346.8 77.2% 49.8%);
        --primary-foreground: var(--primary-foreground-hsl, 355.7 100% 97.3%);

        --secondary: 217.2 32.6% 17.5%;
        --secondary-foreground: 210 40% 98%;

        --success: 120 40% 30%;
        --success-foreground: 120 40% 90%;

        --accent: 217.2 32.6% 17.5%;
        --accent-foreground: 210 40% 98%;

        --destructive: 0 62.8% 30.6%;
        --destructive-foreground: 210 40% 98%;

        --ring: var(--primary-color-hsl, 346.8 77.2% 49.8%);
    }
}

@layer base {
    * {
        @apply border-border;
    }
    body {
        @apply bg-background text-foreground;
    }
}

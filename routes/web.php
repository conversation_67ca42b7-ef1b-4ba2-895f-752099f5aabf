<?php

declare(strict_types=1);

use App\Http\Controllers\AccountController;
use App\Http\Controllers\Auth\AuthenticatedSessionController;
use App\Http\Controllers\Auth\NewPasswordController;
use App\Http\Controllers\Auth\PasswordResetLinkController;
use App\Http\Controllers\Auth\RegisteredUserController;
use App\Http\Controllers\User\WithdrawItemController;
use App\Http\Controllers\User\AffiliateController;
use App\Http\Controllers\Webhook\SePayController;
use App\Http\Controllers\Webhook\Web2MController;
use App\Http\Controllers\WheelController;
use App\Http\Controllers\User\DepositController;
use App\Http\Controllers\AccountCategoryController;
use App\Http\Controllers\FlashSaleController;
use App\Http\Controllers\GameController;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\Auth\SocialiteController;
use App\Http\Controllers\BlogController;
use App\Http\Controllers\User\PasswordController;
use App\Http\Controllers\User\ProfileController;
use App\Http\Controllers\User\PurchasedAccountController;
use App\Http\Controllers\User\RechargeController;
use App\Http\Controllers\User\TransactionController;
use App\Http\Controllers\User\WheelSpinController;
use App\Http\Controllers\SearchController;
use App\Http\Middleware\EnsureInstalled;
use App\Http\Middleware\EnsureNotInstalled;
use App\Http\Middleware\VerifyWebhookToken;
use App\Http\Middleware\VerifyWeb2MWebhookToken;
use App\Livewire\Installer;
use App\Models\Account;
use App\Models\Category;
use App\Models\AccountCategory;
use App\Models\FlashSale;
use App\Models\Game;
use App\Models\Post;
use App\Models\Wheel;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Support\Facades\Route;
use Tabuna\Breadcrumbs\Trail;
use App\Http\Controllers\SitemapController;

Route::get('install', Installer::class)
    ->name('installer')
    ->middleware(EnsureNotInstalled::class);

Route::middleware(EnsureInstalled::class)->group(function (): void {
    Route::get('/', HomeController::class)
        ->name('home')
        ->breadcrumbs(fn(Trail $trail): Trail => $trail->push('Trang chủ', 'home'));

    // Search API endpoint
    Route::get('api/search', [SearchController::class, 'search'])
        ->name('api.search');

    Route::middleware('guest')->group(function (): void {
        Route::get('register', [RegisteredUserController::class, 'create'])
            ->name('register')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Đăng ký', 'register'));
        Route::post('register', [RegisteredUserController::class, 'store']);

        Route::get('login', [AuthenticatedSessionController::class, 'create'])
            ->name('login')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Đăng nhập', 'login'));
        Route::post('login', [AuthenticatedSessionController::class, 'store']);

        Route::get('forgot-password', [PasswordResetLinkController::class, 'create'])
            ->name('password.request')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Quên mật khẩu', 'password.request'));
        Route::post('forgot-password', [PasswordResetLinkController::class, 'store'])->name('password.email');

        Route::get('reset-password/{token}', [NewPasswordController::class, 'create'])
            ->name('password.reset');
        Route::post('reset-password', [NewPasswordController::class, 'store'])->name('password.store');

        Route::get('auth/{provider}/redirect', [SocialiteController::class, 'redirect'])
            ->name('socialite.redirect')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Chuyển hướng xã hội', 'socialite.redirect'));
        Route::get('auth/{provider}/callback', [SocialiteController::class, 'callback'])
            ->name('socialite.callback')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Xã hội callback', 'socialite.callback'));
    });

    Route::middleware('auth')->group(function (): void {
        Route::post('logout', [AuthenticatedSessionController::class, 'destroy'])->name('logout');

        Route::prefix('user')->name('user.')->group(function (): void {
            Route::get('profile', ProfileController::class)
                ->name('profile')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Thông tin cá nhân', 'user.profile'));
            Route::get('change-password', [PasswordController::class, 'edit'])
                ->name('change-password')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Đổi mật khẩu', 'user.change-password'));
            Route::put('change-password', [PasswordController::class, 'update']);
            Route::get('transactions', TransactionController::class)
                ->name('transactions')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Lịch sử giao dịch', 'user.transactions'));
            Route::get('purchased-accounts', [PurchasedAccountController::class, 'index'])
                ->name('purchased-accounts.index')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Tài khoản đã mua', 'user.purchased-accounts.index'));
            Route::get('purchased-accounts/{account}', [PurchasedAccountController::class, 'show'])
                ->name('purchased-accounts.show')
                ->breadcrumbs(fn(Trail $trail, $account): Trail => $trail->parent('user.purchased-accounts.index')->push("Chi tiết tài khoản #$account", route('user.purchased-accounts.show', $account)));
            Route::prefix('deposit')->name('deposit.')->group(function (): void {
                Route::get('/', [DepositController::class, 'index'])
                    ->name('index')
                    ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Nạp tiền', 'user.deposit.index'));
                Route::post('qr-code', [DepositController::class, 'generateQrCode'])->name('qr-code');
                Route::get('recharges', [RechargeController::class, 'index'])
                    ->name('recharges.index')
                    ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('user.deposit.index')->push('Lịch sử nạp', 'user.deposit.recharges.index'));
                Route::post('recharges', [RechargeController::class, 'store'])->name('recharges.store');
            });
            Route::get('withdraw-items/{gameItem?}', [WithdrawItemController::class, 'index'])
                ->name('withdraw-items.index')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Rút vật phẩm', 'user.withdraw-items.index'));
            Route::post('withdraw-items/{gameItem}', [WithdrawItemController::class, 'store'])->name('withdraw-items.store');
            Route::get('wheel-spins', WheelSpinController::class)
                ->name('wheel-spins')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Lịch sử vòng quay', 'user.wheel-spins'));

            Route::get('affiliate', [AffiliateController::class, 'index'])
                ->name('affiliate.index')
                ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Đăng ký Affiliate', 'user.affiliate.index'));
            Route::post('affiliate', [AffiliateController::class, 'store'])
                ->name('affiliate.store');
        });
    });

    Route::get('vong-quay/{wheel:slug}', [WheelController::class, 'show'])
        ->name('wheels.show')
        ->breadcrumbs(function (Trail $trail, string $wheel): void {
            $wheel = Wheel::query()->where('slug', $wheel)->firstOrFail();
            $trail->parent('home')->push('Vòng quay')->push($wheel->name, route('wheels.show', $wheel));
        });
    Route::post('vong-quay/{wheel:slug}', [WheelController::class, 'spin'])
        ->middleware('auth')
        ->name('wheels.spin');

    Route::prefix('acc')->name('accounts.')->group(function (): void {
        Route::get('{id}', [AccountController::class, 'show'])
            ->name('show')
            ->breadcrumbs(function (Trail $trail, $id): void {
                $account = Account::query()->findOrFail($id);
                $trail->parent('categories.show', $account->category->slug)->push("Tài khoản #$id", route('accounts.show', $id));
            })
            ->whereNumber('id');
        Route::post('{id}/purchase', [AccountController::class, 'purchase'])
            ->middleware('auth')
            ->name('purchase')
            ->whereNumber('id');
        Route::post('{id}/apply-discount', [AccountController::class, 'applyDiscount'])->name('apply-discount');
    });

    Route::prefix('blog')->name('blog.')->group(function (): void {
        Route::get('/', [BlogController::class, 'index'])
            ->name('index')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Tin tức', 'blog.index'));
        Route::get('{slug}', [BlogController::class, 'show'])
            ->name('show')
            ->breadcrumbs(function (Trail $trail, string $slug): void {
                $post = Post::query()->where('slug', $slug)->first();
                if (! $post) {
                    $category = Category::query()->where('slug', $slug)->firstOrFail();
                }
                $trail->parent('blog.index')->push($post->title ?? $category->name, route('blog.show', $slug));
            });
    });

    Route::prefix('games')->name('games.')->group(function (): void {
        Route::get('/', [GameController::class, 'index'])
            ->name('index')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Danh sách game', 'games.index'));
        Route::get('{slug}', [GameController::class, 'show'])
            ->name('show')
            ->breadcrumbs(function (Trail $trail, string $slug): void {
                $game = Game::query()->where('slug', $slug)->firstOrFail();
                $trail->parent('games.index')->push($game->name, route('games.show', $slug));
            });
    });

    Route::prefix('flash-sales')->name('flash-sales.')->group(function (): void {
        Route::get('/', [FlashSaleController::class, 'index'])
            ->name('index')
            ->breadcrumbs(fn(Trail $trail): Trail => $trail->parent('home')->push('Flash Sale', 'flash-sales.index'));
        Route::get('{flashSale}', [FlashSaleController::class, 'show'])
            ->name('show')
            ->breadcrumbs(function (Trail $trail, FlashSale $flashSale): void {
                $trail->parent('flash-sales.index')->push($flashSale->name, route('flash-sales.show', $flashSale));
            });
    });

    Route::withoutMiddleware(VerifyCsrfToken::class)->group(function (): void {
        Route::post('recharge/callback', [RechargeController::class, 'callback'])->name('recharge.callback');

        Route::middleware(VerifyWebhookToken::class)->prefix('webhook')->name('webhook.')->group(function (): void {
            Route::post('sepay', SePayController::class)->name('sepay');
        });

        Route::middleware(VerifyWeb2MWebhookToken::class)->prefix('webhook')->name('webhook.')->group(function (): void {
            Route::post('web2m', Web2MController::class)->name('web2m');
        });
    });

    Route::get('sitemap.xml', [SitemapController::class, 'index'])->name('sitemap');

    Route::get('{slug}', [AccountCategoryController::class, 'show'])
        ->name('categories.show')
        ->breadcrumbs(function (Trail $trail, $slug): void {
            $category = AccountCategory::query()
                ->where('slug', $slug)
                ->with(['game', 'parent'])
                ->firstOrFail();

            $trail->parent('home');

            if ($category->game) {
                $trail->push($category->game->name, route('games.show', $category->game->slug));
            }

            if ($category->parent) {
                $trail->push($category->parent->name, route('categories.show', $category->parent->slug));
            }

            $trail->push($category->name, route('categories.show', $slug));
        });
});

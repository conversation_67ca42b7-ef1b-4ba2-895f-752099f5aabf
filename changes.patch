diff --git a/app/Enums/ServiceOrderStatus.php b/app/Enums/ServiceOrderStatus.php
deleted file mode 100644
index 9f71569..0000000
--- a/app/Enums/ServiceOrderStatus.php
+++ /dev/null
@@ -1,43 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Enums;
-
-use Filament\Support\Contracts\HasColor;
-use Filament\Support\Contracts\HasLabel;
-
-enum ServiceOrderStatus: string implements HasLabel, HasColor
-{
-    case Pending = 'pending';
-
-    case Processing = 'processing';
-
-    case Completed = 'completed';
-
-    case Cancelled = 'cancelled';
-
-    case Refunded = 'refunded';
-
-    public function getLabel(): ?string
-    {
-        return match ($this) {
-            self::Pending => 'Chờ xử lý',
-            self::Processing => 'Đang xử lý',
-            self::Completed => 'Hoàn thành',
-            self::Cancelled => 'Đã hủy',
-            self::Refunded => 'Đã hoàn tiền',
-        };
-    }
-
-    public function getColor(): string|array|null
-    {
-        return match ($this) {
-            self::Pending => 'primary',
-            self::Processing => 'warning',
-            self::Completed => 'success',
-            self::Cancelled => 'danger',
-            self::Refunded => 'info',
-        };
-    }
-}
diff --git a/app/Enums/ServicePricingType.php b/app/Enums/ServicePricingType.php
deleted file mode 100644
index 4772e06..0000000
--- a/app/Enums/ServicePricingType.php
+++ /dev/null
@@ -1,28 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Enums;
-
-use Filament\Support\Contracts\HasLabel;
-
-enum ServicePricingType: string implements HasLabel
-{
-    case Multiplier = 'multiplier';
-
-    case Dropdown = 'dropdown';
-
-    case Checklist = 'checklist';
-
-    case MultiplierDropdown = 'multiplier_dropdown';
-
-    public function getLabel(): ?string
-    {
-        return match ($this) {
-            self::Multiplier => 'Hệ số',
-            self::Dropdown => 'Chọn một',
-            self::Checklist => 'Danh sách',
-            self::MultiplierDropdown => 'Hệ số dựa trên danh sách',
-        };
-    }
-}
diff --git a/app/Enums/ServiceType.php b/app/Enums/ServiceType.php
deleted file mode 100644
index 93a221b..0000000
--- a/app/Enums/ServiceType.php
+++ /dev/null
@@ -1,10 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Enums;
-
-enum ServiceType
-{
-    //
-}
diff --git a/app/Enums/TransactionType.php b/app/Enums/TransactionType.php
index 02104bc..e9f7fc2 100644
--- a/app/Enums/TransactionType.php
+++ b/app/Enums/TransactionType.php
@@ -24,8 +24,6 @@ enum TransactionType: string implements HasLabel
 
     case WheelReward = 'wheel_reward';
 
-    case ServiceOrder = 'service_order';
-
     case Refund = 'refund';
 
     public function getLabel(): ?string
@@ -39,7 +37,6 @@ public function getLabel(): ?string
             self::SellAccount => 'Bán tài khoản game',
             self::SpinWheel => 'Quay vòng quay',
             self::WheelReward => 'Phần thưởng vòng quay',
-            self::ServiceOrder => 'Đặt đơn dịch vụ',
             self::Refund => 'Hoàn tiền',
         };
     }
diff --git a/app/Filament/Resources/ServiceOrderResource.php b/app/Filament/Resources/ServiceOrderResource.php
deleted file mode 100644
index ae715b4..0000000
--- a/app/Filament/Resources/ServiceOrderResource.php
+++ /dev/null
@@ -1,120 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources;
-
-use App\Filament\Resources\ServiceOrderResource\Pages;
-use App\Models\ServiceOrder;
-use App\Tables\Columns\DateTimeColumn;
-use Filament\Infolists\Components\RepeatableEntry;
-use Filament\Infolists\Components\Section;
-use Filament\Infolists\Components\TextEntry;
-use Filament\Infolists\Infolist;
-use Filament\Resources\Resource;
-use Filament\Tables;
-use Filament\Tables\Table;
-use Illuminate\Database\Eloquent\Builder;
-use Illuminate\Database\Eloquent\SoftDeletingScope;
-
-class ServiceOrderResource extends Resource
-{
-    protected static ?string $model = ServiceOrder::class;
-
-    protected static ?string $navigationIcon = 'heroicon-o-document-text';
-
-    protected static ?string $modelLabel = 'Đơn hàng dịch vụ';
-
-    public static function infolist(Infolist $infolist): Infolist
-    {
-        return $infolist
-            ->schema([
-                Section::make()
-                    ->columns()
-                    ->schema([
-                        TextEntry::make('user.display_name')
-                            ->label('Người dùng'),
-                        TextEntry::make('total')
-                            ->label('Tổng tiền')
-                            ->money(),
-                        TextEntry::make('status')
-                            ->label('Trạng thái')
-                            ->badge(),
-                        TextEntry::make('created_at')
-                            ->label('Ngày đặt hàng'),
-                    ]),
-
-                RepeatableEntry::make('details')
-                    ->label('Chi tiết')
-                    ->columnSpanFull()
-                    ->schema([
-                        TextEntry::make('service.name')
-                            ->label('Dịch vụ'),
-                        TextEntry::make('package.name')
-                            ->label('Gói dịch vụ'),
-                        TextEntry::make('amount')
-                            ->label('Số tiền')
-                            ->money(),
-                        TextEntry::make('multiplier')
-                            ->label('Hệ số'),
-                        TextEntry::make('value')
-                            ->label('Giá trị')
-                            ->numeric(),
-                    ])
-                    ->columns(),
-            ]);
-    }
-
-    public static function table(Table $table): Table
-    {
-        return $table
-            ->columns([
-                Tables\Columns\TextColumn::make('user.display_name')
-                    ->label('Người dùng')
-                    ->sortable(),
-                Tables\Columns\TextColumn::make('total')
-                    ->label('Tổng tiền')
-                    ->numeric()
-                    ->sortable(),
-                Tables\Columns\TextColumn::make('status')
-                    ->label('Trạng thái')
-                    ->badge()
-                    ->searchable(),
-                DateTimeColumn::make('created_at'),
-                DateTimeColumn::make('updated_at'),
-                DateTimeColumn::make('deleted_at'),
-            ])
-            ->filters([
-                Tables\Filters\TrashedFilter::make(),
-            ])
-            ->actions([
-                Tables\Actions\ViewAction::make(),
-                Tables\Actions\DeleteAction::make(),
-                Tables\Actions\ForceDeleteAction::make(),
-                Tables\Actions\RestoreAction::make(),
-            ])
-            ->bulkActions([
-                Tables\Actions\BulkActionGroup::make([
-                    Tables\Actions\DeleteBulkAction::make(),
-                    Tables\Actions\ForceDeleteBulkAction::make(),
-                    Tables\Actions\RestoreBulkAction::make(),
-                ]),
-            ]);
-    }
-
-    public static function getPages(): array
-    {
-        return [
-            'index' => Pages\ListServiceOrders::route('/'),
-            'view' => Pages\ViewServiceOrder::route('/{record}'),
-        ];
-    }
-
-    public static function getEloquentQuery(): Builder
-    {
-        return parent::getEloquentQuery()
-            ->withoutGlobalScopes([
-                SoftDeletingScope::class,
-            ]);
-    }
-}
diff --git a/app/Filament/Resources/ServiceOrderResource/Pages/ListServiceOrders.php b/app/Filament/Resources/ServiceOrderResource/Pages/ListServiceOrders.php
deleted file mode 100644
index becdbc9..0000000
--- a/app/Filament/Resources/ServiceOrderResource/Pages/ListServiceOrders.php
+++ /dev/null
@@ -1,13 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources\ServiceOrderResource\Pages;
-
-use App\Filament\Resources\ServiceOrderResource;
-use Filament\Resources\Pages\ListRecords;
-
-class ListServiceOrders extends ListRecords
-{
-    protected static string $resource = ServiceOrderResource::class;
-}
diff --git a/app/Filament/Resources/ServiceOrderResource/Pages/ViewServiceOrder.php b/app/Filament/Resources/ServiceOrderResource/Pages/ViewServiceOrder.php
deleted file mode 100644
index 34dbf5a..0000000
--- a/app/Filament/Resources/ServiceOrderResource/Pages/ViewServiceOrder.php
+++ /dev/null
@@ -1,21 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources\ServiceOrderResource\Pages;
-
-use App\Filament\Resources\ServiceOrderResource;
-use Filament\Actions;
-use Filament\Resources\Pages\ViewRecord;
-
-class ViewServiceOrder extends ViewRecord
-{
-    protected static string $resource = ServiceOrderResource::class;
-
-    protected function getHeaderActions(): array
-    {
-        return [
-            Actions\DeleteAction::make(),
-        ];
-    }
-}
diff --git a/app/Filament/Resources/ServiceResource.php b/app/Filament/Resources/ServiceResource.php
deleted file mode 100644
index 546bd5b..0000000
--- a/app/Filament/Resources/ServiceResource.php
+++ /dev/null
@@ -1,133 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources;
-
-use App\Enums\ServicePricingType;
-use App\Filament\Resources\ServiceResource\Pages;
-use App\Filament\Resources\ServiceResource\RelationManagers\PackagesRelationManager;
-use App\Models\Service;
-use App\Tables\Columns\DateTimeColumn;
-use Filament\Forms;
-use Filament\Forms\Form;
-use Filament\Resources\Resource;
-use Filament\Tables;
-use Filament\Tables\Table;
-use Illuminate\Database\Eloquent\Builder;
-use Illuminate\Database\Eloquent\SoftDeletingScope;
-use Illuminate\Support\Str;
-
-class ServiceResource extends Resource
-{
-    protected static ?string $model = Service::class;
-
-    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';
-
-    protected static ?string $modelLabel = 'Dịch Vụ';
-
-    public static function form(Form $form): Form
-    {
-        return $form
-            ->schema([
-                Forms\Components\Section::make()
-                    ->columns()
-                    ->schema([
-                        Forms\Components\TextInput::make('name')
-                            ->label('Tên dịch vụ')
-                            ->live(onBlur: true)
-                            ->required()
-                            ->afterStateUpdated(fn(?string $state, Forms\Set $set) => $set('slug', Str::slug($state))),
-                        Forms\Components\TextInput::make('slug')
-                            ->dehydrated()
-                            ->required()
-                            ->maxLength(255)
-                            ->unique(ignoreRecord: true),
-                        Forms\Components\RichEditor::make('description')
-                            ->label('Mô tả')
-                            ->columnSpanFull(),
-                        Forms\Components\FileUpload::make('image')
-                            ->label('Hình ảnh')
-                            ->columnSpanFull()
-                            ->image(),
-                        Forms\Components\Radio::make('pricing_type')
-                            ->label('Cách tính giá')
-                            ->inline()
-                            ->inlineLabel(false)
-                            ->options(ServicePricingType::class)
-                            ->reactive()
-                            ->columnSpanFull()
-                            ->required(),
-                        Forms\Components\TextInput::make('multiplier')
-                            ->required()
-                            ->columnSpanFull()
-                            ->visible(fn(Forms\Get $get) => $get('pricing_type') === ServicePricingType::Multiplier->value)
-                            ->label('Hệ số')
-                            ->numeric(),
-                        Forms\Components\Toggle::make('is_visible')
-                            ->label('Hiển thị')
-                            ->default(true),
-                    ]),
-            ]);
-    }
-
-    public static function table(Table $table): Table
-    {
-        return $table
-            ->columns([
-                Tables\Columns\TextColumn::make('name')
-                    ->label('Tên dịch vụ')
-                    ->searchable(),
-                Tables\Columns\ImageColumn::make('image')
-                    ->label('Hình ảnh'),
-                Tables\Columns\TextColumn::make('pricing_type')
-                    ->label('Cách tính giá')
-                    ->searchable(),
-                Tables\Columns\ToggleColumn::make('is_visible')
-                    ->label('Hiển thị'),
-                DateTimeColumn::make('created_at'),
-                DateTimeColumn::make('updated_at'),
-                DateTimeColumn::make('deleted_at'),
-            ])
-            ->filters([
-                Tables\Filters\TrashedFilter::make(),
-            ])
-            ->actions([
-                Tables\Actions\DeleteAction::make(),
-                Tables\Actions\EditAction::make(),
-                Tables\Actions\ForceDeleteAction::make(),
-                Tables\Actions\RestoreAction::make(),
-            ])
-            ->bulkActions([
-                Tables\Actions\BulkActionGroup::make([
-                    Tables\Actions\DeleteBulkAction::make(),
-                    Tables\Actions\ForceDeleteBulkAction::make(),
-                    Tables\Actions\RestoreBulkAction::make(),
-                ]),
-            ]);
-    }
-
-    public static function getRelations(): array
-    {
-        return [
-            PackagesRelationManager::make(),
-        ];
-    }
-
-    public static function getPages(): array
-    {
-        return [
-            'index' => Pages\ListServices::route('/'),
-            'create' => Pages\CreateService::route('/create'),
-            'edit' => Pages\EditService::route('/{record}/edit'),
-        ];
-    }
-
-    public static function getEloquentQuery(): Builder
-    {
-        return parent::getEloquentQuery()
-            ->withoutGlobalScopes([
-                SoftDeletingScope::class,
-            ]);
-    }
-}
diff --git a/app/Filament/Resources/ServiceResource/Pages/CreateService.php b/app/Filament/Resources/ServiceResource/Pages/CreateService.php
deleted file mode 100644
index 307bbee..0000000
--- a/app/Filament/Resources/ServiceResource/Pages/CreateService.php
+++ /dev/null
@@ -1,13 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources\ServiceResource\Pages;
-
-use App\Filament\Resources\ServiceResource;
-use Filament\Resources\Pages\CreateRecord;
-
-class CreateService extends CreateRecord
-{
-    protected static string $resource = ServiceResource::class;
-}
diff --git a/app/Filament/Resources/ServiceResource/Pages/EditService.php b/app/Filament/Resources/ServiceResource/Pages/EditService.php
deleted file mode 100644
index 5daf2dc..0000000
--- a/app/Filament/Resources/ServiceResource/Pages/EditService.php
+++ /dev/null
@@ -1,23 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources\ServiceResource\Pages;
-
-use App\Filament\Resources\ServiceResource;
-use Filament\Actions;
-use Filament\Resources\Pages\EditRecord;
-
-class EditService extends EditRecord
-{
-    protected static string $resource = ServiceResource::class;
-
-    protected function getHeaderActions(): array
-    {
-        return [
-            Actions\DeleteAction::make(),
-            Actions\ForceDeleteAction::make(),
-            Actions\RestoreAction::make(),
-        ];
-    }
-}
diff --git a/app/Filament/Resources/ServiceResource/Pages/ListServices.php b/app/Filament/Resources/ServiceResource/Pages/ListServices.php
deleted file mode 100644
index e21551b..0000000
--- a/app/Filament/Resources/ServiceResource/Pages/ListServices.php
+++ /dev/null
@@ -1,21 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources\ServiceResource\Pages;
-
-use App\Filament\Resources\ServiceResource;
-use Filament\Actions;
-use Filament\Resources\Pages\ListRecords;
-
-class ListServices extends ListRecords
-{
-    protected static string $resource = ServiceResource::class;
-
-    protected function getHeaderActions(): array
-    {
-        return [
-            Actions\CreateAction::make(),
-        ];
-    }
-}
diff --git a/app/Filament/Resources/ServiceResource/RelationManagers/PackagesRelationManager.php b/app/Filament/Resources/ServiceResource/RelationManagers/PackagesRelationManager.php
deleted file mode 100644
index c5d7624..0000000
--- a/app/Filament/Resources/ServiceResource/RelationManagers/PackagesRelationManager.php
+++ /dev/null
@@ -1,85 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Filament\Resources\ServiceResource\RelationManagers;
-
-use App\Enums\ServicePricingType;
-use App\Forms\Components\CurrencyInput;
-use Filament\Forms;
-use Filament\Forms\Form;
-use Filament\Resources\RelationManagers\RelationManager;
-use Filament\Tables;
-use Filament\Tables\Table;
-use Illuminate\Contracts\Support\Htmlable;
-use Illuminate\Database\Eloquent\Model;
-
-class PackagesRelationManager extends RelationManager
-{
-    protected static string $relationship = 'packages';
-
-    protected static ?string $modelLabel = 'Gói dịch vụ';
-
-    protected function getTableHeading(): string|Htmlable|null
-    {
-        return 'Gói dịch vụ';
-    }
-
-    public function form(Form $form): Form
-    {
-        return $form
-            ->schema([
-                Forms\Components\TextInput::make('name')
-                    ->required()
-                    ->maxLength(255),
-                CurrencyInput::make('price')
-                    ->label('Giá')
-                    ->required(),
-                Forms\Components\TextInput::make('multiplier')
-                    ->label('Hệ số')
-                    ->numeric()
-                    ->required(),
-            ]);
-    }
-
-    public function table(Table $table): Table
-    {
-        return $table
-            ->recordTitleAttribute('name')
-            ->columns([
-                Tables\Columns\TextColumn::make('name')
-                    ->searchable()
-                    ->label('Tên'),
-                Tables\Columns\TextColumn::make('price')
-                    ->label('Giá')
-                    ->sortable()
-                    ->searchable()
-                    ->money(),
-            ])
-            ->filters([
-                Tables\Filters\TrashedFilter::make(),
-            ])
-            ->headerActions([
-                Tables\Actions\CreateAction::make(),
-            ])
-            ->actions([
-                Tables\Actions\EditAction::make(),
-                Tables\Actions\DeleteAction::make(),
-                Tables\Actions\ForceDeleteAction::make(),
-                Tables\Actions\RestoreAction::make(),
-            ])
-            ->bulkActions([
-                Tables\Actions\BulkActionGroup::make([
-                    Tables\Actions\DeleteBulkAction::make(),
-                    Tables\Actions\ForceDeleteBulkAction::make(),
-                    Tables\Actions\RestoreBulkAction::make(),
-                ]),
-            ]);
-    }
-
-    public static function canViewForRecord(Model $ownerRecord, string $pageClass): bool
-    {
-        return $ownerRecord->pricing_type !== ServicePricingType::Multiplier
-            && parent::canViewForRecord($ownerRecord, $pageClass);
-    }
-}
diff --git a/app/Http/Controllers/ServiceController.php b/app/Http/Controllers/ServiceController.php
deleted file mode 100644
index cd025e9..0000000
--- a/app/Http/Controllers/ServiceController.php
+++ /dev/null
@@ -1,105 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Http\Controllers;
-
-use App\Enums\ServiceOrderStatus;
-use App\Enums\ServicePricingType;
-use App\Enums\TransactionType;
-use App\Http\Resources\ServiceResource;
-use App\Models\Service;
-use App\Models\ServicePackage;
-use Illuminate\Http\Request;
-use Illuminate\Support\Facades\DB;
-use Illuminate\Validation\Rule;
-use Inertia\Inertia;
-use Inertia\Response;
-
-class ServiceController extends Controller
-{
-    public function index(): Response
-    {
-        $services = Service::query()
-            ->where('is_visible', true)
-            ->get();
-
-        return Inertia::render('Services/Index', [
-            'services' => ServiceResource::collection($services),
-        ]);
-    }
-
-    public function show(string $slug): Response
-    {
-        $service = Service::query()
-            ->where('slug', $slug)
-            ->where('is_visible', true)
-            ->with('packages')
-            ->firstOrFail();
-
-        return Inertia::render('Services/Show', [
-            'service' => new ServiceResource($service),
-        ]);
-    }
-
-    public function purchase(string $slug, Request $request)
-    {
-        $service = Service::query()
-            ->where('slug', $slug)
-            ->where('is_visible', true)
-            ->firstOrFail();
-
-        $user = $request->user();
-        /** @var ServicePackage|null $package */
-        $package = null;
-        /** @var ServicePricingType $type */
-        $type = $service->pricing_type;
-
-        if ($type === ServicePricingType::MultiplierDropdown) {
-            $request->validate([
-                'package_id' => [
-                    'required',
-                    Rule::exists(ServicePackage::class, 'id')
-                        ->where('service_id', $service->getKey()),
-                ],
-                'amount' => ['required', 'numeric', 'min:10000'],
-            ]);
-
-            $package = $service->packages()->where('service_id', $service->getKey())->first();
-        }
-
-        $amount = $request->input('amount');
-
-        try {
-            DB::transaction(function () use ($type, $user, $service, $package, $amount) {
-                $order = $user->serviceOrders()->create([
-                    'total' => $amount,
-                    'status' => ServiceOrderStatus::Pending,
-                ]);
-
-                $multiplier = $type === ServicePricingType::MultiplierDropdown
-                    ? $package->multiplier
-                    : $service->multiplier;
-
-                $order->details()->create([
-                    'service_package_id' => $package?->getKey(),
-                    'service_id' => $service->getKey(),
-                    'amount' => $amount,
-                    'multiplier' => $multiplier,
-                    'value' => $amount * $multiplier,
-                ]);
-
-                $user->recordTransaction(TransactionType::ServiceOrder, $amount);
-            });
-
-            return response()->json([
-                'message' => 'Purchase successful',
-            ], 200);
-        } catch (\Exception $e) {
-            return response()->json([
-                'message' => 'Purchase failed',
-                'error' => $e->getMessage(),
-            ], 500);
-        }
-    }
-}
diff --git a/app/Http/Resources/ServicePackageResource.php b/app/Http/Resources/ServicePackageResource.php
deleted file mode 100644
index 0dd62dd..0000000
--- a/app/Http/Resources/ServicePackageResource.php
+++ /dev/null
@@ -1,24 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Http\Resources;
-
-use Illuminate\Http\Request;
-use Illuminate\Http\Resources\Json\JsonResource;
-
-/**
- * @mixin \App\Models\ServicePackage
- */
-class ServicePackageResource extends JsonResource
-{
-    public function toArray(Request $request): array
-    {
-        return [
-            'id' => $this->id,
-            'name' => $this->name,
-            'price' => $this->price,
-            'multiplier' => $this->multiplier,
-        ];
-    }
-}
diff --git a/app/Http/Resources/ServiceResource.php b/app/Http/Resources/ServiceResource.php
deleted file mode 100644
index 04f2e8a..0000000
--- a/app/Http/Resources/ServiceResource.php
+++ /dev/null
@@ -1,27 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Http\Resources;
-
-use Illuminate\Http\Request;
-use Illuminate\Http\Resources\Json\JsonResource;
-use Illuminate\Support\Facades\Storage;
-
-/**
- * @mixin \App\Models\Service
- */
-class ServiceResource extends JsonResource
-{
-    public function toArray(Request $request): array
-    {
-        return [
-            'id' => $this->id,
-            'name' => $this->name,
-            'slug' => $this->slug,
-            'description' => $this->description,
-            'image' => $this->image ? Storage::url($this->image) : null,
-            'packages' => ServicePackageResource::collection($this->whenLoaded('packages')),
-        ];
-    }
-}
diff --git a/app/Models/Service.php b/app/Models/Service.php
deleted file mode 100644
index 7b487c9..0000000
--- a/app/Models/Service.php
+++ /dev/null
@@ -1,31 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Models;
-
-use App\Enums\ServicePricingType;
-use Illuminate\Database\Eloquent\Model;
-use Illuminate\Database\Eloquent\Relations\HasMany;
-use Illuminate\Database\Eloquent\SoftDeletes;
-
-class Service extends Model
-{
-    use SoftDeletes;
-
-    protected $guarded = [];
-
-    protected function casts(): array
-    {
-        return [
-            'pricing_type' => ServicePricingType::class,
-            'multiplier' => 'decimal:2',
-            'is_visible' => 'bool',
-        ];
-    }
-
-    public function packages(): HasMany
-    {
-        return $this->hasMany(ServicePackage::class);
-    }
-}
diff --git a/app/Models/ServiceOrder.php b/app/Models/ServiceOrder.php
deleted file mode 100644
index 0ea13aa..0000000
--- a/app/Models/ServiceOrder.php
+++ /dev/null
@@ -1,36 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Models;
-
-use App\Enums\ServiceOrderStatus;
-use Illuminate\Database\Eloquent\Model;
-use Illuminate\Database\Eloquent\Relations\BelongsTo;
-use Illuminate\Database\Eloquent\Relations\HasMany;
-use Illuminate\Database\Eloquent\SoftDeletes;
-
-class ServiceOrder extends Model
-{
-    use SoftDeletes;
-
-    protected $guarded = [];
-
-    protected function casts(): array
-    {
-        return [
-            'total' => 'int',
-            'status' => ServiceOrderStatus::class,
-        ];
-    }
-
-    public function user(): BelongsTo
-    {
-        return $this->belongsTo(User::class);
-    }
-
-    public function details(): HasMany
-    {
-        return $this->hasMany(ServiceOrderDetail::class);
-    }
-}
diff --git a/app/Models/ServiceOrderDetail.php b/app/Models/ServiceOrderDetail.php
deleted file mode 100644
index a2d4964..0000000
--- a/app/Models/ServiceOrderDetail.php
+++ /dev/null
@@ -1,32 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Models;
-
-use Illuminate\Database\Eloquent\Model;
-use Illuminate\Database\Eloquent\Relations\BelongsTo;
-
-class ServiceOrderDetail extends Model
-{
-    protected $guarded = [];
-
-    protected function casts(): array
-    {
-        return [
-            'amount' => 'int',
-            'multiplier' => 'decimal:2',
-            'value' => 'int',
-        ];
-    }
-
-    public function service(): BelongsTo
-    {
-        return $this->belongsTo(Service::class);
-    }
-
-    public function package(): BelongsTo
-    {
-        return $this->belongsTo(ServicePackage::class, 'service_package_id');
-    }
-}
diff --git a/app/Models/ServicePackage.php b/app/Models/ServicePackage.php
deleted file mode 100644
index ac315c2..0000000
--- a/app/Models/ServicePackage.php
+++ /dev/null
@@ -1,29 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace App\Models;
-
-use Illuminate\Database\Eloquent\Model;
-use Illuminate\Database\Eloquent\Relations\BelongsTo;
-use Illuminate\Database\Eloquent\SoftDeletes;
-
-class ServicePackage extends Model
-{
-    use SoftDeletes;
-
-    protected $guarded = [];
-
-    protected function casts(): array
-    {
-        return [
-            'price' => 'int',
-            'multiplier' => 'decimal:2',
-        ];
-    }
-
-    public function service(): BelongsTo
-    {
-        return $this->belongsTo(Service::class);
-    }
-}
diff --git a/app/Models/User.php b/app/Models/User.php
index 140319d..279e2a8 100644
--- a/app/Models/User.php
+++ b/app/Models/User.php
@@ -153,11 +153,6 @@ public function deposits(): HasMany
         return $this->hasMany(Deposit::class);
     }
 
-    public function serviceOrders(): HasMany
-    {
-        return $this->hasMany(ServiceOrder::class);
-    }
-
     public function referredBy(): BelongsTo
     {
         return $this->belongsTo(Affiliate::class, 'referred_by');
diff --git a/app/Providers/Filament/AdminPanelProvider.php b/app/Providers/Filament/AdminPanelProvider.php
index a27edfd..ca00fe0 100644
--- a/app/Providers/Filament/AdminPanelProvider.php
+++ b/app/Providers/Filament/AdminPanelProvider.php
@@ -116,7 +116,6 @@ public function panel(Panel $panel): Panel
                         ->addMenuPanels([
                             StaticMenuPanel::make()
                                 ->add('Trang chủ', fn() => route('home'))
-                                ->add('Dịch vụ', fn() => route('services.index'))
                                 ->add('Flash Sale', fn() => route('flash-sales.index'))
                                 ->add('Tin tức', fn() => route('blog.index')),
                             ModelMenuPanel::make()
diff --git a/database/migrations/2024_05_19_022259_create_recharges_table.php b/database/migrations/2024_05_19_022259_create_recharges_table.php
index eb2cd29..8f094f4 100644
--- a/database/migrations/2024_05_19_022259_create_recharges_table.php
+++ b/database/migrations/2024_05_19_022259_create_recharges_table.php
@@ -24,7 +24,7 @@ public function up(): void
             $table->integer('declared_amount');
             $table->string('pin');
             $table->string('serial')->nullable();
-            $table->string('status', 20)->default(RechargeStatus::Pending);
+            $table->string('status')->default(RechargeStatus::Pending);
             $table->timestamps();
             $table->softDeletes();
         });
diff --git a/database/migrations/2024_06_22_110203_create_item_withdraws_table.php b/database/migrations/2024_06_22_110203_create_item_withdraws_table.php
index 2c87b6f..ba0fcd3 100644
--- a/database/migrations/2024_06_22_110203_create_item_withdraws_table.php
+++ b/database/migrations/2024_06_22_110203_create_item_withdraws_table.php
@@ -20,7 +20,7 @@ public function up(): void
             $table->foreignIdFor(GameItem::class);
             $table->foreignIdFor(User::class);
             $table->integer('quantity');
-            $table->string('status', 20)->default(ItemWithdrawStatus::Pending);
+            $table->string('status')->default(ItemWithdrawStatus::Pending);
             $table->timestamps();
             $table->softDeletes();
         });
diff --git a/database/migrations/2024_07_04_123245_create_deposits_table.php b/database/migrations/2024_07_04_123245_create_deposits_table.php
index e3275ee..9d21c1f 100644
--- a/database/migrations/2024_07_04_123245_create_deposits_table.php
+++ b/database/migrations/2024_07_04_123245_create_deposits_table.php
@@ -22,7 +22,7 @@ public function up(): void
             $table->string('transaction_id')->nullable();
             $table->integer('amount');
             $table->text('content')->nullable();
-            $table->string('status', 20)->default(DepositStatus::Pending);
+            $table->string('status')->default(DepositStatus::Pending);
             $table->dateTime('transacted_at')->nullable();
             $table->text('raw')->nullable();
             $table->timestamps();
diff --git a/database/migrations/2024_07_07_230118_create_posts_table.php b/database/migrations/2024_07_07_230118_create_posts_table.php
index a7373d8..bd6fa98 100644
--- a/database/migrations/2024_07_07_230118_create_posts_table.php
+++ b/database/migrations/2024_07_07_230118_create_posts_table.php
@@ -24,7 +24,7 @@ public function up(): void
             $table->text('description')->nullable();
             $table->longText('content');
             $table->string('image')->nullable();
-            $table->string('status', 20)->default(PostStatus::Pending);
+            $table->string('status')->default(PostStatus::Pending);
             $table->unsignedInteger('views')->default(0);
             $table->timestamps();
             $table->softDeletes();
diff --git a/database/migrations/2024_08_08_133636_create_services_table.php b/database/migrations/2024_08_08_133636_create_services_table.php
deleted file mode 100644
index eba5748..0000000
--- a/database/migrations/2024_08_08_133636_create_services_table.php
+++ /dev/null
@@ -1,48 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-use App\Models\Service;
-use Illuminate\Database\Migrations\Migration;
-use Illuminate\Database\Schema\Blueprint;
-use Illuminate\Support\Facades\Schema;
-
-return new class extends Migration {
-    /**
-     * Run the migrations.
-     */
-    public function up(): void
-    {
-        Schema::create('services', function (Blueprint $table) {
-            $table->id();
-            $table->string('name');
-            $table->string('slug')->unique();
-            $table->text('description')->nullable();
-            $table->string('image')->nullable();
-            $table->string('pricing_type', 20);
-            $table->decimal('multiplier')->nullable();
-            $table->boolean('is_visible')->default(true);
-            $table->timestamps();
-            $table->softDeletes();
-        });
-
-        Schema::create('service_packages', function (Blueprint $table) {
-            $table->id();
-            $table->foreignIdFor(Service::class);
-            $table->string('name');
-            $table->integer('price')->nullable();
-            $table->decimal('multiplier')->nullable();
-            $table->timestamps();
-            $table->softDeletes();
-        });
-    }
-
-    /**
-     * Reverse the migrations.
-     */
-    public function down(): void
-    {
-        Schema::dropIfExists('service_packages');
-        Schema::dropIfExists('services');
-    }
-};
diff --git a/database/migrations/2024_08_10_132335_create_service_orders_table.php b/database/migrations/2024_08_10_132335_create_service_orders_table.php
deleted file mode 100644
index 8c356ba..0000000
--- a/database/migrations/2024_08_10_132335_create_service_orders_table.php
+++ /dev/null
@@ -1,49 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-use App\Enums\ServiceOrderStatus;
-use App\Models\Service;
-use App\Models\ServiceOrder;
-use App\Models\ServicePackage;
-use App\Models\User;
-use Illuminate\Database\Migrations\Migration;
-use Illuminate\Database\Schema\Blueprint;
-use Illuminate\Support\Facades\Schema;
-
-return new class extends Migration {
-    /**
-     * Run the migrations.
-     */
-    public function up(): void
-    {
-        Schema::create('service_orders', function (Blueprint $table) {
-            $table->id();
-            $table->foreignIdFor(User::class);
-            $table->integer('total');
-            $table->string('status', 20)->default(ServiceOrderStatus::Pending);
-            $table->timestamps();
-            $table->softDeletes();
-        });
-
-        Schema::create('service_order_details', function (Blueprint $table) {
-            $table->id();
-            $table->foreignIdFor(ServiceOrder::class);
-            $table->foreignIdFor(Service::class);
-            $table->foreignIdFor(ServicePackage::class)->nullable();
-            $table->integer('amount');
-            $table->decimal('multiplier')->nullable();
-            $table->integer('value');
-            $table->timestamps();
-        });
-    }
-
-    /**
-     * Reverse the migrations.
-     */
-    public function down(): void
-    {
-        Schema::dropIfExists('service_order_details');
-        Schema::dropIfExists('service_orders');
-    }
-};
diff --git a/database/seeders/DatabaseSeeder.php b/database/seeders/DatabaseSeeder.php
index 5c0bb97..0f26398 100644
--- a/database/seeders/DatabaseSeeder.php
+++ b/database/seeders/DatabaseSeeder.php
@@ -25,7 +25,6 @@ public function run(): void
         $this->uploadFiles('wheels');
         $this->uploadFiles('banks');
         $this->uploadFiles('posts');
-        $this->uploadFiles('services');
 
         $this->call([
             SettingSeeder::class,
@@ -41,17 +40,13 @@ public function run(): void
             BankAccountSeeder::class,
             CategorySeeder::class,
             PostSeeder::class,
-            ServiceSeeder::class,
             AffiliateTierSeeder::class,
         ]);
     }
 
     protected function uploadFiles(string $directory): void
     {
-        // Ensure the directory is clean to avoid outdated or incomplete files.
-        if (Storage::exists($directory)) {
-            Storage::deleteDirectory($directory);
-        }
+        Storage::deleteDirectory($directory);
 
         $files = glob(database_path("seeders/files/$directory/*"));
 
diff --git a/database/seeders/ServiceSeeder.php b/database/seeders/ServiceSeeder.php
deleted file mode 100644
index ee764af..0000000
--- a/database/seeders/ServiceSeeder.php
+++ /dev/null
@@ -1,53 +0,0 @@
-<?php
-
-declare(strict_types=1);
-
-namespace Database\Seeders;
-
-use App\Enums\ServicePricingType;
-use App\Models\Service;
-use Illuminate\Database\Seeder;
-use Illuminate\Support\Arr;
-use Illuminate\Support\Str;
-
-class ServiceSeeder extends Seeder
-{
-    /**
-     * Run the database seeds.
-     */
-    public function run(): void
-    {
-        Service::query()->truncate();
-
-        $services = [
-            [
-                'name' => 'Mua Vàng NRO',
-                'pricing_type' => ServicePricingType::MultiplierDropdown,
-                'packages' => [
-                    ['name' => 'Vũ trụ 1', 'multiplier' => 8000],
-                    ['name' => 'Vũ trụ 2', 'multiplier' => 8000],
-                    ['name' => 'Vũ trụ 3', 'multiplier' => 7000],
-                    ['name' => 'Vũ trụ 4', 'multiplier' => 7000],
-                    ['name' => 'Vũ trụ 5', 'multiplier' => 9500],
-                    ['name' => 'Vũ trụ 6', 'multiplier' => 9500],
-                    ['name' => 'Vũ trụ 7', 'multiplier' => 7000],
-                    ['name' => 'Vũ trụ 8, 9, 10', 'multiplier' => 4800],
-                    ['name' => 'Vũ trụ 11', 'multiplier' => 4800],
-                ],
-            ],
-        ];
-
-        foreach ($services as $service) {
-            $packages = Arr::pull($service, 'packages');
-
-            $service = Service::create([
-                ...$service,
-                'slug' => $slug = Str::slug($service['name']),
-                'image' => "services/$slug.png",
-                'description' => fake()->sentence(),
-            ]);
-
-            $service->packages()->createMany($packages);
-        }
-    }
-}
diff --git a/database/seeders/files/services/mua-vang-nro.png b/database/seeders/files/services/mua-vang-nro.png
deleted file mode 100644
index 963a75e..0000000
Binary files a/database/seeders/files/services/mua-vang-nro.png and /dev/null differ
diff --git a/resources/js/Pages/Services/Index.vue b/resources/js/Pages/Services/Index.vue
deleted file mode 100644
index e34190e..0000000
--- a/resources/js/Pages/Services/Index.vue
+++ /dev/null
@@ -1,52 +0,0 @@
-<script setup lang="ts">
-import { Button } from '@/components/ui/button'
-import { Card } from '@/components/ui/card'
-import { Service } from '@/types'
-import { Link } from '@inertiajs/vue3'
-
-defineProps<{
-    services: Service[]
-}>()
-</script>
-
-<template>
-    <div class="container py-4">
-        <div
-            class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 sm:gap-4"
-        >
-            <Card v-for="(service, index) in services" :key="index">
-                <Link :href="route('services.show', service.slug)">
-                    <img
-                        v-lazy="service.image"
-                        :alt="service.name"
-                        class="transition-all aspect-video w-full object-cover rounded-t-lg"
-                    />
-                </Link>
-                <div class="text-center p-3">
-                    <h4
-                        class="font-medium truncate group-hover:text-primary transition-all mb-2"
-                    >
-                        <Link
-                            :href="route('services.show', service.slug)"
-                            :title="service.name"
-                            class="hover:text-primary transition-all"
-                        >
-                            {{ service.name }}
-                        </Link>
-                    </h4>
-                    <div class="text-muted-foreground text-sm">
-                        123 giao dịch
-                    </div>
-
-                    <div class="grid mt-4">
-                        <Button as-child class="w-full">
-                            <Link :href="route('services.show', service.slug)">
-                                Xem ngay
-                            </Link>
-                        </Button>
-                    </div>
-                </div>
-            </Card>
-        </div>
-    </div>
-</template>
diff --git a/resources/js/Pages/Services/Show.vue b/resources/js/Pages/Services/Show.vue
deleted file mode 100644
index 2a3b99c..0000000
--- a/resources/js/Pages/Services/Show.vue
+++ /dev/null
@@ -1,170 +0,0 @@
-<script setup lang="ts">
-import InputError from '@/components/Forms/InputError.vue'
-import ShoppingBagIcon from '@/components/Icons/ShoppingBagIcon.vue'
-import { Button } from '@/components/ui/button'
-import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
-import { Input } from '@/components/ui/input'
-import { Label } from '@/components/ui/label'
-import {
-    Select,
-    SelectContent,
-    SelectGroup,
-    SelectItem,
-    SelectTrigger,
-    SelectValue,
-} from '@/components/ui/select'
-import { formatCurrency, formatNumber } from '@/lib/utils'
-import { Service } from '@/types'
-import { Head, useForm } from '@inertiajs/vue3'
-import { computed } from 'vue'
-
-const props = defineProps<{
-    service: Service
-}>()
-
-const form = useForm<{
-    package_id: string | undefined
-    amount: number
-}>({
-    package_id: undefined,
-    amount: 0,
-})
-
-const selectedPackage = computed(() =>
-    props.service.packages.find(
-        (servicePackage) => String(servicePackage.id) === form.package_id,
-    ),
-)
-const total = computed(() =>
-    formatNumber(form.amount * (selectedPackage.value?.multiplier ?? 1)),
-)
-
-const submit = () => {
-    form.post(route('services.purchase', { slug: props.service.slug }))
-}
-</script>
-
-<template>
-    <Head :title="service.name" />
-    <div class="container py-4">
-        <h2
-            class="text-xl sm:text-2xl font-bold mb-4 text-center text-primary relative after:content-[''] after:block after:w-20 after:h-1 after:rounded-lg after:bg-primary after:mx-auto after:mt-2 after:mb-4"
-        >
-            Dịch vụ {{ service.name }}
-        </h2>
-
-        <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
-            <div class="grid grid-cols-5 gap-4 col-span-3 sm:col-span-2">
-                <div class="col-span-5 sm:col-span-2">
-                    <img
-                        v-lazy="service.image"
-                        class="rounded-lg w-full"
-                        :alt="service.name"
-                    />
-                </div>
-                <form
-                    @submit.prevent="submit"
-                    class="col-span-5 sm:col-span-3 space-y-6"
-                >
-                    <div class="space-y-2">
-                        <Label>Chọn vũ trụ</Label>
-                        <Select v-model="form.package_id">
-                            <SelectTrigger>
-                                <SelectValue placeholder="Chọn vũ trụ" />
-                            </SelectTrigger>
-                            <SelectContent>
-                                <SelectGroup>
-                                    <SelectItem
-                                        v-for="(
-                                            servicePackage, index
-                                        ) in service.packages"
-                                        :key="index"
-                                        :value="String(servicePackage.id)"
-                                    >
-                                        {{ servicePackage.name }}
-                                    </SelectItem>
-                                </SelectGroup>
-                            </SelectContent>
-                        </Select>
-                        <InputError :message="form.errors.package_id" />
-                    </div>
-                    <div class="space-y-2">
-                        <Label for="amount">Số tiền</Label>
-                        <Input
-                            v-model="form.amount"
-                            type="number"
-                            id="amount"
-                            placeholder="Nhập số tiền cần mua"
-                            :disabled="!form.package_id"
-                        />
-                        <InputError :message="form.errors.amount" />
-                    </div>
-                </form>
-            </div>
-            <div class="col-span-3 sm:col-span-1 mt-2 sm:mt-0">
-                <Card>
-                    <CardHeader class="p-4 pb-0">
-                        <CardTitle class="text-base">
-                            Thông tin dịch vụ
-                        </CardTitle>
-                    </CardHeader>
-                    <CardContent class="p-4">
-                        <div
-                            class="grid grid-cols-1 divide-y text-sm text-muted-foreground"
-                        >
-                            <div class="col-span-1 flex justify-between py-4">
-                                <div class="flex items-center gap-x-2">
-                                    Dịch vụ
-                                </div>
-                                <div class="text-end text-gray-800">
-                                    {{ service.name }}
-                                </div>
-                            </div>
-                            <div class="col-span-1 flex justify-between py-4">
-                                <div class="flex items-center gap-x-2">
-                                    Hệ số
-                                </div>
-                                <div class="text-end text-gray-800">
-                                    {{ selectedPackage?.multiplier }}
-                                </div>
-                            </div>
-                            <div class="col-span-1 flex justify-between py-4">
-                                <div class="flex items-center gap-x-2">
-                                    Số tiền
-                                </div>
-                                <div class="text-end text-gray-800">
-                                    {{ formatCurrency(form.amount) }}
-                                </div>
-                            </div>
-                            <div class="col-span-1 flex justify-between py-4">
-                                <div class="flex items-center gap-x-2">
-                                    Tổng
-                                </div>
-                                <div class="text-end text-primary font-bold">
-                                    {{ total }} vàng
-                                </div>
-                            </div>
-                        </div>
-
-                        <Button
-                            type="submit"
-                            class="w-full mt-4"
-                            size="lg"
-                            :loading="form.processing"
-                            @click="submit"
-                        >
-                            <ShoppingBagIcon
-                                class="size-5 me-2"
-                                v-if="!form.processing"
-                            />
-                            <template v-if="$page.props.auth.user">
-                                Mua ngay
-                            </template>
-                            <template v-else> Đăng nhập để mua </template>
-                        </Button>
-                    </CardContent>
-                </Card>
-            </div>
-        </div>
-    </div>
-</template>
diff --git a/resources/js/components/Breadcrumb.vue b/resources/js/components/Breadcrumb.vue
index 0295a08..88447eb 100644
--- a/resources/js/components/Breadcrumb.vue
+++ b/resources/js/components/Breadcrumb.vue
@@ -10,7 +10,6 @@ import { Link, usePage } from '@inertiajs/vue3'
 import { computed } from 'vue'
 
 const breadcrumbs = computed(() => usePage().props.breadcrumbs || [])
-const isLast = computed(() => breadcrumbs.value.length - 1)
 </script>
 
 <template>
@@ -22,10 +21,7 @@ const isLast = computed(() => breadcrumbs.value.length - 1)
                     :key="index"
                 >
                     <BreadcrumbLink as-child>
-                        <Link
-                            :href="item.url"
-                            v-if="item.url && index !== isLast"
-                        >
+                        <Link :href="item.url" v-if="item.url">
                             {{ item.title }}
                         </Link>
                         <span v-else>{{ item.title }}</span>
diff --git a/resources/js/lib/utils.ts b/resources/js/lib/utils.ts
index 07437bc..d69ad7a 100644
--- a/resources/js/lib/utils.ts
+++ b/resources/js/lib/utils.ts
@@ -4,13 +4,6 @@ import { twMerge } from 'tailwind-merge'
 export function cn(...inputs: ClassValue[]) {
     return twMerge(clsx(inputs))
 }
-
-export function formatNumber(
-    value: number,
-    options?: Intl.NumberFormatOptions,
-) {
-    return new Intl.NumberFormat('vi-VN', options).format(value)
-}
 export function formatCurrency(value: number) {
     return new Intl.NumberFormat('vi-VN', {
         style: 'currency',
diff --git a/resources/js/types/index.d.ts b/resources/js/types/index.d.ts
index f8985b8..41191fe 100644
--- a/resources/js/types/index.d.ts
+++ b/resources/js/types/index.d.ts
@@ -176,20 +176,6 @@ export interface Post {
     views: string
 }
 
-export interface Service {
-    id: number
-    name: string
-    slug: string
-    description: string
-    image: string | null
-    packages: {
-        id: number
-        name: string
-        price: number
-        multiplier: number
-    }[]
-}
-
 export interface FlashSale {
     id: number
     name: string
diff --git a/routes/web.php b/routes/web.php
index 5dcc179..4bb8404 100644
--- a/routes/web.php
+++ b/routes/web.php
@@ -7,7 +7,6 @@
 use App\Http\Controllers\Auth\NewPasswordController;
 use App\Http\Controllers\Auth\PasswordResetLinkController;
 use App\Http\Controllers\Auth\RegisteredUserController;
-use App\Http\Controllers\ServiceController;
 use App\Http\Controllers\User\WithdrawItemController;
 use App\Http\Controllers\User\AffiliateController;
 use App\Http\Controllers\Webhook\SePayController;
@@ -38,7 +37,6 @@
 use App\Models\FlashSale;
 use App\Models\Game;
 use App\Models\Post;
-use App\Models\Service;
 use App\Models\Wheel;
 use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
 use Illuminate\Support\Facades\Route;
@@ -157,24 +155,6 @@
         Route::post('{id}/apply-discount', [AccountController::class, 'applyDiscount'])->name('apply-discount');
     });
 
-    Route::prefix('dich-vu')
-        ->name('services.')
-        ->controller(ServiceController::class)
-        ->group(function () {
-            Route::get('/', 'index')
-                ->name('index')
-                ->breadcrumbs(fn(Trail $trail) => $trail->parent('home')->push('Dịch vụ', 'services.index'));
-            Route::get('{slug}', 'show')
-                ->name('show')
-                ->breadcrumbs(function (Trail $trail, string $slug) {
-                    $service = Service::query()->where('slug', $slug)->firstOrFail();
-                    $trail->parent('services.index')->push($service->name, route('services.show', $slug));
-                });
-            Route::post('{slug}/purchase', 'purchase')
-                ->middleware('auth')
-                ->name('purchase');
-        });
-
     Route::prefix('blog')->name('blog.')->group(function (): void {
         Route::get('/', [BlogController::class, 'index'])
             ->name('index')

{"private": true, "type": "module", "scripts": {"build": "vue-tsc && vite build", "dev": "vite", "format": "prettier --write ."}, "devDependencies": {"@chenfengyuan/vue-countdown": "^2.1.3", "@fancyapps/ui": "^5.0.36", "@inertiajs/vue3": "^2.0.17", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.11", "@types/jquery": "^3.5.32", "@types/lodash": "^4.17.20", "@types/nprogress": "^0.2.3", "@vitejs/plugin-vue": "^5.2.4", "@vueuse/core": "^10.11.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-vue": "^8.6.0", "jquery": "^3.7.1", "js-confetti": "^0.12.0", "laravel-echo": "^1.19.0", "laravel-vite-plugin": "^1.3.0", "lodash": "^4.17.21", "lucide-vue-next": "^0.412.0", "nprogress": "^0.2.0", "prettier": "^3.6.2", "prettier-plugin-organize-imports": "^4.2.0", "prettier-plugin-tailwindcss": "^0.6.14", "pusher-js": "^8.4.0", "radix-vue": "^1.9.17", "tailwind-merge": "^2.6.0", "tailwindcss": "^4.1.11", "tailwindcss-animate": "^1.0.7", "typescript": "^5.9.2", "vite": "^5.4.19", "vue": "^3.5.18", "vue-lazyload": "^3.0.0", "vue-tippy": "^6.7.1", "vue-tsc": "^2.2.12", "ziggy-js": "^2.5.3"}, "dependencies": {"reka-ui": "^2.4.1", "tw-animate-css": "^1.3.6"}}
# Lashgame

## Domain specific settings connections

This project uses [spatie/laravel-settings](https://github.com/spatie/laravel-settings) and stores
settings in a database connection that is selected based on the request's domain.

Database connections for each domain can be configured through environment variables so that
deploying new application versions will not override them. For a domain like `example.com` set
variables using the following pattern in your `.env` file:

```ini
SETTINGS_DB_EXAMPLE_COM_DATABASE=example_settings
SETTINGS_DB_EXAMPLE_COM_USERNAME=user
SETTINGS_DB_EXAMPLE_COM_PASSWORD=secret
SETTINGS_DB_EXAMPLE_COM_HOST=127.0.0.1
```

Optional variables such as `PORT`, `DRIVER`, `CHARSET` and others may also be provided. The
`SettingsProperty` model will automatically create a connection named `settings_example_com` using
these values and will use it for settings whenever a request is made to `example.com`.


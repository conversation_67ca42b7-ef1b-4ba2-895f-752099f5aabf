name: Fix code style

on:
  push:

jobs:
  fix-code-style:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      - name: Set up PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.3'
      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: latest
      - name: Install dependencies
        run: |
          composer install --no-interaction --no-progress
          npm ci
      - name: Run Pint
        run: vendor/bin/pint
      - name: Run Prettier
        run: npm run format
      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: >
            chore: fix code style

{"$schema": "https://getcomposer.org/schema.json", "name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.2", "bezhansalleh/filament-shield": "^3.3.9", "codewithdennis/filament-select-tree": "^3.1.58", "datlechin/filament-menu-builder": "dev-main", "filament/filament": "^3.3.35", "filament/spatie-laravel-settings-plugin": "^3.3.35", "geoip2/geoip2": "^3.2", "inertiajs/inertia-laravel": "^2.0.4", "intervention/image-laravel": "^1.5.6", "jenssegers/agent": "^2.6.4", "laravel-notification-channels/telegram": "^5.0", "laravel/framework": "^11.45.1", "laravel/horizon": "^5.33.2", "laravel/socialite": "^5.23.0", "laravel/tinker": "^2.10.1", "league/flysystem-aws-s3-v3": "^3.29", "malzariey/filament-daterangepicker-filter": "^3.4.3", "predis/predis": "^2.4.0", "pusher/pusher-php-server": "^7.2.7", "resend/resend-php": "^0.15.2", "spatie/color": "^1.8", "tabuna/breadcrumbs": "^4.3.0", "tightenco/ziggy": "^2.5.3", "torann/geoip": "^3.0.9"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.16.0", "barryvdh/laravel-ide-helper": "^3.6.0", "fakerphp/faker": "^1.24.1", "larastan/larastan": "^2.11.2", "laravel-lang/common": "^6.7.1", "laravel/pail": "^1.2.3", "laravel/pint": "^1.24.0", "mockery/mockery": "^1.6.12", "nunomaduro/collision": "^8.5.0", "pestphp/pest": "^2.36.0", "pestphp/pest-plugin-laravel": "^2.4", "pestphp/pest-plugin-livewire": "^2.1"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force", "@php artisan vendor:publish --tag=livewire:assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi", "@php -r \"file_exists('database/database.sqlite') || touch('database/database.sqlite');\"", "@php artisan migrate --graceful --ansi"], "dev": ["Composer\\Config::disableProcessTimeout", "npx concurrently -c \"#93c5fd,#c4b5fd,#fb7185,#fdba74\" \"php artisan serve\" \"php artisan queue:listen --tries=1\" \"php artisan pail\" \"npm run dev\" --names=server,queue,logs,vite"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}
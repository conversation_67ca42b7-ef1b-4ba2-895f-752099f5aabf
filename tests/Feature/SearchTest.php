<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\Account;
use App\Models\AccountCategory;
use App\Models\Category;
use App\Models\Game;
use App\Models\Post;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SearchTest extends TestCase
{
    use RefreshDatabase;

    public function test_search_api_returns_successful_response(): void
    {
        $response = $this->get('/api/search?query=test');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    'query',
                    'results',
                    'total',
                ],
            ]);
    }

    public function test_search_api_validates_query_parameter(): void
    {
        $response = $this->get('/api/search', ['Accept' => 'application/json']);

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['query']);
    }

    public function test_search_api_finds_custom_navigation_items(): void
    {
        $response = $this->get('/api/search?query=nap');

        $response->assertStatus(200)
            ->assertJsonPath('data.total', 1)
            ->assertJsonPath('data.results.0.title', 'Nạp tiền')
            ->assertJsonPath('data.results.0.type', 'navigation');
    }

    public function test_search_api_finds_accounts(): void
    {
        $user = User::factory()->create();
        $game = Game::factory()->create(['name' => 'Test Game']);
        $category = AccountCategory::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
            'game_id' => $game->id,
            'is_visible' => true,
        ]);

        $account = Account::create([
            'user_id' => $user->id,
            'category_id' => $category->id,
            'acc_name' => 'Test Account',
            'description' => 'A test gaming account',
            'price' => 100000,
            'status' => 'selling',
        ]);

        $response = $this->get('/api/search?query=test');

        $response->assertStatus(200);

        $results = $response->json('data.results');
        $accountResults = collect($results)->where('type', 'account');

        $this->assertGreaterThan(0, $accountResults->count());
        $expectedTitle = sprintf('Tài khoản %s #%s', 'Test Category', $account->id);
        $this->assertTrue(
            $accountResults->contains(fn($r) => $r['title'] === $expectedTitle),
            sprintf('Expected to find account titled "%s" in search results.', $expectedTitle),
        );
    }

    public function test_search_api_finds_posts(): void
    {
        $user = User::factory()->create();
        $category = Category::create([
            'name' => 'Test Category',
            'slug' => 'test-category',
        ]);

        Post::create([
            'user_id' => $user->id,
            'category_id' => $category->id,
            'title' => 'Test Post',
            'slug' => 'test-post',
            'description' => 'A test blog post',
            'content' => 'This is test content',
            'status' => 'published',
        ]);

        $response = $this->get('/api/search?query=test');

        $response->assertStatus(200);

        $results = $response->json('data.results');
        $postResults = collect($results)->where('type', 'post');

        $this->assertGreaterThan(0, $postResults->count());
        $this->assertEquals('Test Post', $postResults->first()['title']);
    }

    public function test_search_api_handles_vietnamese_diacritics(): void
    {
        // Test with "nhap" which should find "Đăng nhập"
        $response = $this->get('/api/search?query=nhap');

        $response->assertStatus(200);

        $results = $response->json('data.results');
        $customResults = collect($results)->where('type', 'navigation');

        // Should find "Đăng nhập" when searching for "nhap"
        $loginResult = $customResults->firstWhere('id', 'login');

        $this->assertNotNull($loginResult, 'Login result should be found when searching for "nhap"');
        $this->assertEquals('Đăng nhập', $loginResult['title']);

        // Test with "nap" which should find "Nạp tiền"
        $response2 = $this->get('/api/search?query=nap');
        $results2 = $response2->json('data.results');
        $depositResult = collect($results2)->firstWhere('id', 'deposit');

        $this->assertNotNull($depositResult, 'Deposit result should be found when searching for "nap"');
        $this->assertEquals('Nạp tiền', $depositResult['title']);
    }

    public function test_search_results_are_sorted_by_weight(): void
    {
        $response = $this->get('/api/search?query=tai');

        $response->assertStatus(200);

        $results = $response->json('data.results');

        // Results should be sorted by weight in descending order
        $weights = collect($results)->pluck('weight')->toArray();
        $sortedWeights = collect($weights)->sortDesc()->values()->toArray();

        $this->assertEquals($sortedWeights, $weights);
    }
}

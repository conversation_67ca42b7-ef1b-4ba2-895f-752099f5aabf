<?php

declare(strict_types=1);

namespace Tests\Feature;

use App\Models\BankAccount;
use App\Services\SieuthicodeService;
use App\Settings\DepositSettings;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class SieuthicodeServiceTest extends TestCase
{
    use RefreshDatabase;

    public function test_get_transactions_for_supported_bank(): void
    {
        Http::fake([
            'api.sieuthicode.net/*' => Http::response([
                'status' => 'success',
                'message' => 'Thành công',
                'transactions' => [
                    [
                        'transactionID' => 'TEST123',
                        'amount' => 100000,
                        'description' => 'NAP TIEN 1',
                        'transactionDate' => '2024-12-19',
                        'type' => 'IN',
                    ],
                ],
            ], 200),
        ]);

        $bankAccount = BankAccount::factory()->create([
            'bank_name' => 'MBBank',
            'account_number' => '**********',
            'sieuthicode_enabled' => true,
            'sieuthicode_password' => 'test_password',
            'sieuthicode_token' => 'test_token',
        ]);

        $settings = new DepositSettings();
        $service = new SieuthicodeService($settings);
        $transactions = $service->getTransactions($bankAccount);

        $this->assertCount(1, $transactions);
        $this->assertEquals('TEST123', $transactions[0]['transactionID']);
    }

    public function test_returns_empty_array_for_api_error(): void
    {
        Http::fake([
            'api.sieuthicode.net/*' => Http::response([
                'status' => 'error',
                'message' => 'Invalid credentials',
            ], 200),
        ]);

        $bankAccount = BankAccount::factory()->create([
            'bank_name' => 'MBBank',
            'account_number' => '**********',
            'sieuthicode_enabled' => true,
            'sieuthicode_password' => 'test_password',
            'sieuthicode_token' => 'test_token',
        ]);

        $settings = new DepositSettings();
        $service = new SieuthicodeService($settings);
        $transactions = $service->getTransactions($bankAccount);

        $this->assertEmpty($transactions);
    }
}

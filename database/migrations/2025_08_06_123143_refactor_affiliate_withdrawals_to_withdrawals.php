<?php

declare(strict_types=1);

use App\Models\Affiliate;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('affiliate_withdrawals', function (Blueprint $table) {
            $table->dropForeign(['affiliate_id']);
            $table->dropIndex('affiliate_withdrawals_affiliate_id_status_index');
        });

        Schema::rename('affiliate_withdrawals', 'withdrawals');

        Schema::table('withdrawals', function (Blueprint $table) {
            $table->after('id', function (Blueprint $table) {
                $table->morphs('withdrawable');
            });

            $table->index(['withdrawable_type', 'withdrawable_id'], 'withdrawals_withdrawable_index');
            $table->index(['status', 'created_at'], 'withdrawals_status_created_index');
        });

        DB::table('withdrawals')->update([
            'withdrawable_id' => DB::raw('affiliate_id'),
            'withdrawable_type' => Affiliate::class,
        ]);

        Schema::table('withdrawals', function (Blueprint $table) {
            $table->dropColumn('affiliate_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('withdrawals', function (Blueprint $table) {
            $table->foreignId('affiliate_id')->after('id')->constrained('affiliates')->cascadeOnDelete();
        });

        DB::table('withdrawals')
            ->where('withdrawable_type', Affiliate::class)
            ->update(['affiliate_id' => DB::raw('withdrawable_id')]);

        Schema::table('withdrawals', function (Blueprint $table) {
            $table->dropIndex('withdrawals_withdrawable_index');
            $table->dropIndex('withdrawals_status_created_index');
            $table->dropColumn(['withdrawable_type', 'withdrawable_id', 'completed_at']);
        });

        Schema::rename('withdrawals', 'affiliate_withdrawals');
    }
};

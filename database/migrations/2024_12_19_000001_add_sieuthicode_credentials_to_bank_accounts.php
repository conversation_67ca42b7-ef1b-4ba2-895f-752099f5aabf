<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bank_accounts', function (Blueprint $table) {
            $table->boolean('sieuthicode_enabled')->default(false)->after('is_visible');
            $table->string('sieuthicode_password')->nullable()->after('sieuthicode_enabled');
            $table->string('sieuthicode_token')->nullable()->after('sieuthicode_password');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bank_accounts', function (Blueprint $table) {
            $table->dropColumn(['sieuthicode_enabled', 'sieuthicode_password', 'sieuthicode_token']);
        });
    }
};

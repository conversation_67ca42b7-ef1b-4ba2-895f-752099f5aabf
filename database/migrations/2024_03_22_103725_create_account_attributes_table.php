<?php

declare(strict_types=1);

use App\Models\Account;
use App\Models\Attribute;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('account_attributes', function (Blueprint $table) {
            $table->foreignIdFor(Account::class);
            $table->foreignIdFor(Attribute::class);

            $table->primary(['account_id', 'attribute_id']);
            $table->unique(['account_id', 'attribute_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('account_attributes');
    }
};

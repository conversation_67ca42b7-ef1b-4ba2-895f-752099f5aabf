<?php

declare(strict_types=1);

use App\Enums\AttributeType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('attribute_sets', function (Blueprint $table) {
            $table->string('type', 20)->default(AttributeType::Dropdown->value)->after('slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('attribute_sets', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};

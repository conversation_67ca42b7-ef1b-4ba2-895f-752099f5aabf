<?php

declare(strict_types=1);

use App\Enums\AffiliateStatus;
use App\Models\AffiliateTier;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('affiliates', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(AffiliateTier::class, 'tier_id')->constrained()->cascadeOnDelete();
            $table->string('referral_code')->unique();
            $table->text('bio')->nullable();
            $table->string('status', 20)->default(AffiliateStatus::Pending->value);
            $table->decimal('total_commission', 12, 2)->default(0);
            $table->decimal('available_commission', 12, 2)->default(0);
            $table->decimal('total_referrals', 12, 2)->default(0);
            $table->decimal('total_sales', 12, 2)->default(0);
            $table->integer('total_clicks')->default(0);
            $table->integer('total_conversions')->default(0);
            $table->timestamp('approved_at')->nullable();
            $table->timestamp('suspended_at')->nullable();
            $table->text('rejection_reason')->nullable();
            $table->timestamps();

            $table->index(['status', 'approved_at']);
            $table->index('referral_code');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('affiliates');
    }
};

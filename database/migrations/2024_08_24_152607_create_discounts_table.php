<?php

declare(strict_types=1);

use App\Enums\DiscountScope;
use App\Enums\DiscountType;
use App\Models\Account;
use App\Models\Discount;
use App\Models\AccountCategory;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('discounts', function (Blueprint $table) {
            $table->id();
            $table->string('code')->unique();
            $table->decimal('value', 12)->default(0);
            $table->string('type', 20)->default(DiscountType::Fixed);
            $table->string('applied_to', 20)->default(DiscountScope::All);
            $table->boolean('is_unlimited')->default(false);
            $table->integer('limit')->default(0);
            $table->integer('usage')->default(0);
            $table->timestamp('start_at')->nullable();
            $table->timestamp('end_at')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('discount_category', function (Blueprint $table) {
            $table->foreignIdFor(Discount::class);
            $table->foreignIdFor(AccountCategory::class, 'category_id');
        });

        Schema::create('discount_account', function (Blueprint $table) {
            $table->foreignIdFor(Discount::class);
            $table->foreignIdFor(Account::class);
        });

        Schema::create('discount_user', function (Blueprint $table) {
            $table->foreignIdFor(Discount::class);
            $table->foreignIdFor(User::class);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('discount_category');
        Schema::dropIfExists('discount_account');
        Schema::dropIfExists('discount_user');
        Schema::dropIfExists('discounts');
    }
};

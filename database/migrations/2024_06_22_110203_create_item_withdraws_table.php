<?php

declare(strict_types=1);

use App\Enums\ItemWithdrawStatus;
use App\Models\GameItem;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('item_withdraws', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(GameItem::class);
            $table->foreignIdFor(User::class);
            $table->integer('quantity');
            $table->string('status')->default(ItemWithdrawStatus::Pending);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('item_withdraws');
    }
};

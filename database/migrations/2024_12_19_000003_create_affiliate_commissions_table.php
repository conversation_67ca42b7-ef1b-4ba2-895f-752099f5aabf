<?php

declare(strict_types=1);

use App\Enums\AffiliateCommissionStatus;
use App\Models\Affiliate;
use App\Models\PurchasedAccount;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('affiliate_commissions', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Affiliate::class)->constrained()->cascadeOnDelete();
            $table->foreignIdFor(PurchasedAccount::class)->nullable()->constrained()->cascadeOnDelete();
            $table->string('referral_code');
            $table->decimal('order_amount', 12, 2);
            $table->decimal('commission_rate', 5, 2);
            $table->decimal('commission_amount', 12, 2);
            $table->string('status', 20)->default(AffiliateCommissionStatus::Pending);
            $table->text('description')->nullable();
            $table->timestamp('approved_at')->nullable();
            $table->timestamps();

            $table->index(['affiliate_id', 'status']);
            $table->index(['referral_code', 'created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('affiliate_commissions');
    }
};

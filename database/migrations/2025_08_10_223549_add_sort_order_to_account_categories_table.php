<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('account_categories', function (Blueprint $table) {
            $table->integer('order')->default(0)->after('is_visible');
        });

        DB::table('account_categories')->update([
            'order' => DB::raw('id'),
        ]);
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('account_categories', function (Blueprint $table) {
            $table->dropColumn('order');
        });
    }
};

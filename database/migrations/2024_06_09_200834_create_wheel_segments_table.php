<?php

declare(strict_types=1);

use App\Models\GameItem;
use App\Models\Wheel;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('wheel_segments', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(Wheel::class);
            $table->string('reward_type', 20);
            $table->string('name');
            $table->float('probability');
            $table->integer('value')->nullable();
            $table->foreignIdFor(GameItem::class)->nullable();
            $table->integer('order')->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('wheel_segments');
    }
};

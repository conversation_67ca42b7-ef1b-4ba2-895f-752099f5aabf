<?php

declare(strict_types=1);

use App\Enums\DiscountType;
use App\Models\Account;
use App\Models\FlashSale;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('flash_sales', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->text('description')->nullable();
            $table->timestamp('start_at');
            $table->timestamp('end_at');
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('flash_sale_account', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(FlashSale::class);
            $table->foreignIdFor(Account::class);
            $table->string('type', 20)->default(DiscountType::Fixed);
            $table->decimal('value', 12)->default(0);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('flash_sale_account');
        Schema::dropIfExists('flash_sales');
    }
};

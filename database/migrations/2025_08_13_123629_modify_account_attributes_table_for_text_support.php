<?php

declare(strict_types=1);

use App\Models\Attribute;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('account_attributes', function (Blueprint $table) {
            $table->dropPrimary(['account_id', 'attribute_id']);
            $table->dropUnique(['account_id', 'attribute_id']);
            $table->foreignIdFor(Attribute::class)->nullable()->change();
            $table->foreignId('attribute_set_id')->nullable()->after('attribute_id');
            $table->id()->first();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('account_attributes', function (Blueprint $table) {
            $table->dropColumn(['id', 'attribute_set_id']);
            $table->foreignIdFor(Attribute::class)->nullable(false)->change();
            $table->primary(['account_id', 'attribute_id']);
            $table->unique(['account_id', 'attribute_id']);
        });
    }
};

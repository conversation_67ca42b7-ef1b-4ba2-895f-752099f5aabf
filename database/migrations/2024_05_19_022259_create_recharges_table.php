<?php

declare(strict_types=1);

use App\Enums\RechargeStatus;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharges', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class);
            $table->string('provider')->nullable();
            $table->string('request_id')->nullable();
            $table->string('type');
            $table->integer('amount')->nullable();
            $table->integer('declared_amount');
            $table->string('pin');
            $table->string('serial')->nullable();
            $table->string('status')->default(RechargeStatus::Pending);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharges');
    }
};

<?php

declare(strict_types=1);

use App\Models\User;
use App\Services\DepositCodeService;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('deposit_code', 20)->unique()->nullable()->after('balance');
        });

        User::query()->whereNull('deposit_code')->each(function (User $user) {
            $user->update(['deposit_code' => app(DepositCodeService::class)->generateDepositCode()]);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('deposit_code');
        });
    }
};

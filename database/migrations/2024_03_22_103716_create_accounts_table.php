<?php

declare(strict_types=1);

use App\Enums\AccountStatus;
use App\Models\AccountCategory;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('accounts', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class);
            $table->foreignIdFor(AccountCategory::class, 'category_id');
            $table->string('acc_name');
            $table->string('acc_pass')->nullable();
            $table->text('description')->nullable();
            $table->integer('price');
            $table->integer('compare_at_price')->nullable();
            $table->json('images')->nullable();
            $table->text('content')->nullable();
            $table->string('status', 20)->default(AccountStatus::Pending);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('accounts');
    }
};

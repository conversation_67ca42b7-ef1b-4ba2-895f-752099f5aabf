<?php

declare(strict_types=1);

use App\Models\CustomField;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('custom_fields', function (Blueprint $table) {
            $table->id();
            $table->nullableMorphs('custom_fieldable');
            $table->string('type');
            $table->boolean('required')->default(false);
            $table->string('name');
            $table->string('description')->nullable();
            $table->string('default_value')->nullable();
            $table->json('options')->nullable();
            $table->integer('order')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });

        Schema::create('custom_field_responses', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(CustomField::class);
            $table->morphs('respondable');
            $table->string('value');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('custom_fields');
        Schema::dropIfExists('custom_field_responses');
    }
};

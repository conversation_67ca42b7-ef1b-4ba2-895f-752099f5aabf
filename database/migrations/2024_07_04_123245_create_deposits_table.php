<?php

declare(strict_types=1);

use App\Enums\DepositStatus;
use App\Models\User;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('deposits', function (Blueprint $table) {
            $table->id();
            $table->foreignIdFor(User::class);
            $table->morphs('source');
            $table->string('provider')->nullable();
            $table->string('transaction_id')->nullable();
            $table->integer('amount');
            $table->text('content')->nullable();
            $table->string('status')->default(DepositStatus::Pending);
            $table->dateTime('transacted_at')->nullable();
            $table->text('raw')->nullable();
            $table->timestamps();
            $table->softDeletes();

            $table->unique(['provider', 'transaction_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('deposits');
    }
};

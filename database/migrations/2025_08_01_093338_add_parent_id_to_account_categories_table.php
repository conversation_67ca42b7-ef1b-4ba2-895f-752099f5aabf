<?php

declare(strict_types=1);

use App\Models\AccountCategory;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('account_categories', function (Blueprint $table) {
            $table->foreignIdFor(AccountCategory::class, 'parent_id')->nullable()->after('game_id');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('account_categories', function (Blueprint $table) {
            $table->dropForeignIdFor(AccountCategory::class, 'parent_id');
            $table->dropColumn('parent_id');
        });
    }
};

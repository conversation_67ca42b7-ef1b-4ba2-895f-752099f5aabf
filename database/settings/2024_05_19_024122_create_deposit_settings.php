<?php

declare(strict_types=1);

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
    public function up(): void
    {
        $this->migrator->add('deposit.recharge_type', 'manual');
        $this->migrator->add('deposit.recharge_provider', 'thesieure');
        $this->migrator->add('deposit.recharge_telecoms');
        $this->migrator->add('deposit.thesieure_partner_id');
        $this->migrator->add('deposit.thesieure_partner_key');
        $this->migrator->add('deposit.cardvip_partner_id');
        $this->migrator->add('deposit.cardvip_partner_key');
        $this->migrator->add('deposit.thecaosieure_partner_id');
        $this->migrator->add('deposit.thecaosieure_partner_key');
        $this->migrator->add('deposit.recharge_promotion', 0);
        $this->migrator->add('deposit.auto_pay_enabled', true);
        $this->migrator->add('deposit.auto_pay_webhook_auth_token', bin2hex(random_bytes(16)));
        $this->migrator->add('deposit.auto_pay_default_bank_account');
        $this->migrator->add('deposit.auto_pay_code_prefix', 'NAP TIEN');
        $this->migrator->add('deposit.auto_pay_promotion', 0);
        $this->migrator->add('deposit.auto_pay_min_amount', 1000);
        $this->migrator->add('deposit.auto_pay_max_amount', ********);
    }
};

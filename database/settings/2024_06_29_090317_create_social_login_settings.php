<?php

declare(strict_types=1);

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
    public function up(): void
    {
        $this->migrator->add('social_login.facebook_enabled', false);
        $this->migrator->add('social_login.facebook_client_id', config('services.facebook.client_id'));
        $this->migrator->add('social_login.facebook_client_secret', config('services.facebook.client_secret'));
        $this->migrator->add('social_login.google_enabled', false);
        $this->migrator->add('social_login.google_client_id', config('services.google.client_id'));
        $this->migrator->add('social_login.google_client_secret', config('services.google.client_secret'));
        $this->migrator->add('social_login.zalo_enabled', false);
        $this->migrator->add('social_login.zalo_client_id', config('services.zalo.client_id'));
        $this->migrator->add('social_login.zalo_client_secret', config('services.zalo.client_secret'));
    }
};

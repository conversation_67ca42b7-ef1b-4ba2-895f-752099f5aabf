<?php

declare(strict_types=1);

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
    public function up(): void
    {
        $this->migrator->add('general.site_name', config('app.name'));
        $this->migrator->add('general.site_title', config('app.name'));
        $this->migrator->add('general.logo');
        $this->migrator->add('general.favicon');
        $this->migrator->add('general.timezone', config('app.timezone'));
        $this->migrator->add('general.locale', config('app.locale'));
        $this->migrator->add('general.admin_email', [config('mail.from.address')]);
        $this->migrator->add('general.primary_color', '#4CAF50');
        $this->migrator->add('general.primary_font', 'Be Vietnam Pro');
        $this->migrator->add('general.banner_sliders');
    }
};

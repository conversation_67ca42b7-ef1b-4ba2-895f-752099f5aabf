<?php

declare(strict_types=1);

use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
    public function up(): void
    {
        $this->migrator->add('mail.driver', config('mail.default'));
        $this->migrator->add('mail.from_address', config('mail.from.address'));
        $this->migrator->add('mail.from_name', config('mail.from.name'));
        $this->migrator->add('mail.smtp_host', config('mail.mailers.smtp.host'));
        $this->migrator->add('mail.smtp_port', config('mail.mailers.smtp.port'));
        $this->migrator->add('mail.smtp_username', config('mail.mailers.smtp.username'));
        $this->migrator->add('mail.smtp_password', config('mail.mailers.smtp.password'));
        $this->migrator->add('mail.smtp_encryption', config('mail.mailers.smtp.encryption'));
        $this->migrator->add('mail.log_channel', config('mail.mailers.log.channel'));
        $this->migrator->add('mail.sendmail_path', config('mail.mailers.sendmail.path'));
        $this->migrator->add('mail.mailgun_domain');
        $this->migrator->add('mail.mailgun_secret');
        $this->migrator->add('mail.mailgun_endpoint');
        $this->migrator->add('mail.postmark_token');
        $this->migrator->add('mail.ses_key');
        $this->migrator->add('mail.ses_secret');
        $this->migrator->add('mail.ses_region');
        $this->migrator->add('mail.mailersend_api_key');
        $this->migrator->add('mail.resend_key', config('services.resend.key'));
    }
};

<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Attribute;
use App\Models\AttributeSet;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class AttributeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        AttributeSet::query()->truncate();
        Attribute::query()->truncate();

        $attributeSets = [
            [
                'category_id' => 1,
                'name' => 'Thẻ Vô Cực',
                'is_required' => true,
                'attributes' => [
                    'Có',
                    'Không',
                ],
            ],
            [
                'category_id' => 1,
                'name' => 'Đăng <PERSON>',
                'is_required' => true,
                'attributes' => [
                    'Facebook',
                    'Gmail',
                    'Vkontakte',
                ],
            ],
            [
                'category_id' => 1,
                'name' => 'Rank',
                'is_required' => true,
                'attributes' => [
                    'Đồng',
                    'Bạc',
                    'Vàng',
                    '<PERSON><PERSON><PERSON>',
                    '<PERSON>',
                    '<PERSON><PERSON><PERSON><PERSON>',
                    'Th<PERSON>ch <PERSON>ấ<PERSON>',
                ],
            ],
            [
                'category_id' => 1,
                'name' => 'Skin Súng',
                'is_required' => true,
                'attributes' => [
                    'Súng Vip',
                    'Súng Nâng Cấp',
                    'Súng Thường',
                ],
            ],
            [
                'category_id' => 2,
                'name' => 'Rank',
                'is_required' => true,
                'attributes' => [
                    'Đồng',
                    'Bạc',
                    'Vàng',
                    'Bạch Kim',
                    'Kim Cương',
                    'Tinh Anh',
                    'Cao Thủ',
                    'Chiến Tướng',
                    'Thách Đấu',
                ],
            ],
            [
                'category_id' => 2,
                'name' => 'Số Tướng',
                'is_required' => true,
                'attributes' => [
                    '10 - 30',
                    '30 - 50',
                    '50 - 70',
                    '70 - 100',
                    '100 - 130',
                ],
            ],
            [
                'category_id' => 2,
                'name' => 'Số Skin',
                'is_required' => true,
                'attributes' => [
                    '10 - 30',
                    '30 - 50',
                    '50 - 70',
                    '70 - 100',
                    '100 - 120',
                    '120 - 150',
                    '150 - 180',
                    '180 - 220',
                    '220 - 250',
                    '250 - 300',
                    '300 - 350',
                    '350 - 400',
                    '400 - 450',
                    '450 - 500',
                    '500 - 550',
                    '550 - 600',
                ],
            ],
            [
                'category_id' => 2,
                'name' => 'Đăng Ký',
                'is_required' => true,
                'attributes' => [
                    'Có thông tin đổi được',
                    'Trắng thông tin',
                    'Trắng thông tin liên kết FB rip, FB ảo',
                ],
            ],
            [
                'category_id' => 5,
                'name' => 'Server',
                'is_required' => true,
                'attributes' => array_map(fn($i) => "Server $i", range(1, 10)),
            ],
            [
                'category_id' => 5,
                'name' => 'Hành Tinh',
                'is_required' => true,
                'attributes' => [
                    'Xayda',
                    'Trái Đất',
                    'Namek',
                ],
            ],
            [
                'category_id' => 5,
                'name' => 'Đăng Ký',
                'is_required' => true,
                'attributes' => [
                    'Đăng ký ảo',
                    'Gmail xoá',
                    'Gmail full',
                ],
            ],
            [
                'category_id' => 5,
                'name' => 'Bông Tai',
                'is_required' => true,
                'attributes' => [
                    'Có',
                    'Không',
                ],
            ],
        ];

        foreach ($attributeSets as $attributeSet) {
            $attributes = $attributeSet['attributes'];

            $attributeSet = AttributeSet::query()->create([
                ...Arr::except($attributeSet, 'attributes'),
                'slug' => Str::slug($attributeSet['name']),
            ]);

            foreach ($attributes as $attribute) {
                $attributeSet->attributes()->create([
                    'name' => $attribute,
                    'slug' => Str::slug($attribute),
                ]);
            }
        }
    }
}

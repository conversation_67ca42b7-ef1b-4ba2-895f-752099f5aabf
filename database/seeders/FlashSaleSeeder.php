<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\DiscountType;
use App\Models\Account;
use App\Models\FlashSale;
use App\Models\FlashSaleAccount;
use Illuminate\Database\Seeder;

class FlashSaleSeeder extends Seeder
{
    public function run(): void
    {
        FlashSale::query()->truncate();
        FlashSaleAccount::query()->truncate();

        $flashSale = FlashSale::query()->create([
            'name' => 'Flash Sale',
            'start_at' => now(),
            'end_at' => now()->addDays(7),
            'is_active' => true,
        ]);

        $accounts = Account::query()->inRandomOrder()->take(rand(5, 20))->get();

        $flashSale->accounts()->createMany(
            $accounts->map(fn(Account $account) => [
                'account_id' => $account->getKey(),
                'type' => $type = fake()->randomElement([DiscountType::Fixed, DiscountType::Percentage]),
                'value' => $type === DiscountType::Fixed
                    ? $account->final_price * (1 - fake()->numberBetween(5, 50) / 100)
                    : fake()->numberBetween(5, 50),
            ])->all(),
        );
    }
}

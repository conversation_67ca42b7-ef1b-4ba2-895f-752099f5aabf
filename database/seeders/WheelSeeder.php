<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\RewardType;
use App\Models\Wheel;
use App\Models\WheelSegment;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;

class WheelSeeder extends Seeder
{
    public function run(): void
    {
        Wheel::query()->truncate();
        WheelSegment::query()->truncate();

        $wheels = [
            [
                'name' => 'Vòng quay mùa hè sôi động',
                'price' => 19000,
                'image' => 'wheels/wheel-1.png',
                'thumbnail' => 'wheels/thumb-1.png',
                'segments' => [
                    [
                        'reward_type' => RewardType::None,
                        'name' => '<PERSON><PERSON>c bạn may mắn lần sau',
                        'probability' => 90,
                        'order' => 1,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '100 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 100,
                        'probability' => 30,
                        'order' => 2,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '600 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 600,
                        'probability' => 20,
                        'order' => 3,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '2000 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 2000,
                        'probability' => 10,
                        'order' => 4,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '5000 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 5000,
                        'probability' => 5,
                        'order' => 5,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '8000 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 8000,
                        'probability' => 3,
                        'order' => 6,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '10000 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 10000,
                        'probability' => 2,
                        'order' => 7,
                    ],
                    [
                        'reward_type' => RewardType::Item,
                        'name' => '12000 Kim Cương',
                        'game_item_id' => 2,
                        'value' => 12000,
                        'probability' => 1,
                        'order' => 8,
                    ],
                ],
            ],
        ];

        foreach ($wheels as $key => $wheel) {
            $key++;

            $segments = Arr::pull($wheel, 'segments');

            $wheel = Wheel::query()->create([
                ...$wheel,
                'slug' => Str::slug($wheel['name']),
                'image' => "wheels/wheel-$key.png",
                'thumbnail' => "wheels/thumb-$key.gif",
            ]);

            $wheel->wheelSegments()->createMany($segments);
        }
    }
}

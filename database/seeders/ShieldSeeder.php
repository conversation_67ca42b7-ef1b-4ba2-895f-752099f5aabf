<?php

declare(strict_types=1);

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use BezhanSalleh\FilamentShield\Support\Utils;
use <PERSON>tie\Permission\PermissionRegistrar;

class ShieldSeeder extends Seeder
{
    public function run(): void
    {
        app()[PermissionRegistrar::class]->forgetCachedPermissions();

        $rolesWithPermissions = '[{"name":"super_admin","guard_name":"web","permissions":["view_account","view_any_account","create_account","update_account","restore_account","restore_any_account","replicate_account","reorder_account","delete_account","delete_any_account","force_delete_account","force_delete_any_account","view_account::category","view_any_account::category","create_account::category","update_account::category","restore_account::category","restore_any_account::category","replicate_account::category","reorder_account::category","delete_account::category","delete_any_account::category","force_delete_account::category","force_delete_any_account::category","view_ad","view_any_ad","create_ad","update_ad","restore_ad","restore_any_ad","replicate_ad","reorder_ad","delete_ad","delete_any_ad","force_delete_ad","force_delete_any_ad","view_affiliate","view_any_affiliate","create_affiliate","update_affiliate","restore_affiliate","restore_any_affiliate","replicate_affiliate","reorder_affiliate","delete_affiliate","delete_any_affiliate","force_delete_affiliate","force_delete_any_affiliate","view_affiliate::commission","view_any_affiliate::commission","create_affiliate::commission","update_affiliate::commission","restore_affiliate::commission","restore_any_affiliate::commission","replicate_affiliate::commission","reorder_affiliate::commission","delete_affiliate::commission","delete_any_affiliate::commission","force_delete_affiliate::commission","force_delete_any_affiliate::commission","view_affiliate::tier","view_any_affiliate::tier","create_affiliate::tier","update_affiliate::tier","restore_affiliate::tier","restore_any_affiliate::tier","replicate_affiliate::tier","reorder_affiliate::tier","delete_affiliate::tier","delete_any_affiliate::tier","force_delete_affiliate::tier","force_delete_any_affiliate::tier","view_attribute::set","view_any_attribute::set","create_attribute::set","update_attribute::set","restore_attribute::set","restore_any_attribute::set","replicate_attribute::set","reorder_attribute::set","delete_attribute::set","delete_any_attribute::set","force_delete_attribute::set","force_delete_any_attribute::set","view_authentication::log","view_any_authentication::log","create_authentication::log","update_authentication::log","restore_authentication::log","restore_any_authentication::log","replicate_authentication::log","reorder_authentication::log","delete_authentication::log","delete_any_authentication::log","force_delete_authentication::log","force_delete_any_authentication::log","view_bank::account","view_any_bank::account","create_bank::account","update_bank::account","restore_bank::account","restore_any_bank::account","replicate_bank::account","reorder_bank::account","delete_bank::account","delete_any_bank::account","force_delete_bank::account","force_delete_any_bank::account","view_category","view_any_category","create_category","update_category","restore_category","restore_any_category","replicate_category","reorder_category","delete_category","delete_any_category","force_delete_category","force_delete_any_category","view_deposit","view_any_deposit","create_deposit","update_deposit","restore_deposit","restore_any_deposit","replicate_deposit","reorder_deposit","delete_deposit","delete_any_deposit","force_delete_deposit","force_delete_any_deposit","view_discount","view_any_discount","create_discount","update_discount","restore_discount","restore_any_discount","replicate_discount","reorder_discount","delete_discount","delete_any_discount","force_delete_discount","force_delete_any_discount","view_flash::sale","view_any_flash::sale","create_flash::sale","update_flash::sale","restore_flash::sale","restore_any_flash::sale","replicate_flash::sale","reorder_flash::sale","delete_flash::sale","delete_any_flash::sale","force_delete_flash::sale","force_delete_any_flash::sale","view_game","view_any_game","create_game","update_game","restore_game","restore_any_game","replicate_game","reorder_game","delete_game","delete_any_game","force_delete_game","force_delete_any_game","view_game::item","view_any_game::item","create_game::item","update_game::item","restore_game::item","restore_any_game::item","replicate_game::item","reorder_game::item","delete_game::item","delete_any_game::item","force_delete_game::item","force_delete_any_game::item","view_item::withdraw","view_any_item::withdraw","create_item::withdraw","update_item::withdraw","restore_item::withdraw","restore_any_item::withdraw","replicate_item::withdraw","reorder_item::withdraw","delete_item::withdraw","delete_any_item::withdraw","force_delete_item::withdraw","force_delete_any_item::withdraw","view_menu","view_any_menu","create_menu","update_menu","restore_menu","restore_any_menu","replicate_menu","reorder_menu","delete_menu","delete_any_menu","force_delete_menu","force_delete_any_menu","view_post","view_any_post","create_post","update_post","restore_post","restore_any_post","replicate_post","reorder_post","delete_post","delete_any_post","force_delete_post","force_delete_any_post","view_publisher","view_any_publisher","create_publisher","update_publisher","restore_publisher","restore_any_publisher","replicate_publisher","reorder_publisher","delete_publisher","delete_any_publisher","force_delete_publisher","force_delete_any_publisher","view_purchased::account","view_any_purchased::account","create_purchased::account","update_purchased::account","restore_purchased::account","restore_any_purchased::account","replicate_purchased::account","reorder_purchased::account","delete_purchased::account","delete_any_purchased::account","force_delete_purchased::account","force_delete_any_purchased::account","view_recharge","view_any_recharge","create_recharge","update_recharge","restore_recharge","restore_any_recharge","replicate_recharge","reorder_recharge","delete_recharge","delete_any_recharge","force_delete_recharge","force_delete_any_recharge","view_role","view_any_role","create_role","update_role","delete_role","delete_any_role","view_transaction","view_any_transaction","create_transaction","update_transaction","restore_transaction","restore_any_transaction","replicate_transaction","reorder_transaction","delete_transaction","delete_any_transaction","force_delete_transaction","force_delete_any_transaction","view_user","view_any_user","create_user","update_user","restore_user","restore_any_user","replicate_user","reorder_user","delete_user","delete_any_user","force_delete_user","force_delete_any_user","view_wheel","view_any_wheel","create_wheel","update_wheel","restore_wheel","restore_any_wheel","replicate_wheel","reorder_wheel","delete_wheel","delete_any_wheel","force_delete_wheel","force_delete_any_wheel","view_wheel::spin","view_any_wheel::spin","create_wheel::spin","update_wheel::spin","restore_wheel::spin","restore_any_wheel::spin","replicate_wheel::spin","reorder_wheel::spin","delete_wheel::spin","delete_any_wheel::spin","force_delete_wheel::spin","force_delete_any_wheel::spin","view_withdrawal","view_any_withdrawal","create_withdrawal","update_withdrawal","restore_withdrawal","restore_any_withdrawal","replicate_withdrawal","reorder_withdrawal","delete_withdrawal","delete_any_withdrawal","force_delete_withdrawal","force_delete_any_withdrawal","page_Updater","page_ManageAppearance","page_ManageDeposit","page_ManageGeneral","page_ManageHome","page_ManageMail","page_ManageSocialLogin","widget_CollaboratorStatsWidget","widget_StatsOverview","widget_AffiliateCommissionStatsWidget","widget_UpdaterStatsWidget","widget_LatestTransactions","widget_LatestAuthenticationLog"]}]';
        $directPermissions = '[]';

        static::makeRolesWithPermissions($rolesWithPermissions);
        static::makeDirectPermissions($directPermissions);

        $this->command->info('Shield Seeding Completed.');
    }

    protected static function makeRolesWithPermissions(string $rolesWithPermissions): void
    {
        if (! blank($rolePlusPermissions = json_decode($rolesWithPermissions, true))) {
            /** @var Model $roleModel */
            $roleModel = Utils::getRoleModel();
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($rolePlusPermissions as $rolePlusPermission) {
                $role = $roleModel::firstOrCreate([
                    'name' => $rolePlusPermission['name'],
                    'guard_name' => $rolePlusPermission['guard_name'],
                ]);

                if (! blank($rolePlusPermission['permissions'])) {
                    $permissionModels = collect($rolePlusPermission['permissions'])
                        ->map(fn($permission) => $permissionModel::firstOrCreate([
                            'name' => $permission,
                            'guard_name' => $rolePlusPermission['guard_name'],
                        ]))
                        ->all();

                    $role->syncPermissions($permissionModels);
                }
            }
        }
    }

    public static function makeDirectPermissions(string $directPermissions): void
    {
        if (! blank($permissions = json_decode($directPermissions, true))) {
            /** @var Model $permissionModel */
            $permissionModel = Utils::getPermissionModel();

            foreach ($permissions as $permission) {
                if ($permissionModel::whereName($permission)->doesntExist()) {
                    $permissionModel::create([
                        'name' => $permission['name'],
                        'guard_name' => $permission['guard_name'],
                    ]);
                }
            }
        }
    }
}

<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\AccountCategory;
use App\Models\Game;
use App\Models\Publisher;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class PublisherSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        Publisher::query()->truncate();
        Game::query()->truncate();
        AccountCategory::query()->truncate();

        $publishers = [
            'Garena' => [
                'Free Fire' => [
                    'Tài khoản VIP' => [
                        'Tài khoản VIP Cao Cấp',
                        'Tài khoản VIP Trung Bình',
                        'Tài khoản VIP Cơ Bản',
                    ],
                    'Tài khoản Rác' => [
                        'Tài khoản <PERSON>',
                        'Tài khoản Rác Trung Bình',
                        'Tài khoản Rác <PERSON>',
                    ],
                    'Tài khoản Full Skin' => [
                        'Tà<PERSON> khoản Full Skin Hiếm',
                        'Tài khoản Full Skin Thường',
                    ],
                ],
                'Liên Quân <PERSON>' => [
                    'Tài khoản VIP' => [
                        'Tài khoản VIP Cao Cấp',
                        'Tài khoản VIP Trung Bình',
                        'Tài khoản VIP Cơ Bản',
                    ],
                    'Tài khoản Rác' => [
                        'Tài khoản Rác Cao Cấp',
                        'Tài khoản Rác Trung Bình',
                        'Tài khoản Rác Cơ Bản',
                    ],
                    'Tài khoản Full Hero' => [
                        'Tài khoản Full Hero Hiếm',
                        'Tài khoản Full Hero Thường',
                    ],
                ],
            ],
            'VNG' => [
                'Liên Minh Huyền Thoại' => [
                    'Tài khoản VIP' => [
                        'Tài khoản VIP Cao Cấp',
                        'Tài khoản VIP Trung Bình',
                        'Tài khoản VIP Cơ Bản',
                    ],
                    'Tài khoản Rác' => [
                        'Tài khoản Rác Cao Cấp',
                        'Tài khoản Rác Trung Bình',
                        'Tài khoản Rác Cơ Bản',
                    ],
                    'Tài khoản Full Champion' => [
                        'Tài khoản Full Champion Hiếm',
                        'Tài khoản Full Champion Thường',
                    ],
                ],
                'PUBG Mobile' => [
                    'Tài khoản VIP' => [
                        'Tài khoản VIP Cao Cấp',
                        'Tài khoản VIP Trung Bình',
                        'Tài khoản VIP Cơ Bản',
                    ],
                    'Tài khoản Rác' => [
                        'Tài khoản Rác Cao Cấp',
                        'Tài khoản Rác Trung Bình',
                        'Tài khoản Rác Cơ Bản',
                    ],
                    'Tài khoản Full Skin' => [
                        'Tài khoản Full Skin Hiếm',
                        'Tài khoản Full Skin Thường',
                    ],
                ],
            ],
            'Teamobi' => [
                'Ngọc Rồng Online' => [
                    'Tài khoản VIP' => [
                        'Tài khoản VIP Cao Cấp',
                        'Tài khoản VIP Trung Bình',
                        'Tài khoản VIP Cơ Bản',
                    ],
                    'Tài khoản Rác' => [
                        'Tài khoản Rác Cao Cấp',
                        'Tài khoản Rác Trung Bình',
                        'Tài khoản Rác Cơ Bản',
                    ],
                    'Tài khoản Cao Cấp' => [
                        'Tài khoản Cao Cấp Hiếm',
                        'Tài khoản Cao Cấp Thường',
                    ],
                ],
                'Ninja School' => [
                    'Tài khoản VIP' => [
                        'Tài khoản VIP Cao Cấp',
                        'Tài khoản VIP Trung Bình',
                        'Tài khoản VIP Cơ Bản',
                    ],
                    'Tài khoản Rác' => [
                        'Tài khoản Rác Cao Cấp',
                        'Tài khoản Rác Trung Bình',
                        'Tài khoản Rác Cơ Bản',
                    ],
                    'Tài khoản Cao Cấp' => [
                        'Tài khoản Cao Cấp Hiếm',
                        'Tài khoản Cao Cấp Thường',
                    ],
                ],
            ],
        ];

        foreach ($publishers as $publisherName => $games) {
            $publisher = Publisher::query()->create([
                'name' => $publisherName,
                'slug' => Str::slug($publisherName),
            ]);

            foreach ($games as $gameName => $categories) {
                $game = $publisher->games()->create([
                    'name' => $gameName,
                    'slug' => Str::slug($gameName),
                    'image' => 'account-categories/' . Str::slug($gameName) . '.gif',
                ]);

                foreach ($categories as $categoryName => $subcategories) {
                    $category = $game->categories()->create([
                        'name' => $categoryName,
                        'slug' => Str::slug($gameName) . '-' . Str::slug($categoryName),
                        'image' => 'account-categories/' . Str::slug($categoryName) . '.gif',
                    ]);

                    foreach ($subcategories as $subcategoryName) {
                        $game->categories()->create([
                            'parent_id' => $category->id,
                            'name' => $subcategoryName,
                            'slug' => Str::slug($gameName) . '-' . Str::slug($categoryName) . '-' . Str::slug($subcategoryName),
                            'image' => 'account-categories/' . Str::slug($subcategoryName) . '.gif',
                        ]);
                    }
                }
            }
        }
    }
}

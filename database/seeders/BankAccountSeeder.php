<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\Bank;
use App\Models\BankAccount;
use App\Settings\DepositSettings;
use Illuminate\Database\Seeder;

class BankAccountSeeder extends Seeder
{
    public function run(): void
    {
        BankAccount::query()->truncate();

        $bankAccount = BankAccount::query()->create([
            'name' => 'MB Bank',
            'bank_name' => Bank::MBBank,
            'account_number' => '*************',
            'account_holder' => 'NGO QUOC DAT',
            'logo' => 'banks/mbbank.png',
        ]);

        $depositSettings = app(DepositSettings::class);
        $depositSettings->auto_pay_default_bank_account = $bankAccount->getKey();
        $depositSettings->save();
    }
}

<?php

declare(strict_types=1);

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Storage;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        Schema::disableForeignKeyConstraints();

        $this->uploadFiles('telecoms');
        $this->uploadFiles('account-categories');
        $this->uploadFiles('accounts');
        $this->uploadFiles('banners');
        $this->uploadFiles('wheels');
        $this->uploadFiles('banks');
        $this->uploadFiles('posts');

        $this->call([
            SettingSeeder::class,
            MenuSeeder::class,
            ShieldSeeder::class,
            UserSeeder::class,
            PublisherSeeder::class,
            AttributeSeeder::class,
            AccountSeeder::class,
            FlashSaleSeeder::class,
            GameItemSeeder::class,
            WheelSeeder::class,
            BankAccountSeeder::class,
            CategorySeeder::class,
            PostSeeder::class,
            AffiliateTierSeeder::class,
        ]);
    }

    protected function uploadFiles(string $directory): void
    {
        Storage::deleteDirectory($directory);

        $files = glob(database_path("seeders/files/$directory/*"));

        if (empty($files)) {
            return;
        }

        Storage::makeDirectory($directory);

        collect($files)
            ->each(fn(string $file) => Storage::put($directory . '/' . basename($file), file_get_contents($file)));
    }
}

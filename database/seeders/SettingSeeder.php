<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Settings\DepositSettings;
use Illuminate\Database\Seeder;

class SettingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $telecoms = [
            [
                'name' => 'Vcoin',
                'logo' => 'telecoms/vcoin.png',
                'value' => 'VCOIN',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 1000000,
                        'fee' => 12.5,
                    ],
                    [
                        'value' => 2000000,
                        'fee' => 14.5,
                    ],
                    [
                        'value' => 5000000,
                        'fee' => 14.5,
                    ],
                    [
                        'value' => 10000000,
                        'fee' => 16.5,
                    ],
                ],
            ],
            [
                'name' => 'Vnmobi',
                'logo' => 'telecoms/vietnamobile.png',
                'value' => 'VNMOBI',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 30000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 18,
                    ],
                ],
            ],
            [
                'name' => 'Vinaphone',
                'logo' => 'telecoms/vinaphone.svg',
                'value' => 'VINAPHONE',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 30000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 11,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 12.5,
                    ],
                ],
            ],
            [
                'name' => 'Zing',
                'logo' => 'telecoms/zing.webp',
                'value' => 'ZING',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 30000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 10.5,
                    ],
                    [
                        'value' => 1000000,
                        'fee' => 10.5,
                    ],
                ],
            ],
            [
                'name' => 'Viettel',
                'logo' => 'telecoms/viettel.png',
                'value' => 'VIETTEL',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 13,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 15,
                    ],
                    [
                        'value' => 30000,
                        'fee' => 16,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 14,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 14,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 14,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 14.5,
                    ],
                    [
                        'value' => 1000000,
                        'fee' => 15,
                    ],
                ],
            ],
            [
                'name' => 'Mobifone',
                'logo' => 'telecoms/mobifone.png',
                'value' => 'MOBIFONE',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 30000,
                        'fee' => 18,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 16.5,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 16,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 15,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 15,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 13.5,
                    ],
                ],
            ],
            [
                'name' => 'Gate',
                'logo' => 'telecoms/gate.png',
                'value' => 'GATE',
                'amounts' => [
                    [
                        'value' => 10000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 20000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 30000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 300000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 1000000,
                        'fee' => 15.5,
                    ],
                    [
                        'value' => 2000000,
                        'fee' => 16.5,
                    ],
                    [
                        'value' => 5000000,
                        'fee' => 16.5,
                    ],
                    [
                        'value' => 10000000,
                        'fee' => 16.5,
                    ],
                ],
            ],
            [
                'name' => 'Garena',
                'logo' => 'telecoms/garena.png',
                'value' => 'GARENA',
                'amounts' => [
                    [
                        'value' => 20000,
                        'fee' => 13,
                    ],
                    [
                        'value' => 50000,
                        'fee' => 13,
                    ],
                    [
                        'value' => 100000,
                        'fee' => 13,
                    ],
                    [
                        'value' => 200000,
                        'fee' => 13,
                    ],
                    [
                        'value' => 500000,
                        'fee' => 13,
                    ],
                ],
            ],
        ];

        $depositSettings = app(DepositSettings::class);
        $depositSettings->recharge_telecoms = $telecoms;
        $depositSettings->save();
    }
}

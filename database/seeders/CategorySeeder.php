<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    public function run(): void
    {
        Category::query()->truncate();

        $categories = [
            'Tin Tức Game',
            'Hướng Dẫn Chơi Game',
            'Review Game',
            'Sự Kiện Game',
            'Mẹo Và Thủ Thuật',
            'Cập Nhật Game Mới',
            'Phỏng Vấn Game Thủ',
            'Esports',
            'Công Nghệ Game',
        ];

        foreach ($categories as $category) {
            Category::query()->create([
                'name' => $category,
                'slug' => Str::slug($category),
                'description' => fake()->paragraphs(2, true),
                'is_visible' => true,
            ]);
        }
    }
}

<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\TransactionType;
use App\Models\User;
use App\Services\DepositCodeService;
use Illuminate\Database\Seeder;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        User::query()->truncate();

        $admin = User::query()->create([
            'name' => 'Ngô Quốc Đạt',
            'username' => 'ngoquocdat',
            'email' => '<EMAIL>',
            'password' => '123456',
            'is_super_admin' => true,
            'deposit_code' => app(DepositCodeService::class)->generateDepositCode(),
        ]);

        $user = User::query()->create([
            'name' => 'Test User',
            'username' => 'test',
            'email' => '<EMAIL>',
            'password' => 'password',
            'deposit_code' => app(DepositCodeService::class)->generateDepositCode(),
        ]);

        $user->recordTransaction(
            TransactionType::AddBalance,
            2_000_000_000,
            $admin,
            'Cộng tiền',
        );

        $admin->recordTransaction(
            TransactionType::AddBalance,
            2_000_000_000,
            $admin,
            'Cộng tiền',
        );
    }
}

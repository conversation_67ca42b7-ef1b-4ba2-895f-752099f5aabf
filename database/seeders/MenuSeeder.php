<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\AccountCategory;
use Datlechin\FilamentMenuBuilder\Enums\LinkTarget;
use Datlechin\FilamentMenuBuilder\Models\Menu;
use Datlechin\FilamentMenuBuilder\Models\MenuItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class MenuSeeder extends Seeder
{
    public function run(): void
    {
        MenuItem::query()->truncate();
        Menu::query()->truncate();

        $menu = Menu::query()->create([
            'name' => 'Header',
        ]);

        $menu->locations()->create([
            'location' => 'header',
        ]);

        $items = [
            [
                'title' => 'Trang chủ',
                'url' => route('home'),
            ],
            [
                'title' => 'Nạp tiền',
                'url' => route('user.deposit.index'),
            ],
            [
                'title' => 'Mua acc',
                'url' => '#',
                'children' => [
                    [
                        'title' => 'Liên Quân Mobile',
                        'linkable_type' => AccountCategory::class,
                        'linkable_id' => 2,
                    ],
                    [
                        'title' => 'Ngọc Rồng Online',
                        'linkable_type' => AccountCategory::class,
                        'linkable_id' => 5,
                    ],
                    [
                        'title' => 'Ninja School',
                        'linkable_type' => AccountCategory::class,
                        'linkable_id' => 6,
                    ],
                ],
            ],
            [
                'title' => 'Tin tức',
                'url' => route('blog.index'),
            ],
        ];

        foreach ($items as $index => $item) {
            $children = Arr::pull($item, 'children', []);

            $menuItem = $menu->menuItems()->create([
                ...$item,
                'target' => LinkTarget::Self,
                'order' => ++$index,
            ]);

            foreach ($children as $childIndex => $child) {
                $menuItem->children()->create([
                    ...$child,
                    'menu_id' => $menu->getKey(),
                    'target' => LinkTarget::Self,
                    'order' => ++$childIndex,
                ]);
            }
        }
    }
}

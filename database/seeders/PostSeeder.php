<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\PostStatus;
use App\Models\Category;
use App\Models\Post;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Str;

class PostSeeder extends Seeder
{
    public function run(): void
    {
        Post::query()->truncate();

        $posts = [
            'Hướng Dẫn Chơi Liên Quân Mobile: Những Mẹo Và Thủ Thuật Cho Người Mới Bắt Đầu',
            'Top 10 Game Mobile Đáng Chơi Nhất Năm 2024',
            'Review Genshin Impact: Thế Giới Mở Tuyệt Đẹp Và Gameplay Hấp Dẫn',
            'Cách Kiếm Vàng Nhanh Trong PUBG Mobile',
            'Sự Kiện Đặc Biệt Của Free Fire: Nhận Miễn <PERSON>í Skin Và Vật Phẩm',
            '<PERSON>ến <PERSON>c Leo Rank Hiệu Qu<PERSON>orant',
            'Những Phụ Kiện Game Nên Mua Để Trải Nghiệm Tốt Hơn',
            '<PERSON><PERSON><PERSON><PERSON> Đấu Esports: Những Điều Bạn <PERSON>',
            'Minecraft: Những <PERSON>ông Trình Đẹp Nhất Do Người Chơi Tạo Ra',
            'Cập Nhật Mới Nhất Của Liên Minh Huyền Thoại: Những Thay Đổi Đáng Chú Ý',
            'Phỏng Vấn Game Thủ Chuyên Nghiệp: Hành Trình Trở Thành Nhà Vô Địch',
            'Tại Sao Fortnite Vẫn Là Game Hấp Dẫn Nhất Năm 2024?',
            'Top 5 Chiến Thuật Hiệu Quả Trong Clash of Clans',
            'Những Điều Bạn Cần Biết Về Apex Legends Mobile',
            'Nhận Định Game FIFA Online 4: Chiến Thuật Và Lối Chơi Tốt Nhất',
        ];

        $categories = Category::query()->pluck('id');
        $users = User::query()->pluck('id');
        $content = File::get(database_path('seeders/contents/post.md'));

        foreach ($posts as $post) {
            Post::query()->create([
                'category_id' => $categories->random(),
                'user_id' => $users->random(),
                'title' => $post,
                'slug' => Str::slug($post),
                'description' => fake()->sentence(),
                'content' => $content,
                'image' => sprintf('posts/%s.jpg', fake()->numberBetween(1, 9)),
                'status' => PostStatus::Published,
                'created_at' => fake()->dateTimeBetween('-1 month'),
                'views' => fake()->numberBetween(100, 1000),
            ]);
        }
    }
}

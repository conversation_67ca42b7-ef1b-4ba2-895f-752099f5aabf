<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Enums\AccountStatus;
use App\Models\Account;
use App\Models\AccountCategory;
use App\Models\AttributeSet;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AccountSeeder extends Seeder
{
    public function run(): void
    {
        DB::table('account_attributes')->truncate();
        Account::query()->truncate();

        $user = User::query()->first();
        $attributeSets = AttributeSet::query()->with('attributes')->get();
        $categories = AccountCategory::query()->orderBy('order')->get();

        foreach (range(1, 1000) as $ignored) {
            $images = [];
            $category = $categories->random();

            $prefix = match ($category->game->name) {
                'Free Fire' => 'ff-',
                'Liên Qu<PERSON>' => 'lq-',
                '<PERSON><PERSON><PERSON>' => 'lmht-',
                'PUBG Mobile' => 'pubg-',
                'Ngọc Rồng Online' => 'nro-',
                'Ninja School' => 'ns-',
                default => 'acc-',
            };

            foreach (range(1, fake()->numberBetween(1, 5)) as $ignored) {
                $images[] = sprintf('accounts/%s%s.jpg', $prefix, fake()->numberBetween(1, 10));
            }

            $account = Account::query()->create([
                'user_id' => $user->getKey(),
                'category_id' => $category->id,
                'acc_name' => fake()->userName(),
                'acc_pass' => fake()->password(),
                'price' => round(fake()->numberBetween(1_000, 10_000_000), -3),
                'compare_at_price' => fake()->boolean() ? round(fake()->numberBetween(1_000, 10_000_000), -3) : null,
                'description' => fake()->paragraph(1),
                'images' => $images,
                'content' => fake()->paragraph(3),
                'status' => AccountStatus::Selling,
                'created_at' => fake()->dateTimeBetween('-1 month'),
            ]);

            $filteredAttributeSets = $attributeSets->where('category_id', $category->id);

            if ($filteredAttributeSets->isNotEmpty()) {
                foreach ($filteredAttributeSets as $attributeSet) {
                    $account->attributes()->attach($attributeSet->attributes->random());
                }
            }
        }
    }
}

<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\AffiliateTier;
use Illuminate\Database\Seeder;

class AffiliateTierSeeder extends Seeder
{
    public function run(): void
    {
        $tiers = [
            [
                'name' => 'Đồng',
                'slug' => 'bronze',
                'commission_rate' => 5.00,
                'min_commission_required' => 0,
                'description' => 'Cấp bậc mặc định cho affiliate mới',
                'color' => '#CD7F32',
                'is_default' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Bạc',
                'slug' => 'silver',
                'commission_rate' => 10.00,
                'min_commission_required' => 1000000,
                'description' => 'Cấp bậc bạc - hoa hồng 10%',
                'color' => '#C0C0C0',
                'is_default' => false,
                'sort_order' => 2,
            ],
            [
                'name' => 'Vàng',
                'slug' => 'gold',
                'commission_rate' => 15.00,
                'min_commission_required' => 5000000,
                'description' => 'Cấp bậc vàng - hoa hồng cao nhất 15%',
                'color' => '#FFD700',
                'is_default' => false,
                'sort_order' => 3,
            ],
        ];

        foreach ($tiers as $tier) {
            AffiliateTier::firstOrCreate(
                ['slug' => $tier['slug']],
                $tier,
            );
        }
    }
}

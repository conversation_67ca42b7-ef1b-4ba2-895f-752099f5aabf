<?php

declare(strict_types=1);

namespace Database\Seeders;

use App\Models\GameItem;
use Illuminate\Database\Seeder;
use Illuminate\Support\Arr;

class GameItemSeeder extends Seeder
{
    public function run(): void
    {
        GameItem::query()->truncate();

        $items = [
            [
                'name' => 'Quân Huy',
                'withdraw_packages' => [220, 440, 600, 1000, 2000],
            ],
            [
                'name' => 'Kim Cương',
                'withdraw_packages' => [100, 200, 300, 500, 1000],
            ],
        ];

        foreach ($items as $item) {
            $packages = collect(Arr::pull($item, 'withdraw_packages'))->mapWithKeys(
                fn($package) => [$package => [
                    'name' => "$package $item[name]",
                    'value' => $package,
                    'description' => "Gói rút $package {$item['name']}",
                ]],
            )->all();

            GameItem::create([
                ...$item,
                'withdraw_packages' => $packages,
            ]);
        }
    }
}

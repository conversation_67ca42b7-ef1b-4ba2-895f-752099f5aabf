<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\Bank;
use App\Models\BankAccount;
use Illuminate\Database\Eloquent\Factories\Factory;

class BankAccountFactory extends Factory
{
    protected $model = BankAccount::class;

    public function definition(): array
    {
        return [
            'name' => $this->faker->company(),
            'bank_name' => $this->faker->randomElement(Bank::cases()),
            'account_number' => $this->faker->numerify('##########'),
            'account_holder' => $this->faker->name(),
            'is_visible' => true,
            'sieuthicode_enabled' => false,
            'sieuthicode_password' => null,
            'sieuthicode_token' => null,
            'logo' => null,
        ];
    }
}

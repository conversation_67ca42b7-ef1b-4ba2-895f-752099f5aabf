<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\AccountCategory;
use App\Models\Game;
use App\Models\Publisher;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\AccountCategory>
 */
class AccountCategoryFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->unique()->words(2, true);

        return [
            'publisher_id' => Publisher::factory(),
            'game_id' => Game::factory(),
            'parent_id' => null,
            'name' => $name,
            'slug' => Str::slug($name),
            'image' => $this->faker->imageUrl,
            'description' => $this->faker->text,
            'is_visible' => $this->faker->boolean,
        ];
    }

    /**
     * Tạo category con
     */
    public function child(AccountCategory $parent): static
    {
        return $this->state(fn(array $attributes) => [
            'parent_id' => $parent->id,
            'publisher_id' => $parent->publisher_id,
            'game_id' => $parent->game_id,
        ]);
    }
}

<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Models\Publisher;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Game>
 */
class GameFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $name = $this->faker->unique()->words(2, true);

        return [
            'publisher_id' => Publisher::factory(),
            'name' => $name,
            'slug' => Str::slug($name),
            'image' => $this->faker->imageUrl,
            'description' => $this->faker->text,
            'is_visible' => $this->faker->boolean(80),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\User;

class DepositCodeService
{
    private const ALLOWED_LETTERS = [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'J',
        'K',
        'M',
        'N',
        'O',
        'P',
        'Q',
        'S',
        'T',
        'U',
        'V',
        'Y',
        'Z',
    ];

    public function generateDepositCode(): string
    {
        do {
            $letters = '';

            for ($i = 0; $i < 4; $i++) {
                $letters .= self::ALLOWED_LETTERS[array_rand(self::ALLOWED_LETTERS)];
            }

            $numbers = str_pad((string) rand(0, 99), 2, '0', STR_PAD_LEFT);

            $depositCode = $letters . $numbers;
        } while (User::where('deposit_code', $depositCode)->exists());

        return $depositCode;
    }

    public function findUserByCode(string $code): ?User
    {
        return User::where('deposit_code', strtoupper($code))->first();
    }
}

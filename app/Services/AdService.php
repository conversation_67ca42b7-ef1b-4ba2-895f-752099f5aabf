<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\Ad;
use Illuminate\Support\Collection;

class AdService
{
    public function getAdsByPosition(string $position): Collection
    {
        return Ad::query()
            ->active()
            ->byPosition($position)
            ->ordered()
            ->get();
    }

    public function getAllAdsByPositions(): array
    {
        $ads = Ad::query()
            ->active()
            ->ordered()
            ->get()
            ->groupBy('position');

        $positions = $this->getPositions();
        $result = [];

        foreach (array_keys($positions) as $position) {
            $result[$position] = $ads->get($position, collect());
        }

        return $result;
    }

    public function renderAd(Ad $ad): string
    {
        return match ($ad->type) {
            'html' => $this->renderHtmlAd($ad),
            'image' => $this->renderImageAd($ad),
            'banner' => $this->renderBannerAd($ad),
            'script' => $this->renderScriptAd($ad),
            default => '',
        };
    }

    public function renderAdsByPosition(string $position): string
    {
        $ads = $this->getAdsByPosition($position);

        if ($ads->isEmpty()) {
            return '';
        }

        $html = '';
        foreach ($ads as $ad) {
            $html .= $this->renderAd($ad);
        }

        return $html;
    }

    private function renderHtmlAd(Ad $ad): string
    {
        $content = $ad->content;

        if ($ad->link_url) {
            $content = "<a href=\"{$ad->link_url}\" target=\"_blank\" rel=\"noopener noreferrer\">{$content}</a>";
        }

        return "<div class=\"ad-widget ad-{$ad->type} ad-{$ad->position}\">{$content}</div>";
    }

    private function renderImageAd(Ad $ad): string
    {
        $imgTag = "<img src=\"{$ad->content}\" alt=\"{$ad->name}\" class=\"w-full h-auto\" />";

        if ($ad->link_url) {
            $imgTag = "<a href=\"{$ad->link_url}\" target=\"_blank\" rel=\"noopener noreferrer\">{$imgTag}</a>";
        }

        return "<div class=\"ad-widget ad-{$ad->type} ad-{$ad->position}\">{$imgTag}</div>";
    }

    private function renderBannerAd(Ad $ad): string
    {
        $bannerHtml = "<div class=\"banner-ad\" style=\"background-image: url('{$ad->content}');\"></div>";

        if ($ad->link_url) {
            $bannerHtml = "<a href=\"{$ad->link_url}\" target=\"_blank\" rel=\"noopener noreferrer\">{$bannerHtml}</a>";
        }

        return "<div class=\"ad-widget ad-{$ad->type} ad-{$ad->position}\">{$bannerHtml}</div>";
    }

    private function renderScriptAd(Ad $ad): string
    {
        return "<div class=\"ad-widget ad-{$ad->type} ad-{$ad->position}\">{$ad->content}</div>";
    }

    public function getPositions(): array
    {
        return Ad::getPositions();
    }

    public function getTypes(): array
    {
        return Ad::getTypes();
    }
}

<?php

declare(strict_types=1);

namespace App\Services;

use App\Models\BankAccount;
use App\Settings\DepositSettings;
use Exception;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class SieuthicodeService
{
    private PendingRequest $http;

    public function __construct(private DepositSettings $settings)
    {
        $this->http = Http::timeout(30)
            ->retry(3, 1000)
            ->withHeaders([
                'User-Agent' => 'LashGame/1.0',
            ]);
    }

    public function getTransactions(BankAccount $bankAccount): array
    {
        if (! $bankAccount->sieuthicode_enabled) {
            Log::info('Sieuthicode: Disabled for bank account', [
                'bank' => $bankAccount->bank_name->value,
                'account_number' => $bankAccount->account_number,
            ]);
            return [];
        }

        if (! $bankAccount->sieuthicode_password || ! $bankAccount->sieuthicode_token) {
            Log::warning('Sieuthicode: Missing credentials for bank account', [
                'bank' => $bankAccount->bank_name->value,
                'account_number' => $bankAccount->account_number,
            ]);
            return [];
        }

        $endpoint = $this->getEndpoint($bankAccount->bank_name->value);

        if (! $endpoint) {
            Log::error('Sieuthicode: Unsupported bank', [
                'bank' => $bankAccount->bank_name->value,
                'account_number' => $bankAccount->account_number,
            ]);
            return [];
        }

        $url = sprintf(
            '%s/%s/%s/%s',
            $endpoint,
            $bankAccount->sieuthicode_password,
            $bankAccount->account_number,
            $bankAccount->sieuthicode_token,
        );

        try {
            $response = $this->http->get($url);

            if (! $response->successful()) {
                Log::error('Sieuthicode API error', [
                    'status' => $response->status(),
                    'body' => $response->body(),
                    'bank' => $bankAccount->bank_name->value,
                    'account_number' => $bankAccount->account_number,
                ]);
                return [];
            }

            $data = $response->json();

            if (! isset($data['status']) || $data['status'] !== 'success') {
                return [];
            }

            return $data['transactions'] ?? [];
        } catch (Exception $e) {
            Log::error('Sieuthicode API exception', [
                'message' => $e->getMessage(),
                'bank' => $bankAccount->bank_name->value,
                'account_number' => $bankAccount->account_number,
            ]);
            return [];
        }
    }

    private function getEndpoint(string $bankName): ?string
    {
        return match ($bankName) {
            'TPBank' => 'https://api.sieuthicode.net/historyapitpbv3',
            'Vietcombank' => 'https://api.sieuthicode.net/historyapivcbv3',
            'ACB' => 'https://api.sieuthicode.net/historyapiacbv3',
            'MBBank' => 'https://api.sieuthicode.net/historyapimbv3',
            'BIDV' => 'https://api.sieuthicode.net/historyapibidvv3',
            'VietinBank' => 'https://api.sieuthicode.net/historyapiviettinv3',
            'SEABANK' => 'https://api.sieuthicode.net/historyapiseabankv3',
            'MSB' => 'https://api.sieuthicode.net/historyapimsbv3',
            'TIMO' => 'https://api.sieuthicode.net/historyapitimov3',
            default => null,
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Number;
use ZipArchive;

class UpdaterService
{
    private string $updateServerUrl;
    private string $currentVersion;
    private string $appPath;

    public function __construct()
    {
        $this->updateServerUrl = config('app.update_server_url', 'https://license-server.nqd.vn');
        $this->currentVersion = config('app.version', '1.0.0');
        $this->appPath = base_path();
    }

    public function checkForUpdates(): array
    {
        try {
            $response = Http::timeout(30)->get($this->updateServerUrl . '/api/v1/updates/check', [
                'license_key' => config('app.license_key'),
                'current_version' => $this->currentVersion,
                'product_slug' => config('app.product_slug', 'lashgame'),
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            return [
                'success' => false,
                'message' => 'Không thể kết nối đến license server',
                'has_update' => false,
            ];
        } catch (Exception $e) {
            Log::error('Update check failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Lỗi khi kiểm tra update: ' . $e->getMessage(),
                'has_update' => false,
            ];
        }
    }

    public function downloadUpdate(string $version): array
    {
        try {
            $downloadUrl = $this->updateServerUrl . '/api/v1/updates/download/' . $version;
            $tempPath = storage_path('app/updates/temp_' . $version . '.zip');

            if (! File::exists(dirname($tempPath))) {
                File::makeDirectory(dirname($tempPath), 0755, true);
            }

            $response = Http::timeout(300)
                ->withHeaders([
                    'Authorization' => 'Bearer ' . config('app.license_key'),
                ])
                ->get($downloadUrl);

            if ($response->successful()) {
                File::put($tempPath, $response->body());

                return [
                    'success' => true,
                    'file_path' => $tempPath,
                    'message' => 'Download thành công',
                ];
            }

            return [
                'success' => false,
                'message' => $response->json('message'),
            ];
        } catch (Exception $e) {
            Log::error('Update download failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Lỗi khi tải xuống bản cập nhật: ' . $e->getMessage(),
            ];
        }
    }

    public function installUpdate(string $filePath, ?string $newVersion = null): array
    {
        try {
            $zip = new ZipArchive();

            if ($zip->open($filePath) !== true) {
                return [
                    'success' => false,
                    'message' => 'Không thể mở file cập nhật',
                ];
            }

            $this->createBackup();

            $extractPath = storage_path('app/updates/extract_' . time());
            $zip->extractTo($extractPath);
            $zip->close();

            $this->copyFiles($extractPath, $this->appPath);

            $this->runComposerInstallIfNeeded($extractPath);

            $this->runMigrations();

            $this->clearCache();

            if ($newVersion) {
                $this->updateVersion($newVersion);
            }

            File::deleteDirectory($extractPath);
            File::delete($filePath);

            return [
                'success' => true,
                'message' => 'Cập nhật thành công',
                'new_version' => $newVersion,
            ];
        } catch (Exception $e) {
            Log::error('Update installation failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Lỗi khi cài đặt cập nhật: ' . $e->getMessage(),
            ];
        }
    }

    private function createBackup(): void
    {
        $backupPath = storage_path('app/backups/backup_' . date('Y-m-d_H-i-s'));

        if (! File::exists(dirname($backupPath))) {
            File::makeDirectory(dirname($backupPath), 0755, true);
        }

        $directories = ['app', 'config', 'database', 'resources', 'routes'];

        foreach ($directories as $dir) {
            $sourcePath = base_path($dir);
            $destPath = $backupPath . '/' . $dir;

            if (File::exists($sourcePath)) {
                File::copyDirectory($sourcePath, $destPath);
            }
        }
    }

    private function copyFiles(string $source, string $destination): void
    {
        $files = File::allFiles($source);

        foreach ($files as $file) {
            $relativePath = $file->getRelativePathname();
            $destPath = $destination . '/' . $relativePath;

            if (! File::exists(dirname($destPath))) {
                File::makeDirectory(dirname($destPath), 0755, true);
            }

            File::copy($file->getPathname(), $destPath);
        }
    }

    private function runMigrations(): void
    {
        try {
            Artisan::call('migrate', ['--force' => true]);
        } catch (Exception $e) {
            Log::error('Migration failed during cập nhật: ' . $e->getMessage());
        }
    }

    private function clearCache(): void
    {
        try {
            Artisan::call('optimize:clear');
        } catch (Exception $e) {
            Log::error('Cache clear failed during cập nhật: ' . $e->getMessage());
        }
    }

    private function runComposerInstallIfNeeded(string $extractPath): void
    {
        try {
            $currentComposerLock = base_path('composer.lock');
            $newComposerLock = $extractPath . '/composer.lock';

            if (File::exists($newComposerLock) && File::exists($currentComposerLock)) {
                $currentHash = hash_file('sha256', $currentComposerLock);
                $newHash = hash_file('sha256', $newComposerLock);

                if ($currentHash !== $newHash) {
                    $command = 'cd ' . base_path() . ' && composer install --no-dev --optimize-autoloader --no-interaction';
                    $output = shell_exec($command . ' 2>&1');

                    if ($output === null) {
                        Log::error('Failed to run composer install');
                        throw new Exception('Failed to run composer install');
                    }
                }
            } elseif (File::exists($newComposerLock)) {
                $command = 'cd ' . base_path() . ' && composer install --no-dev --optimize-autoloader --no-interaction';
                $output = shell_exec($command . ' 2>&1');

                if ($output === null) {
                    Log::error('Failed to run composer install');
                    throw new Exception('Failed to run composer install');
                }
            }
        } catch (Exception $e) {
            Log::error('Composer install failed during update: ' . $e->getMessage());
            throw $e;
        }
    }

    public function getCurrentVersion(): string
    {
        return $this->currentVersion;
    }

    public function getServerRequirements(): array
    {
        $requirements = [
            'php_version' => [
                'required' => '8.3.0',
                'current' => PHP_VERSION,
                'status' => version_compare(PHP_VERSION, '8.3.0', '>='),
            ],
            'extensions' => [
                'zip' => [
                    'required' => true,
                    'current' => extension_loaded('zip'),
                    'status' => extension_loaded('zip'),
                ],
                'curl' => [
                    'required' => true,
                    'current' => extension_loaded('curl'),
                    'status' => extension_loaded('curl'),
                ],
                'openssl' => [
                    'required' => true,
                    'current' => extension_loaded('openssl'),
                    'status' => extension_loaded('openssl'),
                ],
                'mbstring' => [
                    'required' => true,
                    'current' => extension_loaded('mbstring'),
                    'status' => extension_loaded('mbstring'),
                ],
            ],
            'permissions' => [
                'storage_writable' => [
                    'required' => true,
                    'current' => is_writable(storage_path()),
                    'status' => is_writable(storage_path()),
                ],
                'bootstrap_cache_writable' => [
                    'required' => true,
                    'current' => is_writable(base_path('bootstrap/cache')),
                    'status' => is_writable(base_path('bootstrap/cache')),
                ],
            ],
            'disk_space' => [
                'required' => '500MB',
                'current' => Number::fileSize((int) disk_free_space(base_path())),
                'status' => (disk_free_space(base_path()) ?: 0) > 500 * 1024 * 1024, // 500MB
            ],
        ];

        $requirements['can_update'] =
            $requirements['php_version']['status'] &&
            $requirements['extensions']['zip']['status'] &&
            $requirements['extensions']['curl']['status'] &&
            $requirements['extensions']['openssl']['status'] &&
            $requirements['extensions']['mbstring']['status'] &&
            $requirements['permissions']['storage_writable']['status'] &&
            $requirements['permissions']['bootstrap_cache_writable']['status'] &&
            $requirements['disk_space']['status'];

        return $requirements;
    }

    private function updateVersion(string $newVersion): void
    {
        try {
            $envPath = base_path('.env');
            if (File::exists($envPath)) {
                $envContent = File::get($envPath);

                if (preg_match('/^APP_VERSION\s*=/m', $envContent)) {
                    $envContent = preg_replace(
                        '/^APP_VERSION\s*=\s*.*$/m',
                        "APP_VERSION={$newVersion}",
                        $envContent,
                    );
                } else {
                    $envContent .= "\nAPP_VERSION={$newVersion}";
                }

                File::put($envPath, $envContent);
            }

            $configPath = config_path('app.php');
            if (File::exists($configPath)) {
                $configContent = File::get($configPath);
                $configContent = preg_replace(
                    "/'version'\s*=>\s*['\"][^'\"]*['\"]/",
                    "'version' => '{$newVersion}'",
                    $configContent,
                );
                File::put($configPath, $configContent);
            }

            $this->currentVersion = $newVersion;

            Log::info("Version updated to: {$newVersion}");
        } catch (Exception $e) {
            Log::error('Failed to update version: ' . $e->getMessage());
        }
    }

    public function validateLicense(): array
    {
        try {
            $response = Http::timeout(30)->post($this->updateServerUrl . '/api/v1/license/validate', [
                'license_key' => config('app.license_key'),
                'domain' => request()->getHost(),
                'product_slug' => config('app.product_slug', 'lashgame'),
            ]);

            if ($response->successful()) {
                return $response->json();
            }

            return [
                'success' => false,
                'message' => $response->json('message'),
            ];
        } catch (Exception $e) {
            Log::error('License validation failed: ' . $e->getMessage());

            return [
                'success' => false,
                'message' => 'Lỗi khi validate license: ' . $e->getMessage(),
            ];
        }
    }

    public function getUpdateHistory(): array
    {
        $historyFile = storage_path('app/updates/history.json');

        if (File::exists($historyFile)) {
            return json_decode(File::get($historyFile), true) ?? [];
        }

        return [];
    }

    public function logUpdate(string $version, string $status): void
    {
        $history = $this->getUpdateHistory();

        $history[] = [
            'version' => $version,
            'status' => $status,
            'timestamp' => now()->toISOString(),
        ];

        $historyFile = storage_path('app/updates/history.json');

        if (! File::exists(dirname($historyFile))) {
            File::makeDirectory(dirname($historyFile), 0755, true);
        }

        File::put($historyFile, json_encode($history, JSON_PRETTY_PRINT));
    }

    public function autoUpdate(): array
    {
        $checkResult = $this->checkForUpdates();

        if (! $checkResult['success'] || !$checkResult['has_update']) {
            return [
                'success' => false,
                'message' => 'Không có cập nhật mới hoặc lỗi khi kiểm tra',
            ];
        }

        $version = $checkResult['latest_version'];

        $downloadResult = $this->downloadUpdate($version);

        if (! $downloadResult['success']) {
            return $downloadResult;
        }

        $installResult = $this->installUpdate($downloadResult['file_path'], $version);

        if ($installResult['success']) {
            $installResult['new_version'] = $version;
        }

        return $installResult;
    }
}

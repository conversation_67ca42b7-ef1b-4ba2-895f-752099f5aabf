<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\AffiliateCommissionStatus;
use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use App\Models\AffiliateCommission;
use App\Models\AffiliateTier;
use Illuminate\Support\Collection;

class AffiliateCommissionService
{
    public function calculateCommission(float $orderAmount, float $commissionRate): float
    {
        return ($orderAmount * $commissionRate) / 100;
    }

    public function getTotalApprovedCommission(Affiliate $affiliate): float
    {
        return (float) $affiliate
            ->commissions()
            ->where('status', AffiliateCommissionStatus::Approved)
            ->sum('commission_amount');
    }

    public function getTotalPendingCommission(Affiliate $affiliate): float
    {
        return (float) $affiliate->commissions()
            ->where('status', AffiliateCommissionStatus::Pending)
            ->sum('commission_amount');
    }

    public function getMonthlyCommissionStats(Affiliate $affiliate, int $year, int $month): Collection
    {
        return $affiliate->commissions()
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->get()
            ->groupBy('status')
            ->map(function ($commissions) {
                return [
                    'count' => $commissions->count(),
                    'amount' => $commissions->sum('commission_amount'),
                ];
            });
    }

    public function getTopAffiliatesByRevenue(int $limit = 10): Collection
    {
        return Affiliate::query()
            ->where('status', AffiliateStatus::Approved)
            ->withSum('commissions as total_commission', 'commission_amount')
            ->orderByDesc('total_commission')
            ->limit($limit)
            ->get();
    }

    public function getTopAffiliatesByOrders(int $limit = 10): Collection
    {
        return Affiliate::query()
            ->where('status', AffiliateStatus::Approved)
            ->withCount('commissions as total_orders')
            ->orderByDesc('total_orders')
            ->limit($limit)
            ->get();
    }

    public function updateAffiliateTier(Affiliate $affiliate): void
    {
        $totalRevenue = $affiliate->total_sales;

        $newTier = AffiliateTier::query()
            ->where('min_revenue', '<=', $totalRevenue)
            ->orderByDesc('min_revenue')
            ->first();

        if ($newTier && $newTier->id !== $affiliate->tier_id) {
            $affiliate->update(['tier_id' => $newTier->id]);
        }
    }

    public function getCommissionReport(string $startDate, string $endDate): array
    {
        $commissions = AffiliateCommission::query()
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with(['affiliate.user', 'affiliate.tier'])
            ->get();

        return [
            'total_commissions' => $commissions->count(),
            'total_amount' => $commissions->sum('commission_amount'),
            'pending_amount' => $commissions->where('status', 'pending')->sum('commission_amount'),
            'approved_amount' => $commissions->where('status', 'approved')->sum('commission_amount'),
            'paid_amount' => $commissions->where('status', 'paid')->sum('commission_amount'),
            'rejected_amount' => $commissions->where('status', 'rejected')->sum('commission_amount'),
            'by_tier' => $commissions->groupBy('affiliate.tier.name')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'amount' => $group->sum('commission_amount'),
                ];
            }),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Services\Search;

use App\Contracts\SearchProvider;
use App\DataTransferObjects\SearchResult;
use Illuminate\Support\Collection;

class SearchService
{
    /**
     * @var Collection<SearchProvider>
     */
    private Collection $providers;

    public function __construct()
    {
        $this->providers = collect();
    }

    public function addProvider(SearchProvider $provider): void
    {
        $this->providers->push($provider);
    }

    public function search(string $query, int $limit = 20): Collection
    {
        if (empty(trim($query))) {
            return collect();
        }

        $results = collect();

        $sortedProviders = $this->providers->sortByDesc(fn(SearchProvider $provider) => $provider->getPriority());

        foreach ($sortedProviders as $provider) {
            $providerResults = $provider->search($query, $limit);
            $results = $results->merge($providerResults);
        }

        return $results
            ->sortByDesc(fn(SearchResult $result) => $result->weight)
            ->take($limit)
            ->values();
    }

    public function getProviders(): Collection
    {
        return $this->providers;
    }
}

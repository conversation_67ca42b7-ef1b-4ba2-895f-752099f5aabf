<?php

declare(strict_types=1);

namespace App\Services\Search\Providers;

use App\Contracts\Searchable;
use App\Contracts\SearchProvider;
use App\DataTransferObjects\SearchResult;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class DatabaseSearchProvider implements SearchProvider
{
    /**
     * @var Collection<class-string<Model&Searchable>>
     */
    private Collection $searchableModels;

    public function __construct()
    {
        $this->searchableModels = collect();
    }

    public function addSearchableModel(string $modelClass): void
    {
        if (!is_subclass_of($modelClass, Model::class)) {
            throw new \InvalidArgumentException("Model class must extend Eloquent Model");
        }

        if (!in_array(Searchable::class, class_implements($modelClass) ?: [])) {
            throw new \InvalidArgumentException("Model class must implement Searchable interface");
        }

        $this->searchableModels->push($modelClass);
    }

    public function search(string $query, int $limit = 10): Collection
    {
        $results = collect();

        foreach ($this->searchableModels as $modelClass) {
            /** @var Model&Searchable $model */
            $model = new $modelClass();

            $modelResults = $model->search($query)
                ->limit($limit)
                ->get()
                ->map(fn($item) => new SearchResult(...$item->toSearchResult()));

            $results = $results->merge($modelResults);
        }

        return $results;
    }

    public function getProviderName(): string
    {
        return 'database';
    }

    public function getPriority(): int
    {
        return 100;
    }

    public function getSearchableModels(): Collection
    {
        return $this->searchableModels;
    }
}

<?php

declare(strict_types=1);

namespace App\Services;

use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;

class ReferralCodeService
{
    public function isSelfReferral(string $referralCode, ?User $user = null): bool
    {
        if (! $user) {
            $user = Auth::user();
        }

        if (! $user) {
            return false;
        }

        if ($user->referral_code === $referralCode) {
            return true;
        }

        $affiliate = Affiliate::query()->where('referral_code', $referralCode)->first();

        if ($user->id === $affiliate->user_id) {
            return true;
        }

        if ($affiliate && $user->referred_by === $affiliate->getKey()) {
            return true;
        }

        return false;
    }

    public function isSpamClick(string $referralCode, Request $request): bool
    {
        $sessionKey = "referral_click_{$referralCode}";
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();

        $clickIdentifier = md5($referralCode . $ipAddress . $userAgent);

        if (Session::has($sessionKey) && Session::get($sessionKey) === $clickIdentifier) {
            return true;
        }

        $affiliate = Affiliate::query()->where('referral_code', $referralCode)->first();

        if ($affiliate) {
            $recentClick = $affiliate->clicks()
                ->where('ip_address', $ipAddress)
                ->where('referral_code', $referralCode)
                ->where('created_at', '>=', now()->subMinutes(5))
                ->first();

            if ($recentClick) {
                return true;
            }
        }

        return false;
    }

    public function saveClickSession(string $referralCode, Request $request): void
    {
        $sessionKey = "referral_click_{$referralCode}";
        $ipAddress = $request->ip();
        $userAgent = $request->userAgent();

        $clickIdentifier = md5($referralCode . $ipAddress . $userAgent);

        Session::put($sessionKey, $clickIdentifier);
        Session::put("{$sessionKey}_timestamp", now()->timestamp);
    }

    public function processReferralCode(string $referralCode, Request $request): bool
    {
        $affiliate = Affiliate::query()
            ->where('referral_code', $referralCode)
            ->where('status', AffiliateStatus::Approved)
            ->first();

        if (! $affiliate) {
            return false;
        }

        if ($this->isSelfReferral($referralCode)) {
            return false;
        }

        if ($this->isSpamClick($referralCode, $request)) {
            return false;
        }

        $this->saveClickSession($referralCode, $request);

        session(['referral_code' => $referralCode]);

        if (Auth::check()) {
            $user = Auth::user();
            if (empty($user->referral_code) && empty($user->referred_by)) {
                $user->update([
                    'referral_code' => $referralCode,
                    'referred_by' => $affiliate->getKey(),
                    'referred_at' => now(),
                ]);

                $affiliate->increment('total_referrals');
            }
        }

        $affiliate->clicks()->create([
            'referral_code' => $referralCode,
            'ip_address' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'referrer' => $this->getReferrer($request),
            'landing_page' => $request->fullUrl(),
        ]);

        $affiliate->increment('total_clicks');

        return true;
    }

    private function getReferrer(Request $request): ?string
    {
        return match (true) {
            $referer = $request->header('referer') => $referer,
            $httpReferer = $request->header('HTTP_REFERER') => $httpReferer,
            $serverReferer = $request->server('HTTP_REFERER') => $serverReferer,
            $utmSource = $request->get('utm_source') => "utm_source:{$utmSource}",
            $source = $request->get('source') => "source:{$source}",
            default => null,
        };
    }
}

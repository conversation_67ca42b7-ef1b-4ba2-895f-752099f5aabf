<?php

declare(strict_types=1);

namespace App\Services;

use Spatie\Color\Hex;

class ColorService
{
    public static function hexToHsl(string $hex): array
    {
        $hexColor = Hex::fromString($hex);
        $hslColor = $hexColor->toHsl();

        return [
            'h' => $hslColor->hue(),
            's' => $hslColor->saturation(),
            'l' => $hslColor->lightness(),
        ];
    }

    public static function hexToHslString(string $hex): string
    {
        $hsl = self::hexToHsl($hex);
        return "{$hsl['h']} {$hsl['s']}% {$hsl['l']}%";
    }

    public static function getContrastColor(string $hex): string
    {
        $hexColor = Hex::fromString($hex);
        $rgbColor = $hexColor->toRgb();

        $r = $rgbColor->red() / 255;
        $g = $rgbColor->green() / 255;
        $b = $rgbColor->blue() / 255;

        $r = $r <= 0.03928 ? $r / 12.92 : pow(($r + 0.055) / 1.055, 2.4);
        $g = $g <= 0.03928 ? $g / 12.92 : pow(($g + 0.055) / 1.055, 2.4);
        $b = $b <= 0.03928 ? $b / 12.92 : pow(($b + 0.055) / 1.055, 2.4);

        $luminance = 0.2126 * $r + 0.7152 * $g + 0.0722 * $b;

        return $luminance > 0.179 ? '#000000' : '#ffffff';
    }

    public static function getContrastColorHsl(string $hex): string
    {
        $contrastHex = self::getContrastColor($hex);
        return self::hexToHslString($contrastHex);
    }

    public static function generatePrimaryColorVariables(string $hex, ?string $textColor = null): array
    {
        $primaryHsl = self::hexToHslString($hex);
        $textHsl = $textColor ? self::hexToHslString($textColor) : self::getContrastColorHsl($hex);

        return [
            'primary_hsl' => $primaryHsl,
            'primary_foreground_hsl' => $textHsl,
        ];
    }
}

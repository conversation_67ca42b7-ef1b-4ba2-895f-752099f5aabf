<?php

declare(strict_types=1);

namespace App\Support;

use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Number;
use App\Services\AdService;

class Helper
{
    public static function getFonts(): array
    {
        return File::json(resource_path('data/google-fonts.json'));
    }

    public static function formatCurrency(float|int $amount, string $currency = 'VND'): string
    {
        return Number::currency($amount, $currency, app()->getLocale());
    }

    public static function formatDateTime(Carbon $carbon, ?string $format = null): string
    {
        return $carbon->translatedFormat($format ?? 'd/m/Y H:i:s');
    }

    public static function renderAds(string $position): string
    {
        return app(AdService::class)->renderAdsByPosition($position);
    }

    public static function hasAds(string $position): bool
    {
        return app(AdService::class)->getAdsByPosition($position)->isNotEmpty();
    }
}

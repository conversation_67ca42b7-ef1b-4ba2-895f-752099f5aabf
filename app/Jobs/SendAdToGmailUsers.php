<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Mail\AdContentMail;
use App\Models\Ad;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Mail;

class SendAdToGmailUsers implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(public Ad $ad) {}

    public function handle(): void
    {
        User::query()
            ->where('email', 'like', '%@gmail.com')
            ->chunk(100, function ($users): void {
                foreach ($users as $user) {
                    Mail::to($user->email)->queue(new AdContentMail($this->ad));
                }
            });
    }
}

<?php

declare(strict_types=1);

namespace App\Tables\Columns;

use Filament\Tables\Columns\TextColumn;

class DateTimeColumn extends TextColumn
{
    public static function make(string $name, $isToggledHiddenByDefault = true): static
    {
        return parent::make($name)
            ->dateTime()
            ->sortable()
            ->toggleable(isToggledHiddenByDefault: $isToggledHiddenByDefault);
    }
}

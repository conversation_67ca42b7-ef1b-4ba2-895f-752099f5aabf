<?php

declare(strict_types=1);

namespace App\Settings;

use App\Enums\MailDriver;
use <PERSON><PERSON>\LaravelSettings\Settings;

class MailSettings extends Settings
{
    public MailDriver $driver;

    public string $from_address;

    public string $from_name;

    public string $smtp_host;

    public int $smtp_port;

    public ?string $smtp_username = null;

    public ?string $smtp_password = null;

    public ?string $smtp_encryption = null;

    public ?string $log_channel = null;

    public ?string $sendmail_path = null;

    public ?string $mailgun_domain = null;

    public ?string $mailgun_secret = null;

    public ?string $mailgun_endpoint = null;

    public ?string $postmark_token = null;

    public ?string $ses_key = null;

    public ?string $ses_secret = null;

    public ?string $ses_region = null;

    public ?string $mailersend_api_key = null;

    public ?string $resend_key = null;

    public static function group(): string
    {
        return 'mail';
    }
}

<?php

declare(strict_types=1);

namespace App\Settings;

use App\Enums\RechargeProvider;
use <PERSON><PERSON>\LaravelSettings\Settings;

class DepositSettings extends Settings
{
    public string $recharge_type = 'manual';

    public ?RechargeProvider $recharge_provider = null;

    public ?string $thesieure_partner_id = null;

    public ?string $thesieure_partner_key = null;

    public ?string $cardvip_partner_id = null;

    public ?string $cardvip_partner_key = null;

    public ?string $thecaosieure_partner_id = null;

    public ?string $thecaosieure_partner_key = null;

    public float $recharge_promotion = 0;

    public ?array $recharge_telecoms = [];

    public bool $auto_pay_enabled = true;

    public ?string $auto_pay_webhook_auth_token = null;

    public ?string $web2m_webhook_auth_token = null;

    public ?int $auto_pay_default_bank_account = null;

    public ?string $auto_pay_code_prefix = '';

    public float $auto_pay_promotion = 0;

    public int $auto_pay_min_amount = 0;

    public int $auto_pay_max_amount = 0;

    public static function group(): string
    {
        return 'deposit';
    }
}

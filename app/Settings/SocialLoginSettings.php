<?php

declare(strict_types=1);

namespace App\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class SocialLoginSettings extends Settings
{
    public bool $facebook_enabled;

    public ?string $facebook_client_id = null;

    public ?string $facebook_client_secret = null;

    public bool $google_enabled;

    public ?string $google_client_id = null;

    public ?string $google_client_secret = null;

    public bool $zalo_enabled;

    public ?string $zalo_client_id = null;

    public ?string $zalo_client_secret = null;

    public static function group(): string
    {
        return 'social_login';
    }
}

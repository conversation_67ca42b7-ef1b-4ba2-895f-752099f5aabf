<?php

declare(strict_types=1);

namespace App\DataTransferObjects;

use App\Enums\RechargeStatus;
use Illuminate\Contracts\Support\Arrayable;

readonly class RechargeResponse implements Arrayable
{
    public function __construct(
        public int $status,
        public ?string $requestId = null,
        public string|int|null $providerTransactionId = null,
        public ?int $amount = null,
        public ?string $message = null,
    ) {}

    public function getStatus(): RechargeStatus
    {
        return match ($this->status) {
            1 => RechargeStatus::Completed,
            2 => RechargeStatus::WrongAmount,
            3 => RechargeStatus::Invalid,
            99 => RechargeStatus::Pending,
            default => RechargeStatus::Failed,
        };
    }

    public function getMessage(): string
    {
        return match ($this->message) {
            'lang.invalid_card_code' => 'Định dạng mã thẻ không hợp lệ',
            'lang.invalid_serial_code' => 'Định dạng số serial không hợp lệ',
            'lang.card_existed' => 'Thẻ đã tồn tại trong hệ thống',
            'PENDING' => 'Thẻ gửi lên không hợp lệ',
            default => $this->message,
        };
    }

    public function guessCausedInputName(): string
    {
        return match ($this->message) {
            'lang.invalid_card_code' => 'code',
            'lang.invalid_serial_code' => 'serial',
            default => 'type',
        };
    }

    public function toArray(): array
    {
        return [
            'status' => $this->status,
            'request_id' => $this->requestId,
            'trans_id' => $this->providerTransactionId,
            'amount' => $this->amount,
            'message' => $this->message,
        ];
    }
}

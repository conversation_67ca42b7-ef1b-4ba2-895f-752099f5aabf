<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Filament\Pages\Auth\Login;
use BezhanSalleh\FilamentShield\FilamentShieldPlugin;
use App\Filament\Pages\Dashboard;
use App\Http\Middleware\EnsureInstalled;
use App\Models\Category;
use App\Models\AccountCategory;
use App\Models\Menu;
use App\Settings\AppearanceSettings;
use App\Settings\GeneralSettings;
use Datlechin\FilamentMenuBuilder\FilamentMenuBuilderPlugin;
use Datlechin\FilamentMenuBuilder\MenuPanel\ModelMenuPanel;
use Datlechin\FilamentMenuBuilder\MenuPanel\StaticMenuPanel;
use Filament\Forms\Components\Select;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Infolists\Infolist;
use Filament\Navigation\NavigationGroup;
use Filament\Notifications\Notification;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Table;
use Filament\View\PanelsRenderHook;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        $panel
            ->default()
            ->id('admin')
            ->path('admin')
            ->login(Login::class)
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->discoverClusters(in: app_path('Filament/Clusters'), for: 'App\\Filament\\Clusters')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->middleware([
                EnsureInstalled::class,
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ])
            ->navigationGroups([
                NavigationGroup::make()
                    ->label('Dashboard')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Quản lý Game')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Quản lý Nội dung')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Quản lý Tài chính')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Tiếp thị liên kết')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Quản lý Người dùng')
                    ->collapsed(),
                NavigationGroup::make()
                    ->label('Cài đặt')
                    ->collapsed(),
            ])
            ->renderHook(
                PanelsRenderHook::GLOBAL_SEARCH_BEFORE,
                fn(): string => Blade::render(<<<HTML
                    <x-filament::button color="gray" tag="a" :href="route('home')" icon="heroicon-o-globe-alt" class="hidden sm:flex">
                        Xem trang web
                    </x-filament::button>
                HTML),
            );

        rescue(function () use ($panel): void {
            $generalSettings = app(GeneralSettings::class);
            $appearanceSettings = app(AppearanceSettings::class);

            $panel
                ->plugins([
                    FilamentShieldPlugin::make(),
                    FilamentMenuBuilderPlugin::make()
                        ->usingMenuModel(Menu::class)
                        ->addLocation('header', 'Header')
                        ->addLocation('footer', 'Footer')
                        ->addMenuPanels([
                            StaticMenuPanel::make()
                                ->add('Trang chủ', fn() => route('home'))
                                ->add('Flash Sale', fn() => route('flash-sales.index'))
                                ->add('Tin tức', fn() => route('blog.index')),
                            ModelMenuPanel::make()
                                ->model(Category::class),
                            ModelMenuPanel::make()
                                ->model(AccountCategory::class),
                        ]),
                ])
                ->colors([
                    'primary' => Color::hex($appearanceSettings->primary_color),
                ])
                ->font($appearanceSettings->primary_font)
                ->favicon($generalSettings->favicon ? Storage::url($generalSettings->favicon) : null)
                ->brandLogo($generalSettings->logo ? Storage::url($generalSettings->logo) : null)
                ->theme(asset('css/filament/admin/theme.css'));
        }, report: true);

        return $panel;
    }

    public function boot(): void
    {
        Select::configureUsing(function (Select $select): void {
            $select->native(false);
        });

        Table::configureUsing(function (Table $table): void {
            $table::$defaultCurrency = 'VND';
        });

        Infolist::configureUsing(function (Infolist $infolist): void {
            $infolist::$defaultCurrency = 'VND';
        });

        DeleteBulkAction::configureUsing(function (DeleteBulkAction $action): void {
            $action->before(function (DeleteBulkAction $action): void {
                if (app()->environment('demo')) {
                    Notification::make()
                        ->title('Bạn không thể thực hiện thao tác này trong chế độ xem demo.')
                        ->warning()
                        ->send();

                    $action->cancel();
                }
            });
        });
    }
}

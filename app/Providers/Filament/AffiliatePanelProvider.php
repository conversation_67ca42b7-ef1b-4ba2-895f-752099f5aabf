<?php

declare(strict_types=1);

namespace App\Providers\Filament;

use App\Filament\Affiliate\Pages\Dashboard;
use App\Http\Middleware\EnsureUserIsApprovedAffiliate;
use App\Settings\GeneralSettings;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\AuthenticateSession;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AffiliatePanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        $panel
            ->id('affiliate')
            ->path('affiliate')
            ->colors([
                'primary' => Color::Amber,
            ])
            ->discoverResources(in: app_path('Filament/Affiliate/Resources'), for: 'App\\Filament\\Affiliate\\Resources')
            ->discoverPages(in: app_path('Filament/Affiliate/Pages'), for: 'App\\Filament\\Affiliate\\Pages')
            ->pages([
                Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Affiliate/Widgets'), for: 'App\\Filament\\Affiliate\\Widgets')
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
                EnsureUserIsApprovedAffiliate::class,
            ]);

        rescue(function () use ($panel): void {
            $generalSettings = $this->app->make(GeneralSettings::class);

            $panel
                ->colors([
                    'primary' => Color::hex($generalSettings->primary_color),
                ])
                ->font($generalSettings->primary_font)
                ->favicon($generalSettings->favicon ? Storage::url($generalSettings->favicon) : null)
                ->brandLogo($generalSettings->logo ? Storage::url($generalSettings->logo) : null)
                ->theme(asset('css/filament/affiliate/theme.css'));
        }, report: false);

        return $panel;
    }
}

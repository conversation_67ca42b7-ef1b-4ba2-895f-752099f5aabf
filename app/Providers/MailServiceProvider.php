<?php

declare(strict_types=1);

namespace App\Providers;

use App\Enums\MailDriver;
use App\Settings\MailSettings;
use Illuminate\Foundation\Application;
use Illuminate\Support\ServiceProvider;

class MailServiceProvider extends ServiceProvider
{
    public function boot(): void
    {
        $this->app->booted(function (Application $app): void {
            rescue(function () use ($app): void {
                $config = $app->make('config');
                $mailSettings = $app->make(MailSettings::class);

                $config->set([
                    'mail' => [
                        ...$config->get('mail'),
                        'default' => $mailSettings->driver->value,
                        'from' => [
                            'address' => $mailSettings->from_address,
                            'name' => $mailSettings->from_name,
                        ],
                    ],
                ]);

                match ($mailSettings->driver) {
                    MailDriver::Log => $config->set([
                        'mail.mailers.log' => [
                            ...$config->get('mail.mailers.log'),
                            'channel' => $mailSettings->log_channel,
                        ],
                    ]),
                    MailDriver::Smtp => $config->set([
                        'mail.mailers.smtp' => [
                            ...$config->get('mail.mailers.smtp'),
                            'host' => $mailSettings->smtp_host,
                            'port' => $mailSettings->smtp_port,
                            'encryption' => $mailSettings->smtp_encryption,
                            'username' => $mailSettings->smtp_username,
                            'password' => $mailSettings->smtp_password,
                        ],
                    ]),
                    MailDriver::Sendmail => $config->set([
                        'mail.mailers.sendmail' => [
                            ...$config->get('mail.mailers.sendmail'),
                            'path' => $mailSettings->sendmail_path,
                        ],
                    ]),
                    MailDriver::Mailgun => $config->set([
                        'mail.mailers.mailgun' => [
                            ...$config->get('mail.mailers.mailgun'),
                            'domain' => $mailSettings->mailgun_domain,
                            'secret' => $mailSettings->mailgun_secret,
                        ],
                    ]),
                    MailDriver::Ses => $config->set([
                        'mail.mailers.ses' => [
                            ...$config->get('mail.mailers.ses'),
                            'key' => $mailSettings->ses_key,
                            'secret' => $mailSettings->ses_secret,
                            'region' => $mailSettings->ses_region,
                        ],
                    ]),
                    MailDriver::Postmark => $config->set([
                        'mail.mailers.postmark' => [
                            ...$config->get('mail.mailers.postmark'),
                            'token' => $mailSettings->postmark_token,
                        ],
                    ]),
                    MailDriver::MailerSend => $config->set([
                        'mail.mailers.mailersend' => [
                            ...$config->get('mail.mailers.mailersend'),
                            'api_key' => $mailSettings->mailersend_api_key,
                        ],
                    ]),
                    MailDriver::Resend => $config->set([
                        'services.resend.key' => $mailSettings->resend_key,
                    ]),
                    default => null,
                };
            }, report: false);
        });
    }
}

<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\Account;
use App\Models\AccountCategory;
use App\Models\Game;
use App\Models\Post;
use App\Services\Search\Providers\CustomSearchProvider;
use App\Services\Search\Providers\DatabaseSearchProvider;
use App\Services\Search\SearchService;
use Illuminate\Support\ServiceProvider;

class SearchServiceProvider extends ServiceProvider
{
    public function register(): void
    {
        $this->app->singleton(SearchService::class, function ($app) {
            $searchService = new SearchService();

            $databaseProvider = new DatabaseSearchProvider();
            $databaseProvider->addSearchableModel(Account::class);
            $databaseProvider->addSearchableModel(Game::class);
            $databaseProvider->addSearchableModel(Post::class);
            $databaseProvider->addSearchableModel(AccountCategory::class);

            $searchService->addProvider($databaseProvider);

            $customProvider = new CustomSearchProvider();
            $searchService->addProvider($customProvider);

            return $searchService;
        });
    }

    public function boot(): void
    {
        //
    }
}

<?php

declare(strict_types=1);

namespace App\Managers;

use App\Contracts\Recharge;
use App\Contracts\RechargeDriver;
use App\RechargeDrivers\Cardvip;
use App\RechargeDrivers\Thecaosieure;
use App\RechargeDrivers\Thesieure;
use App\Settings\DepositSettings;
use Illuminate\Contracts\Container\Container;
use Illuminate\Support\Manager;

class RechargeManager extends Manager implements Recharge
{
    public function __construct(Container $container, protected DepositSettings $settings)
    {
        parent::__construct($container);
    }

    public function createThesieureDriver(): RechargeDriver
    {
        return new Thesieure($this->settings);
    }

    public function createCardvipDriver(): RechargeDriver
    {
        return new Cardvip($this->settings);
    }

    public function createThecaosieureDriver(): RechargeDriver
    {
        return new Thecaosieure($this->settings);
    }

    public function getDefaultDriver(): string
    {
        return $this->settings->recharge_provider?->value ?: 'thesieure';
    }
}

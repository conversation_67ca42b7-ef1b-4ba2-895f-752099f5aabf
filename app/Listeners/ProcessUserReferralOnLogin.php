<?php

declare(strict_types=1);

namespace App\Listeners;

use App\Actions\ProcessUserReferralAction;
use App\Events\UserLoggedIn;
use Illuminate\Contracts\Queue\ShouldQueue;

class ProcessUserReferralOnLogin implements ShouldQueue
{
    public function __construct(protected ProcessUserReferralAction $processUserReferralAction) {}

    public function handle(UserLoggedIn $event): void
    {
        ($this->processUserReferralAction)($event->user);
    }
}

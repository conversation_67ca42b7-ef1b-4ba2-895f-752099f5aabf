<?php

declare(strict_types=1);

namespace App\Listeners;

use App\Models\AuthenticationLog;
use Illuminate\Auth\Events\OtherDeviceLogout;
use Illuminate\Http\Request;

class OtherDeviceLogoutListener
{
    public function __construct(public Request $request) {}

    public function handle(OtherDeviceLogout $event): void
    {
        /** @var \App\Models\User&\App\Contracts\AuthenticationLoggable $user */
        $user = $event->user;
        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        $authenticationLog = $user->authentications()
            ->whereIpAddress($ip)
            ->whereUserAgent($userAgent)
            ->first();

        if (! $authenticationLog) {
            $authenticationLog = new AuthenticationLog([
                'ip_address' => $ip,
                'user_agent' => $userAgent,
            ]);
        }

        foreach ($user->authentications()->whereLoginSuccessful(true)->whereNull('logout_at')->get() as $log) {
            if ($log->id !== $authenticationLog->getKey()) {
                $log->update([
                    'cleared_by_user' => true,
                    'logout_at' => now(),
                ]);
            }
        }
    }
}

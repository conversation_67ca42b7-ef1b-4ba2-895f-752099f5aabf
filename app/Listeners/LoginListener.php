<?php

declare(strict_types=1);

namespace App\Listeners;

use App\Events\UserLoggedIn;
use App\Notifications\NewDevice;
use Illuminate\Auth\Events\Login;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;

class LoginListener
{
    public function __construct(public Request $request) {}

    public function handle(Login $event): void
    {
        /** @var \App\Models\User&\App\Contracts\AuthenticationLoggable $user */
        $user = $event->user;
        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        $known = $user->authentications()
            ->whereIpAddress($ip)
            ->whereUserAgent($userAgent)
            ->whereLoginSuccessful(true)
            ->first();
        $newUser = Carbon::parse($user->created_at)->diffInMinutes(Carbon::now()) < 1;

        $log = $user->authentications()->create([
            'ip_address' => $ip,
            'user_agent' => $userAgent,
            'login_at' => now(),
            'login_successful' => true,
            'location' => optional(geoip()->getLocation($ip))->toArray(),
        ]);

        if (! $known && ! $newUser) {
            $user->notify(new NewDevice($log));
        }

        event(new UserLoggedIn($user));
    }
}

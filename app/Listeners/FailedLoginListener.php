<?php

declare(strict_types=1);

namespace App\Listeners;

use App\Notifications\FailedLogin;
use Illuminate\Auth\Events\Failed;
use Illuminate\Http\Request;

class FailedLoginListener
{
    public function __construct(public Request $request) {}

    public function handle(Failed $event): void
    {
        $user = $event->user;

        if (! $user) {
            return;
        }

        $ip = $this->request->ip();

        /** @var \App\Models\AuthenticationLog $log */
        $log = $user->authentications()->create([
            'ip_address' => $ip,
            'user_agent' => $this->request->userAgent(),
            'login_at' => now(),
            'login_successful' => false,
            'location' => optional(geoip()->getLocation($ip))->toArray(),
        ]);

        $user->notify(new FailedLogin($log));
    }
}

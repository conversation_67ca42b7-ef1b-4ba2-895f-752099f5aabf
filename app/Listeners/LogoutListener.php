<?php

declare(strict_types=1);

namespace App\Listeners;

use App\Models\AuthenticationLog;
use Illuminate\Auth\Events\Logout;
use Illuminate\Http\Request;

class LogoutListener
{
    public function __construct(public Request $request) {}

    public function handle(Logout $event): void
    {
        /** @var \App\Models\User&\App\Contracts\AuthenticationLoggable $user */
        $user = $event->user;
        $ip = $this->request->ip();
        $userAgent = $this->request->userAgent();
        $log = $user->authentications()
            ->whereIpAddress($ip)
            ->whereUserAgent($userAgent)
            ->orderByDesc('login_at')
            ->first();

        if (! $log) {
            $log = new AuthenticationLog([
                'ip_address' => $ip,
                'user_agent' => $userAgent,
            ]);
        }

        $log->logout_at = now();

        $user->authentications()->save($log);
    }
}

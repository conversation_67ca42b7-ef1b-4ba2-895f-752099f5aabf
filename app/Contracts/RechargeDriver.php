<?php

declare(strict_types=1);

namespace App\Contracts;

use App\DataTransferObjects\RechargeResponse;
use Illuminate\Support\Collection;

interface RechargeDriver
{
    public function charge(string $telecom, int $amount, string $code, ?string $serial = null): RechargeResponse;

    public function getFees(): Collection;

    public function generateSignature(string $code, string $serial): string;
}

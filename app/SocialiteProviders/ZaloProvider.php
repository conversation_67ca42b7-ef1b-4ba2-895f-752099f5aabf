<?php

declare(strict_types=1);

namespace App\SocialiteProviders;

use GuzzleHttp\RequestOptions;
use <PERSON><PERSON>\Socialite\Two\AbstractProvider;
use <PERSON><PERSON>\Socialite\Two\User;

class ZaloProvider extends AbstractProvider
{
    protected $scopes = ['*'];

    protected function getAuthUrl($state): string
    {
        return $this->buildAuthUrlFromBase('https://oauth.zaloapp.com/v4/permission', $state);
    }

    protected function getTokenUrl(): string
    {
        return 'https://oauth.zaloapp.com/v4/access_token';
    }

    protected function getUserByToken($token): array
    {
        $response = $this->getHttpClient()->get('https://graph.zalo.me/v2.0/me', [
            RequestOptions::HEADERS => ['access_token' => $token],
            RequestOptions::QUERY => ['fields' => 'id,error,message,name,picture'],
        ]);

        return json_decode((string) $response->getBody(), true);
    }

    protected function mapUserToObject(array $user): User
    {
        return (new User())->setRaw($user)->map([
            'id' => $user['id'],
            'nickname' => null,
            'name' => $user['name'] ?? null,
            'avatar' => preg_replace('/^http:/i', 'https:', $user['picture']['data']['url']),
        ]);
    }

    protected function getTokenFields($code): array
    {
        return [
            ...parent::getTokenFields($code),
            'app_id' => $this->clientId,
        ];
    }

    protected function getCodeFields($state = null): array
    {
        return [
            ...parent::getCodeFields($state),
            'app_id' => $this->clientId,
        ];
    }
}

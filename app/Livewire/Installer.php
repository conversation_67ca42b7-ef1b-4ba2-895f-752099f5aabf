<?php

declare(strict_types=1);

namespace App\Livewire;

use App\Models\User;
use Exception;
use Filament\Facades\Filament;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Concerns\InteractsWithForms;
use Filament\Forms\Contracts\HasForms;
use Filament\Forms\Form;
use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Livewire\Component;

class Installer extends Component implements HasForms
{
    use InteractsWithForms;

    public array $data = [
        'database_host' => 'localhost',
        'database_username' => 'root',
    ];

    public function install(): void
    {
        try {
            $this->validate([
                'data.site_title' => ['required', 'string'],
                'data.database_host' => ['required', 'string'],
                'data.database_name' => ['required', 'string'],
                'data.database_username' => ['required', 'string'],
                'data.database_password' => ['nullable', 'string'],
                'data.admin_username' => ['required', 'string'],
                'data.admin_email' => ['required', 'email'],
                'data.admin_password' => ['required', 'string', 'confirmed'],
            ]);

            config([
                'database.connections.mysql.host' => $this->data['database_host'],
                'database.connections.mysql.database' => $this->data['database_name'],
                'database.connections.mysql.username' => $this->data['database_username'],
                'database.connections.mysql.password' => $this->data['database_password'] ?? '',
            ]);

            $connection = DB::reconnect();

            if (! $connection->getDatabaseName()) {
                $this->addError('data.database_host', 'Không thể kết nối đến cơ sở dữ liệu với thông tin đã cung cấp.');
                return;
            }

            $this->writeEnvFile([
                'APP_NAME' => $this->data['site_title'],
                'APP_ENV' => 'production',
                'APP_DEBUG' => 'false',
                'APP_URL' => url(''),
                'DB_HOST' => $this->data['database_host'],
                'DB_DATABASE' => $this->data['database_name'],
                'DB_USERNAME' => $this->data['database_username'],
                'DB_PASSWORD' => $this->data['database_password'] ?? '',
            ]);

            Artisan::call('optimize:clear');
            Artisan::call('key:generate', ['--force' => true]);
            Artisan::call('db:wipe', ['--force' => true]);
            Artisan::call('migrate', ['--force' => true]);
            Artisan::call('storage:link');

            $this->createAdminUser($this->data);

            file_put_contents(storage_path('installed'), '');

            $this->redirectIntended(Filament::getUrl());
        } catch (Exception $e) {
            Log::error('Installation failed: ' . $e->getMessage());

            $this->addError('general', 'Cài đặt thất bại: ' . $e->getMessage());
        }
    }

    protected function createAdminUser(array $data): void
    {
        $user = User::query()->create([
            'username' => $data['admin_username'],
            'email' => $data['admin_email'],
            'password' => $data['admin_password'],
            'is_super_admin' => true,
        ]);

        Auth::login($user, true);
    }

    protected function ensureEnvFileExists(): void
    {
        $envPath = base_path('.env');
        $envExamplePath = base_path('.env.example');

        if (file_exists($envPath)) {
            return;
        }

        if (! file_exists($envExamplePath)) {
            throw new Exception('File .env.example không tồn tại. Vui lòng tạo file .env thủ công.');
        }

        $copyResult = copy($envExamplePath, $envPath);
        if ($copyResult === false) {
            throw new Exception('Không thể tạo file .env từ .env.example. Vui lòng kiểm tra quyền thư mục.');
        }

        chmod($envPath, 0644);
    }

    protected function writeEnvFile(array $data): void
    {
        $this->ensureEnvFileExists();

        $envPath = base_path('.env');

        if (! is_readable($envPath)) {
            throw new Exception('Không thể đọc file .env');
        }

        if (! is_writable($envPath)) {
            throw new Exception('Không thể ghi file .env. Vui lòng kiểm tra quyền file.');
        }

        $env = file_get_contents($envPath);

        if ($env === false) {
            throw new Exception('Không thể đọc nội dung file .env');
        }

        foreach ($data as $key => $value) {
            $replacement = str_contains($value, ' ') ? "\"{$value}\"" : $value;
            $env = preg_replace("/{$key}=(.*)/", "{$key}={$replacement}", $env);
        }

        $result = file_put_contents($envPath, $env);

        if ($result === false) {
            throw new Exception('Không thể ghi file .env. Vui lòng kiểm tra quyền file.');
        }
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                TextInput::make('site_title')
                    ->label('Tên trang web')
                    ->required(),
                Fieldset::make()
                    ->label('Cơ sở dữ liệu')
                    ->schema([
                        TextInput::make('database_host')
                            ->label('Host')
                            ->required(),
                        TextInput::make('database_name')
                            ->label('Tên cơ sở dữ liệu')
                            ->required(),
                        TextInput::make('database_username')
                            ->label('Tên người dùng')
                            ->required(),
                        TextInput::make('database_password')
                            ->label('Mật khẩu'),
                    ]),
                Fieldset::make()
                    ->label('Tài khoản quản trị')
                    ->schema([
                        TextInput::make('admin_username')
                            ->columnSpanFull()
                            ->label('Tên người dùng')
                            ->required(),
                        TextInput::make('admin_email')
                            ->columnSpanFull()
                            ->label('Email')
                            ->email()
                            ->required(),
                        TextInput::make('admin_password')
                            ->label('Mật khẩu')
                            ->password()
                            ->required(),
                        TextInput::make('admin_password_confirmation')
                            ->label('Xác nhận mật khẩu')
                            ->password()
                            ->required(),
                    ]),
            ])
            ->statePath('data');
    }

    public function render(): View
    {
        return view('filament.livewire.installer')
            ->layout('layouts.installer');
    }
}

<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Schema;

class DatabaseExport extends Command
{
    protected $signature = 'db:export';

    protected $description = 'Export database';

    public function handle(): void
    {
        $connection = Schema::getConnection();
        $database = $connection->getDatabaseName();
        $username = $connection->getConfig('username');
        $password = $connection->getConfig('password');

        $file = 'database.sql';

        $command = match ($connection->getName()) {
            'sqlite' => "sqlite3 $database .dump > $file",
            'mysql' => "mysqldump -u$username -p$password $database > $file",
            default => null,
        };

        $this->components->info('Exporting database...');

        if (! $command) {
            $this->components->error('Database export failed.');

            exit(self::FAILURE);
        }

        shell_exec($command);

        $this->components->info('Database exported successfully.');
    }
}

<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;

class GoogleFontsUpdate extends Command
{
    protected $signature = 'google-fonts:update';

    protected $description = 'Update the list of Google Fonts';

    public function handle(): void
    {
        $apiKey = config('services.google_fonts.key');
        $fontsPath = resource_path('data/google-fonts.json');

        $currentFonts = File::exists($fontsPath) ? json_decode(File::get($fontsPath), true) : [];

        if (empty($apiKey)) {
            $this->components->error(
                'No Google Fonts API key found. Please add <comment>GOOGLE_FONTS_KEY</comment> to your <comment>.env</comment> file.',
            );

            exit(self::FAILURE);
        }

        $this->components->info('Fetching Google Fonts...');

        $response = Http::get('https://www.googleapis.com/webfonts/v1/webfonts', [
            'key' => $apiKey,
        ]);

        if (! $response->ok()) {
            $this->components->error($response->reason() ?: 'Failed to fetch Google Fonts.');

            exit(self::FAILURE);
        }

        $fonts = $response->json('items');

        $fonts = array_map(fn($font): mixed => $font['family'], $fonts);

        File::put($fontsPath, json_encode($fonts, JSON_PRETTY_PRINT));

        $newFonts = array_diff($fonts, $currentFonts);

        if (empty($newFonts)) {
            $this->components->info('No new fonts found.');

            exit(self::SUCCESS);
        }

        $this->components->info(sprintf('Added new %s fonts: %s', count($newFonts), implode(', ', $newFonts)));

        exit(self::SUCCESS);
    }
}

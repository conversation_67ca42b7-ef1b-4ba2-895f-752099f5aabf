<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Enums\AffiliateCommissionStatus;
use App\Enums\AffiliateStatus;
use App\Models\AffiliateCommission;
use App\Services\AffiliateCommissionService;
use Illuminate\Console\Command;

class ProcessAffiliateCommissions extends Command
{
    protected $signature = 'affiliate:process-commissions {--auto-approve : Tự động duyệt hoa hồng}';

    protected $description = 'Xử lý hoa hồng affiliate';

    public function __construct(protected AffiliateCommissionService $commissionService)
    {
        parent::__construct();
    }

    public function handle(): int
    {
        $this->components->info('Bắt đầu xử lý hoa hồng affiliate...');

        $pendingCommissions = AffiliateCommission::query()
            ->where('status', AffiliateCommissionStatus::Pending)
            ->with(['affiliate.user', 'affiliate.tier'])
            ->get();

        $this->components->info("Tìm thấy {$pendingCommissions->count()} hoa hồng đang chờ duyệt");

        $approvedCount = 0;
        $rejectedCount = 0;

        foreach ($pendingCommissions as $commission) {
            $this->components->info("Xử lý hoa hồng #{$commission->id} - {$commission->affiliate->user->display_name}");

            if ($this->option('auto-approve') && $this->shouldAutoApprove($commission)) {
                $commission->update([
                    'status' => AffiliateCommissionStatus::Approved,
                    'approved_at' => now(),
                ]);

                $affiliate = $commission->affiliate;
                $affiliate->increment('total_commission', $commission->commission_amount);
                $affiliate->increment('available_commission', $commission->commission_amount);

                $this->components->info("✓ Tự động duyệt hoa hồng #{$commission->id}");
                $approvedCount++;
            } else {
                if ($this->shouldReject($commission)) {
                    $commission->update([
                        'status' => AffiliateCommissionStatus::Rejected,
                        'description' => $commission->description . ' - Tự động từ chối: Vi phạm quy định',
                    ]);

                    $this->components->warn("✗ Tự động từ chối hoa hồng #{$commission->id}");
                    $rejectedCount++;
                } else {
                    $this->components->info("- Giữ nguyên trạng thái chờ duyệt cho hoa hồng #{$commission->id}");
                }
            }
        }

        $this->components->info("Hoàn thành xử lý hoa hồng:");
        $this->components->info("- Đã duyệt: {$approvedCount}");
        $this->components->info("- Đã từ chối: {$rejectedCount}");

        return exit(self::SUCCESS);
    }

    private function shouldAutoApprove(AffiliateCommission $commission): bool
    {
        $affiliate = $commission->affiliate;

        if ($affiliate->tier->commission_rate >= 5 && $affiliate->total_sales >= 1000000) {
            return true;
        }

        if ($affiliate->total_clicks > 0 && ($affiliate->total_conversions / $affiliate->total_clicks) >= 0.1) {
            return true;
        }

        return false;
    }

    private function shouldReject(AffiliateCommission $commission): bool
    {
        $affiliate = $commission->affiliate;

        if ($affiliate->status === AffiliateStatus::Suspended) {
            return true;
        }

        if ($commission->commission_amount > $commission->order_amount * 0.5) {
            return true;
        }

        $rejectedCount = $affiliate->commissions()->where('status', AffiliateCommissionStatus::Rejected)->count();

        if ($rejectedCount >= 5) {
            return true;
        }

        return false;
    }
}

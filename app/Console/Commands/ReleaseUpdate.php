<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Actions\ReleaseUpdateAction;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Contracts\Console\PromptsForMissingInput;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Number;

use function Laravel\Prompts\text;
use function Laravel\Prompts\confirm;
use function Laravel\Prompts\password;
use function Laravel\Prompts\spin;

class ReleaseUpdate extends Command implements PromptsForMissingInput
{
    protected $signature = 'release:update {version}';

    protected $description = 'Release update package - build and optionally upload to license server';

    public function handle(): int
    {
        $version = $this->argument('version');
        $description = text('Enter update description', placeholder: 'E.g. Fix bugs and add new features', required: true);
        $isRequired = confirm('Is this a required update?', default: false);
        $shouldUpload = confirm('Upload to license server?', default: true);
        $token = config('app.update_token') ?: password('Enter upload token', placeholder: 'your-upload-token', required: true);

        $this->components->info("Building update package for version: {$version}");
        $this->components->info("Description: {$description}");
        $this->components->info("Required: " . ($isRequired ? 'Yes' : 'No'));

        $outputPath = storage_path("app/updates/update-{$version}.zip");
        if (File::exists($outputPath)) {
            if (! confirm(
                label: "Update file for version {$version} already exists. Overwrite?",
                default: false,
                yes: 'Yes, overwrite',
                no: 'No, cancel',
            )) {
                $this->components->info('Build cancelled.');
                return 0;
            }
        }

        try {
            $action = new ReleaseUpdateAction(
                version: $version,
                description: $description,
                isRequired: $isRequired,
                shouldUpload: $shouldUpload,
                token: $token,
            );

            $result = spin(
                callback: fn() => $action->execute(),
                message: 'Building and uploading update package...',
            );

            $this->components->info('✅ Update package built successfully!');
            $this->components->info("File: {$result['file_path']}");
            $this->components->info("Size: " . Number::fileSize($result['file_size']));
            $this->components->info("Checksum: {$result['checksum']}");
            $this->components->info("Metadata: {$result['metadata_path']}");

            if ($result['uploaded'] ?? false) {
                $this->components->info('✅ Update package uploaded successfully!');
            }

            return 0;
        } catch (Exception $e) {
            $this->components->error('❌ Failed to build update package: ' . $e->getMessage());
            return 1;
        }
    }
}

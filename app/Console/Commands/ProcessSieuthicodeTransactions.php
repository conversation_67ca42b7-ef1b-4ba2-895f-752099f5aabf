<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Actions\HandleSieuthicodeTransactionAction;
use App\Models\BankAccount;
use App\Services\SieuthicodeService;
use Exception;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessSieuthicodeTransactions extends Command
{
    protected $signature = 'sieuthicode:process-transactions';

    protected $description = 'Process transactions from Sieuthicode API for all bank accounts';

    public function handle(SieuthicodeService $service, HandleSieuthicodeTransactionAction $action): void
    {
        $this->components->info('Starting Sieuthicode transaction processing...');

        $bankAccounts = BankAccount::query()
            ->where('is_visible', true)
            ->where('sieuthicode_enabled', true)
            ->get();

        $totalProcessed = 0;
        $totalErrors = 0;

        foreach ($bankAccounts as $bankAccount) {
            $this->components->info("Processing transactions for {$bankAccount->name} ({$bankAccount->bank_name->value})...");

            try {
                $transactions = $service->getTransactions($bankAccount);

                $processed = 0;

                foreach ($transactions as $transaction) {
                    try {
                        $action($transaction, $bankAccount);
                        $processed++;
                    } catch (Exception $e) {
                        $totalErrors++;

                        Log::error('Sieuthicode: Error processing transaction', [
                            'transaction' => $transaction,
                            'bank_account' => $bankAccount->toArray(),
                            'error' => $e->getMessage(),
                        ]);
                    }
                }

                $totalProcessed += $processed;

                $this->components->info("Processed {$processed} transactions for {$bankAccount->name}");
            } catch (Exception $e) {
                $totalErrors++;

                Log::error('Sieuthicode: Error fetching transactions', [
                    'bank_account' => $bankAccount->toArray(),
                    'error' => $e->getMessage(),
                ]);

                $this->components->error("Error processing {$bankAccount->name}: {$e->getMessage()}");
            }
        }

        $this->components->info("Completed! Total processed: {$totalProcessed}, Total errors: {$totalErrors}");
    }
}

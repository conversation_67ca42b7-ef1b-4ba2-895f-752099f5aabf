<?php

declare(strict_types=1);

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Filesystem\Filesystem;
use Illuminate\Support\Collection;

class ClearLogs extends Command
{
    protected $signature = 'logs:clear';

    protected $description = 'Clear every log files in storage/logs';

    public function handle(Filesystem $filesystem): void
    {
        $files = Collection::make(
            $filesystem->allFiles(storage_path('logs')),
        )->sortBy('mTime');

        $deleted = $files->each(fn($file) => $filesystem->delete($file->getPathname()))->count();

        if ($deleted === 0) {
            $this->components->info('No log files to delete.');
        } else {
            $this->components->info("Deleted $deleted log files.");
        }

        exit(self::SUCCESS);
    }
}

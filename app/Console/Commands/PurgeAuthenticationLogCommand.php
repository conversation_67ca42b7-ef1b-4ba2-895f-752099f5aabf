<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\AuthenticationLog;
use Illuminate\Console\Command;

class PurgeAuthenticationLogCommand extends Command
{
    public $signature = 'authentication-log:purge';

    public $description = 'Purge all authentication logs older than the configurable amount of days.';

    public function handle(): void
    {
        $this->components->info('Clearing authentication log...');

        $count = AuthenticationLog::query()
            ->where('login_at', '<', now()->subYear()->format('Y-m-d H:i:s'))
            ->delete();

        $this->components->info(" $count authentication logs cleared.");

        exit(self::SUCCESS);
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use App\Concerns\HasCustomFieldResponses;
use App\Contracts\CustomFieldRespondable;
use App\Enums\ItemWithdrawStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property ItemWithdrawStatus $status
 * @property Carbon $created_at
 */
class ItemWithdraw extends Model implements CustomFieldRespondable
{
    use SoftDeletes;
    use HasCustomFieldResponses;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'quantity' => 'int',
            'status' => ItemWithdrawStatus::class,
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function gameItem(): BelongsTo
    {
        return $this->belongsTo(GameItem::class);
    }
}

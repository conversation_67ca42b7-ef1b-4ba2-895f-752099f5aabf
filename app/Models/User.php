<?php

declare(strict_types=1);

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;

use App\Concerns\HasAuthenticationLogs;
use App\Contracts\AuthenticationLoggable;
use App\Contracts\Withdrawable;
use App\Enums\AffiliateStatus;
use App\Enums\TransactionType;
use App\Notifications\Auth\ResetPassword;
use App\Notifications\Auth\VerifyEmail;
use Filament\Models\Contracts\FilamentUser;
use Filament\Models\Contracts\HasName;
use Filament\Panel;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Spatie\Permission\Traits\HasRoles;

/**
 * @property string $display_name
 * @property string $avatar_url
 * @property bool $is_super_admin
 */
class User extends Authenticatable implements FilamentUser, HasName, AuthenticationLoggable, Withdrawable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory;
    use SoftDeletes;
    use Notifiable;
    use HasAuthenticationLogs;
    use HasRoles;

    protected $fillable = [
        'name',
        'username',
        'email',
        'phone',
        'password',
        'balance',
        'deposit_code',
        'avatar',
        'is_super_admin',
        'referral_code',
        'referred_by',
        'referred_at',
    ];

    protected $hidden = [
        'password',
        'remember_token',
    ];

    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'balance' => 'int',
            'is_super_admin' => 'bool',
            'referred_at' => 'datetime',
        ];
    }

    protected static function boot(): void
    {
        parent::boot();

        static::deleting(function (self $user): void {
            $user->userItem()->delete();
            $user->socials()->delete();
        });
    }

    public function getFilamentName(): string
    {
        return $this->display_name ?? 'Không xác định';
    }

    public function canAccessPanel(Panel $panel): bool
    {
        return match ($panel->getId()) {
            'affiliate' => $this->affiliate?->status === AffiliateStatus::Approved,
            default => $this->hasAnyRole('super_admin', 'collaborator'),
        };
    }

    public function sendEmailVerificationNotification(): void
    {
        $this->notify(new VerifyEmail());
    }

    public function sendPasswordResetNotification(#[\SensitiveParameter] $token): void
    {
        $this->notify(new ResetPassword($token));
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(Transaction::class);
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(Account::class);
    }

    public function purchasedAccounts(): HasMany
    {
        return $this->hasMany(PurchasedAccount::class, 'user_id');
    }

    public function recharges(): HasMany
    {
        return $this->hasMany(Recharge::class);
    }

    public function userItem(): HasOne
    {
        return $this->hasOne(UserItem::class);
    }

    public function gameItems(): HasManyThrough
    {
        return $this
            ->hasManyThrough(GameItem::class, UserItem::class, 'user_id', 'id', 'id', 'game_item_id')
            ->with('userItem');
    }

    public function wheelSpins(): HasMany
    {
        return $this->hasMany(WheelSpin::class);
    }

    public function socials(): HasMany
    {
        return $this->hasMany(UserSocial::class);
    }

    public function deposits(): HasMany
    {
        return $this->hasMany(Deposit::class);
    }

    public function referredBy(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class, 'referred_by');
    }

    public function referrals(): HasMany
    {
        return $this->hasMany(Affiliate::class, 'referred_by');
    }

    public function affiliate(): HasOne
    {
        return $this->hasOne(Affiliate::class);
    }

    public function bankAccounts(): MorphMany
    {
        return $this->morphMany(BankAccount::class, 'owner');
    }

    public function withdrawals(): MorphMany
    {
        return $this->morphMany(Withdrawal::class, 'withdrawable');
    }

    public function recordTransaction(
        TransactionType $type,
        int|float $amount,
        ?Model $reference = null,
        ?string $description = null,
    ): Transaction {
        $transaction = $this->transactions()->create([
            'reference_id' => $reference?->getKey() ?? Auth::id(),
            'reference_type' => $reference?->getMorphClass() ?? $this->getMorphClass(),
            'type' => $type,
            'amount' => $amount,
            'balance' => $type->isPositive() ? $this->balance + $amount : $this->balance - $amount,
            'description' => $description,
        ]);

        $this->update([
            'balance' => $transaction->balance,
        ]);

        return $transaction;
    }

    protected function avatarUrl(): Attribute
    {
        return Attribute::get(function () {
            if (! $this->avatar) {
                return sprintf('https://unavatar.io/%s', $this->email ?: $this->username);
            }

            if (filter_var($this->avatar, FILTER_VALIDATE_URL)) {
                return $this->avatar;
            }

            return Storage::url($this->avatar);
        });
    }

    protected function displayName(): Attribute
    {
        return Attribute::get(fn() => $this->name ?: $this->username);
    }

    public function getWithdrawableName(): string
    {
        return $this->display_name;
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use App\Concerns\Searchable as SearchableTrait;
use App\Contracts\Searchable;
use App\Enums\AccountStatus;
use App\Enums\DiscountType;
use Illuminate\Database\Eloquent\Casts\Attribute as CastsAttribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Storage;
use Illuminate\Database\Eloquent\Builder;

/**
 * @property AccountStatus $status
 * @property array $images
 * @property string|null $image
 * @property int $original_price
 * @property int $final_price
 */
class Account extends Model implements Searchable
{
    use SoftDeletes;
    use SearchableTrait;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'acc_pass' => 'encrypted',
            'price' => 'int',
            'compare_at_price' => 'int',
            'images' => 'array',
            'status' => AccountStatus::class,
        ];
    }

    protected static function booted(): void
    {
        static::deleting(function (Account $account): void {
            $account->accountAttributes()->delete();
            $account->flashSales()->detach();
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(AccountCategory::class, 'category_id');
    }

    public function accountAttributes(): HasMany
    {
        return $this->hasMany(AccountAttribute::class);
    }

    public function flashSales(): BelongsToMany
    {
        return $this
            ->belongsToMany(FlashSale::class, 'flash_sale_account')
            ->using(FlashSaleAccount::class)
            ->withPivot('type', 'value');
    }

    protected function image(): CastsAttribute
    {
        return CastsAttribute::get(
            fn() => $this->images ? Storage::url(Arr::first($this->images)) : null,
        );
    }

    protected function originalPrice(): CastsAttribute
    {
        return CastsAttribute::get(fn() => $this->compare_at_price ?: $this->price);
    }

    protected function finalPrice(): CastsAttribute
    {
        return CastsAttribute::get(function () {
            if ($this->relationLoaded('flashSales')) {
                if ($this->flashSales->isEmpty()) {
                    return $this->original_price;
                }

                $flashSale = $this->flashSales->first();
                /** @var FlashSaleAccount */
                $pivot = $flashSale->pivot; // @phpstan-ignore-line

                return match ($pivot->type) {
                    DiscountType::Fixed => $pivot->value,
                    DiscountType::Percentage => round($this->price - ($this->price * $pivot->value / 100)),
                };
            }

            return $this->original_price;
        })->shouldCache();
    }

    public function getSearchableColumns(): array
    {
        return [
            'acc_name',
            'description',
            'content',
        ];
    }

    public function toSearchResult(): array
    {
        return [
            'id' => 'account_' . $this->id,
            'title' => sprintf('Tài khoản %s #%s', $this->category?->name ?? '', $this->id),
            'description' => $this->description ?? 'Tài khoản game ' . ($this->category?->name ?? ''),
            'type' => 'account',
            'url' => route('accounts.show', $this->id),
            'image' => $this->image,
            'category' => $this->category?->game?->name ?? 'Game',
            'weight' => $this->status === AccountStatus::Selling ? 90 : 50,
            'metadata' => [
                'price' => $this->final_price,
                'status' => $this->status->value,
                'game' => $this->category?->game?->name ?? null,
            ],
        ];
    }

    public function getSearchWeight(): int
    {
        return $this->status === AccountStatus::Selling ? 90 : 50;
    }

    public function getSearchConstraints(Builder $query): Builder
    {
        return $query->where('status', AccountStatus::Selling);
    }
}

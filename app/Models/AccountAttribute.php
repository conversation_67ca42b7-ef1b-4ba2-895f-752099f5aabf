<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AccountAttribute extends Model
{
    protected $table = 'account_attributes';

    protected $guarded = [];

    public $timestamps = false;

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function attribute(): BelongsTo
    {
        return $this->belongsTo(Attribute::class);
    }

    public function attributeSet(): BelongsTo
    {
        return $this->belongsTo(AttributeSet::class);
    }

    /**
     * Get the display value for this account attribute
     */
    public function getDisplayValue(): string
    {
        // If it's a text attribute (has value), return the value
        if (!empty($this->value)) {
            return $this->value;
        }

        // If it's a dropdown attribute, return the attribute name
        return $this->attribute?->name ?? '';
    }
}

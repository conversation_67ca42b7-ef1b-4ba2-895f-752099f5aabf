<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property array{city: string, state: string, country: string}|null $location
 * @property Carbon|null $login_at
 * @property Carbon|null $logout_at
 */
class AuthenticationLog extends Model
{
    use SoftDeletes;

    public $timestamps = false;

    protected $table = 'authentication_log';

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'cleared_by_user' => 'bool',
            'location' => 'array',
            'login_successful' => 'bool',
            'login_at' => 'datetime',
            'logout_at' => 'datetime',
        ];
    }

    public function authenticatable(): MorphTo
    {
        return $this->morphTo();
    }
}

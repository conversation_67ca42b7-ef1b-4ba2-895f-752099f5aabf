<?php

declare(strict_types=1);

namespace App\Models;

use App\Contracts\Withdrawable;
use App\Enums\AffiliateCommissionStatus;
use App\Enums\AffiliateStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Str;

class Affiliate extends Model implements Withdrawable
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'total_commission' => 'decimal:2',
            'available_commission' => 'decimal:2',
            'total_referrals' => 'decimal:2',
            'total_sales' => 'decimal:2',
            'approved_at' => 'datetime',
            'suspended_at' => 'datetime',
            'status' => AffiliateStatus::class,
        ];
    }

    protected static function booted(): void
    {
        static::creating(function (Affiliate $affiliate): void {
            if (empty($affiliate->referral_code)) {
                $affiliate->referral_code = self::generateReferralCode();
            }
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function tier(): BelongsTo
    {
        return $this->belongsTo(AffiliateTier::class, 'tier_id');
    }

    public function clicks(): HasMany
    {
        return $this->hasMany(AffiliateClick::class);
    }

    public function commissions(): HasMany
    {
        return $this->hasMany(AffiliateCommission::class);
    }

    public function withdrawals(): MorphMany
    {
        return $this->morphMany(Withdrawal::class, 'withdrawable');
    }

    public function bankAccounts(): MorphMany
    {
        return $this->morphMany(BankAccount::class, 'owner');
    }

    public function getWithdrawableName(): string
    {
        return $this->user->display_name;
    }

    protected function referralUrl(): Attribute
    {
        return Attribute::get(fn() => route('home') . '?ref=' . $this->referral_code);
    }

    protected function conversionRate(): Attribute
    {
        return Attribute::get(function (): float {
            if ($this->total_clicks === 0 || $this->total_clicks === null || !is_numeric($this->total_clicks) || $this->total_clicks <= 0) {
                return 0;
            }

            $totalConversions = is_numeric($this->total_conversions) ? (float) $this->total_conversions : 0;
            $totalClicks = (float) $this->total_clicks;

            return round(($totalConversions / $totalClicks) * 100, 2);
        });
    }

    public static function generateReferralCode(): string
    {
        do {
            $code = strtoupper(Str::random(8));
        } while (self::where('referral_code', $code)->exists());

        return $code;
    }

    public function getTotalApprovedCommission(): float
    {
        return (float) $this->commissions()
            ->where('status', AffiliateCommissionStatus::Approved)
            ->sum('commission_amount');
    }

    public function getNextTier(): ?AffiliateTier
    {
        $totalCommission = $this->getTotalApprovedCommission();

        return AffiliateTier::getNextTier($totalCommission);
    }

    public function getUpgradeProgress(): array
    {
        $currentCommission = $this->getTotalApprovedCommission();
        $nextTier = $this->getNextTier();

        if (! $nextTier) {
            return [
                'can_upgrade' => false,
                'current_tier' => $this->tier,
                'next_tier' => null,
                'progress_percentage' => 100,
                'remaining_amount' => 0,
            ];
        }

        $currentTier = $this->tier;
        $currentTierMin = $currentTier ? $currentTier->min_commission_required : 0;
        $nextTierMin = $nextTier->min_commission_required;

        $progress = $nextTierMin > $currentTierMin
            ? (($nextTierMin - $currentTierMin) > 0
                ? (($currentCommission - $currentTierMin) / ($nextTierMin - $currentTierMin)) * 100
                : 0)
            : 0;

        return [
            'can_upgrade' => true,
            'current_tier' => $currentTier,
            'next_tier' => $nextTier,
            'progress_percentage' => min(100, max(0, $progress)),
            'remaining_amount' => max(0, $nextTierMin - $currentCommission),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\DiscountScope;
use App\Enums\DiscountType;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property DiscountType $type
 * @property DiscountScope $applied_to
 * @property Carbon $start_at
 * @property Carbon|null $end_at
 * @property bool $is_expired
 * @property bool $is_upcoming
 */
class Discount extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'type' => DiscountType::class,
            'value' => 'float',
            'applied_to' => DiscountScope::class,
            'is_unlimited' => 'bool',
            'limit' => 'int',
            'usage' => 'int',
            'start_at' => 'datetime',
            'end_at' => 'datetime',
            'is_active' => 'bool',
        ];
    }

    protected static function booted(): void
    {
        static::deleted(function (Discount $discount): void {
            $discount->categories()->detach();
            $discount->accounts()->detach();
            $discount->users()->detach();
        });
    }

    public function categories(): BelongsToMany
    {
        return $this->belongsToMany(AccountCategory::class, 'discount_category', relatedPivotKey: 'category_id');
    }

    public function accounts(): BelongsToMany
    {
        return $this->belongsToMany(Account::class, 'discount_account');
    }

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'discount_user');
    }

    public function scopeActive(Builder $query): void
    {
        $query
            ->whereNotNull('start_at')
            ->whereDate('start_at', '<=', now())
            ->where(
                fn(Builder $query) => $query
                ->whereNull('end_at')
                ->orWhereDate('end_at', '>=', now()),
            );
    }

    protected function status(): Attribute
    {
        return Attribute::get(fn(): string => match (true) {
            $this->start_at->isFuture() => 'Sắp diễn ra',
            $this->end_at?->isPast() => 'Đã hết hạn',
            default => 'Đang hoạt động',
        });
    }

    protected function isExpired(): Attribute
    {
        return Attribute::get(fn() => $this->end_at?->isPast());
    }

    protected function isUpcoming(): Attribute
    {
        return Attribute::get(fn() => $this->start_at->isFuture());
    }
}

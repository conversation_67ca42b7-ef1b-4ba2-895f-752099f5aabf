<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\Bank;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Casts\Attribute;

/**
 * @property Bank $bank_name
 */
class BankAccount extends Model
{
    use HasFactory;
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'bank_name' => Bank::class,
            'is_visible' => 'bool',
            'sieuthicode_enabled' => 'bool',
        ];
    }

    public function owner(): MorphTo
    {
        return $this->morphTo();
    }

    public function deposits(): MorphMany
    {
        return $this->morphMany(Deposit::class, 'source');
    }

    protected function displayName(): Attribute
    {
        return Attribute::get(function (): string {
            $accountNumber = $this->account_number;
            $accountHolder = $this->account_holder;

            return "{$this->bank_name->name} - {$accountNumber} ({$accountHolder})";
        });
    }
}

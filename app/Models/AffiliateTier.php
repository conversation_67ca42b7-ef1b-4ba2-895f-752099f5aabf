<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

class AffiliateTier extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'commission_rate' => 'decimal:2',
            'min_commission_required' => 'decimal:2',
            'is_default' => 'bool',
        ];
    }

    public function affiliates(): HasMany
    {
        return $this->hasMany(Affiliate::class, 'tier_id');
    }

    public function canUpgradeTo(float $totalCommission): bool
    {
        return $totalCommission >= $this->min_commission_required;
    }

    public static function getNextTier(float $totalCommission): ?self
    {
        return self::query()
            ->where('min_commission_required', '>', $totalCommission)
            ->orderBy('min_commission_required')
            ->first();
    }

    public static function getTierByCommission(float $totalCommission): ?self
    {
        return self::query()
            ->where('min_commission_required', '<=', $totalCommission)
            ->orderByDesc('min_commission_required')
            ->first();
    }

    public static function getDefaultTier(): ?self
    {
        return self::query()->where('is_default', true)->first();
    }
}

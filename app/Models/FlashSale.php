<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Carbon;

/**
 * @property Carbon $start_at
 * @property Carbon $end_at
 */
class FlashSale extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'start_at' => 'datetime',
            'end_at' => 'datetime',
            'is_active' => 'bool',
        ];
    }

    protected static function booted(): void
    {
        static::deleting(function (FlashSale $flashSale): void {
            $flashSale->accounts()->delete();
        });
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(FlashSaleAccount::class);
    }

    public function scopeAvailable(Builder $query): void
    {
        $query
            ->where('is_active', true)
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now());
    }

    public function scopeEndingSoon(Builder $query, int $hours = 24): void
    {
        $query
            ->where('is_active', true)
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now())
            ->where('end_at', '<=', now()->addHours($hours));
    }

    public function scopeUpcoming(Builder $query): void
    {
        $query
            ->where('is_active', true)
            ->where('start_at', '>', now());
    }

    protected function isAvailable(): Attribute
    {
        return Attribute::get(fn(): bool => $this->is_active && $this->start_at <= now() && $this->end_at >= now());
    }

    protected function isEndingSoon(): Attribute
    {
        return Attribute::get(fn(): bool => $this->is_available && $this->end_at <= now()->addHours(24));
    }

    protected function isUpcoming(): Attribute
    {
        return Attribute::get(fn(): bool => $this->is_active && $this->start_at > now());
    }

    protected function timeRemaining(): Attribute
    {
        return Attribute::get(function (): ?string {
            if (!$this->is_available) {
                return null;
            }

            $diff = now()->diff($this->end_at);

            if ($diff->days > 0) {
                return $diff->days . ' ngày ' . $diff->h . ' giờ';
            } elseif ($diff->h > 0) {
                return $diff->h . ' giờ ' . $diff->i . ' phút';
            } else {
                return $diff->i . ' phút';
            }
        });
    }
}

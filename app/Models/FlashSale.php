<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Carbon;

/**
 * @property Carbon $start_at
 * @property Carbon $end_at
 */
class FlashSale extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'start_at' => 'datetime',
            'end_at' => 'datetime',
            'is_active' => 'bool',
        ];
    }

    protected static function booted(): void
    {
        static::deleting(function (FlashSale $flashSale): void {
            $flashSale->accounts()->delete();
        });
    }

    public function accounts(): HasMany
    {
        return $this->hasMany(FlashSaleAccount::class);
    }

    public function scopeAvailable(Builder $query): void
    {
        $query
            ->where('is_active', true)
            ->where('start_at', '<=', now())
            ->where('end_at', '>=', now());
    }

    protected function isAvailable(): Attribute
    {
        return Attribute::get(fn(): bool => $this->is_active && $this->start_at <= now() && $this->end_at >= now());
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class AttributeSet extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'is_required' => 'bool',
            'is_visible' => 'bool',
        ];
    }

    protected static function booted(): void
    {
        static::deleting(function (AttributeSet $attributeSet): void {
            $attributeSet->attributes()->each(fn(Attribute $attribute) => $attribute->delete());
        });
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(AccountCategory::class, 'category_id');
    }

    public function attributes(): HasMany
    {
        return $this->hasMany(Attribute::class);
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use App\Concerns\HasCustomFields;
use App\Contracts\CustomFieldable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property array $withdraw_packages
 */
class GameItem extends Model implements CustomFieldable
{
    use SoftDeletes;
    use HasCustomFields;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'withdraw_packages' => 'array',
        ];
    }

    protected static function boot(): void
    {
        parent::boot();

        static::deleting(function (GameItem $gameItem): void {
            $gameItem->userItem()->delete();
        });
    }

    public function userItem(): HasOne
    {
        return $this->hasOne(UserItem::class);
    }

    public function wheelSegments(): HasMany
    {
        return $this->hasMany(WheelSegment::class);
    }
}

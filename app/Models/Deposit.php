<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\DepositStatus;
use App\Support\Helper;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property string $source_name
 */
class Deposit extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'amount' => 'int',
            'status' => DepositStatus::class,
            'transacted_at' => 'datetime',
            'raw' => 'array',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    protected function sourceName(): Attribute
    {
        return Attribute::get(fn(): string => match ($this->source_type) {
            BankAccount::class => sprintf('Ngân hàng %s', $this->source->name),
            Recharge::class => sprintf('Thẻ %s %s', $this->source->type, Helper::formatCurrency($this->source->amount)),
            default => 'Không xác định',
        });
    }
}

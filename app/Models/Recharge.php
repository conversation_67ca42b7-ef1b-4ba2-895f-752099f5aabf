<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\RechargeProvider;
use App\Enums\RechargeStatus;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property RechargeProvider|null $provider
 * @property RechargeStatus $status
 * @property Carbon $created_at
 */
class Recharge extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'amount' => 'int',
            'declared_amount' => 'int',
            'status' => RechargeStatus::class,
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function deposit(): MorphOne
    {
        return $this->morphOne(Deposit::class, 'source');
    }
}

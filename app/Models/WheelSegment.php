<?php

declare(strict_types=1);

namespace App\Models;

use App\Enums\RewardType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property RewardType $reward_type
 */
class WheelSegment extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'order' => 'int',
            'reward_type' => RewardType::class,
            'probability' => 'float',
            'value' => 'int',
        ];
    }

    public function wheel(): BelongsTo
    {
        return $this->belongsTo(Wheel::class);
    }

    public function gameItem(): BelongsTo
    {
        return $this->belongsTo(GameItem::class);
    }
}

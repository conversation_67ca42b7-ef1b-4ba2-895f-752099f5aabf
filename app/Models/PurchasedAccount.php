<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Carbon;

/**
 * @property Carbon|null $seen_at
 * @property Carbon $created_at
 */
class PurchasedAccount extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'price' => 'int',
            'discount_amount' => 'int',
            'total' => 'int',
            'seen_at' => 'datetime',
        ];
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use Datlechin\FilamentMenuBuilder\Concerns\HasMenuPanel;
use Datlechin\FilamentMenuBuilder\Contracts\MenuPanelable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends Model implements MenuPanelable
{
    use SoftDeletes;
    use HasMenuPanel;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'is_visible' => 'bool',
        ];
    }

    public function posts(): HasMany
    {
        return $this->hasMany(Post::class);
    }

    public function getMenuPanelName(): string
    {
        return 'Danh mục blog';
    }

    public function getMenuPanelTitleColumn(): string
    {
        return 'name';
    }

    public function getMenuPanelUrlUsing(): callable
    {
        return fn(self $model) => route('blog.show', $model->slug);
    }
}

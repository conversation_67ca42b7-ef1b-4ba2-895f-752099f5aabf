<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Wheel extends Model
{
    use SoftDeletes;

    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'price' => 'int',
            'is_visible' => 'bool',
        ];
    }

    protected static function boot(): void
    {
        parent::boot();

        static::deleting(function (Wheel $wheel): void {
            $wheel->wheelSegments()->delete();
        });
    }

    public function wheelSegments(): HasMany
    {
        return $this->hasMany(WheelSegment::class);
    }

    public function wheelSpins(): HasMany
    {
        return $this->hasMany(WheelSpin::class);
    }
}

<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

class Ad extends Model
{
    protected $guarded = [];

    protected function casts(): array
    {
        return [
            'is_active' => 'bool',
            'start_at' => 'datetime',
            'end_at' => 'datetime',
        ];
    }

    public function scopeActive(Builder $query): void
    {
        $query
            ->where('is_active', true)
            ->where(fn(Builder $q) => $q->whereNull('start_at')->orWhere('start_at', '<=', now()))
            ->where(fn(Builder $q) => $q->whereNull('end_at')->orWhere('end_at', '>=', now()));
    }

    public function scopeByPosition(Builder $query, string $position): void
    {
        $query->where('position', $position);
    }

    public function scopeOrdered(Builder $query): void
    {
        $query->orderBy('sort_order')->orderBy('created_at');
    }

    public static function getPositions(): array
    {
        return [
            'header' => 'Header (<PERSON><PERSON>u trang)',
            'sidebar' => 'Sidebar (<PERSON>h bên)',
            'footer' => 'Footer (Chân trang)',
            'home_banner' => 'Banner trang chủ',
            'account_list' => 'Danh sách tài khoản',
            'account_detail' => 'Chi tiết tài khoản',
            'blog_sidebar' => 'Sidebar blog',
            'blog_detail' => 'Chi tiết blog',
            'user_profile' => 'Trang profile user',
            'user_transactions' => 'Lịch sử giao dịch',
            'games_list' => 'Danh sách games',
            'wheels_list' => 'Danh sách vòng quay',
            'mobile_bottom' => 'Bottom mobile',
        ];
    }

    public static function getTypes(): array
    {
        return [
            'html' => 'HTML tùy chỉnh',
            'image' => 'Hình ảnh',
            'banner' => 'Banner',
            'script' => 'Script (Google Ads, Facebook Pixel, etc.)',
        ];
    }
}

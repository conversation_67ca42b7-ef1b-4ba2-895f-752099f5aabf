<?php

declare(strict_types=1);

namespace App\Models;

use Illuminate\Support\Str;
use Spatie\LaravelSettings\Models\SettingsProperty as BaseSettingsProperty;

class SettingsProperty extends BaseSettingsProperty
{
    public function getConnectionName(): ?string
    {
        $host = request()->getHost();

        if (! $host) {
            return parent::getConnectionName();
        }

        $slug = Str::slug($host, '_');
        $connection = 'settings_' . $slug;

        if (! config()->has("database.connections.$connection")) {
            $envPrefix = 'SETTINGS_DB_' . Str::upper($slug) . '_';

            if (env($envPrefix . 'DATABASE')) {
                config([
                    "database.connections.$connection" => [
                        'driver' => env($envPrefix . 'DRIVER', env('DB_CONNECTION', 'mysql')),
                        'host' => env($envPrefix . 'HOST', env('DB_HOST', '127.0.0.1')),
                        'port' => env($envPrefix . 'PORT', env('DB_PORT', '3306')),
                        'database' => env($envPrefix . 'DATABASE'),
                        'username' => env($envPrefix . 'USERNAME', env('DB_USERNAME', 'forge')),
                        'password' => env($envPrefix . 'PASSWORD', env('DB_PASSWORD', '')),
                        'unix_socket' => env($envPrefix . 'SOCKET', env('DB_SOCKET', '')),
                        'charset' => env($envPrefix . 'CHARSET', 'utf8mb4'),
                        'collation' => env($envPrefix . 'COLLATION', 'utf8mb4_unicode_ci'),
                        'prefix' => '',
                        'prefix_indexes' => true,
                        'strict' => env($envPrefix . 'STRICT', false),
                        'engine' => null,
                    ],
                ]);
            }
        }

        if (config()->has("database.connections.$connection")) {
            return $connection;
        }

        return parent::getConnectionName();
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\AppearanceSettings;
use App\Support\Helper;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Components\ColorPicker;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage as PagesSettingsPage;

class ManageAppearance extends PagesSettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-paint-brush';

    protected static string $settings = AppearanceSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 6;

    protected static ?string $title = 'Cài đặt giao diện';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Container Settings')
                    ->description('Cài đặt kích thước của container chính')
                    ->schema([
                        Select::make('container_max_width')
                            ->label('Chiều rộng tối đa của container')
                            ->options([
                                'max-w-none' => 'Không giới hạn',
                                'max-w-5xl' => '5XL (1024px)',
                                'max-w-6xl' => '6XL (1152px)',
                                'max-w-7xl' => '7XL (1280px)',
                                'max-w-full' => 'Full (100%)',
                                'max-w-screen-sm' => 'Screen Small (640px)',
                                'max-w-screen-md' => 'Screen Medium (768px)',
                                'max-w-screen-lg' => 'Screen Large (1024px)',
                                'max-w-screen-xl' => 'Screen XL (1280px)',
                                'max-w-screen-2xl' => 'Screen 2XL (1536px)',
                            ])
                            ->searchable()
                            ->required()
                            ->helperText('Chọn chiều rộng tối đa cho container chính của trang web'),
                    ])
                    ->columns(1),

                Section::make('Color & Typography')
                    ->description('Cài đặt màu sắc và font chữ chủ đạo')
                    ->schema([
                        ColorPicker::make('primary_color')
                            ->label('Màu chủ đạo')
                            ->required(),
                        ColorPicker::make('primary_text_color')
                            ->label('Màu chữ chủ đạo'),
                        Select::make('primary_font')
                            ->label('Font chữ chủ đạo')
                            ->options(array_combine($fonts = Helper::getFonts(), $fonts))
                            ->searchable()
                            ->required(),
                    ])
                    ->columns(3),

                Section::make('Banner & Sliders')
                    ->description('Cài đặt ảnh quảng cáo và slider')
                    ->schema([
                        Repeater::make('banner_sliders')
                            ->label('Ảnh quảng cáo')
                            ->columnSpanFull()
                            ->itemLabel(fn(array $state): ?string => $state['label'])
                            ->schema([
                                TextInput::make('label')
                                    ->label('Tiêu đề'),
                                FileUpload::make('image')
                                    ->label('Hình ảnh')
                                    ->image()
                                    ->required(),
                                TextInput::make('url')
                                    ->label(__('URL'))
                                    ->url(),
                                Toggle::make('open_in_new_tab')
                                    ->label('Mở trong tab mới'),
                            ]),
                    ])
                    ->columns(1),
            ]);
    }
}

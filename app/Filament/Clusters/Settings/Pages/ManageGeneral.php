<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\GeneralSettings;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TagsInput;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage as PagesSettingsPage;

class ManageGeneral extends PagesSettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-cog-6-tooth';

    protected static string $settings = GeneralSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 1;

    protected static ?string $title = 'Cài đặt chung';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        TextInput::make('site_name')
                            ->label('Tên trang web')
                            ->required()
                            ->columnSpanFull(),
                        TextInput::make('site_title')
                            ->label('Tiêu đề trang web')
                            ->required()
                            ->columnSpanFull(),
                        TextInput::make('app_url')
                            ->label('URL ứng dụng')
                            ->url()
                            ->placeholder('https://example.com')
                            ->helperText('URL chính của ứng dụng. Để trống để sử dụng giá trị từ biến môi trường APP_URL.')
                            ->columnSpanFull(),
                        TagsInput::make('admin_email')
                            ->label('Email quản trị viên')
                            ->nestedRecursiveRules('email')
                            ->columnSpanFull(),
                        FileUpload::make('favicon')
                            ->image(),
                        FileUpload::make('logo')
                            ->image(),
                    ])
                    ->columns(),
            ]);
    }
}

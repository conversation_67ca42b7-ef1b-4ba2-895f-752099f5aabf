<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\HomeSettings;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;

class ManageHome extends SettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $settings = HomeSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 2;

    protected static ?string $title = 'Cài đặt trang chủ';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make('Cài đặt trang chủ')
                    ->description('Quản lý thứ tự hiển thị các section trên trang chủ')
                    ->persistCollapsed()
                    ->schema([
                        Repeater::make('home_sections_order')
                            ->label('Thứ tự hiển thị các section')
                            ->columnSpanFull()
                            ->itemLabel(fn(array $state): ?string => $state['label'] ?? 'Section')
                            ->reorderable()
                            ->defaultItems(6)
                            ->schema([
                                Select::make('section')
                                    ->label('Loại section')
                                    ->options([
                                        'banner_sliders' => 'Banner quảng cáo',
                                        'flash_sale' => 'Flash sale',
                                        'wheels' => 'Vòng quay may mắn',
                                        'account_categories' => 'Danh mục tài khoản',
                                        'ad_home_banner' => 'Quảng cáo banner trang chủ',
                                    ])
                                    ->required()
                                    ->reactive(),
                                Toggle::make('is_visible')
                                    ->label('Hiển thị')
                                    ->default(true),
                            ])
                            ->columns(),
                    ]),
            ]);
    }
}

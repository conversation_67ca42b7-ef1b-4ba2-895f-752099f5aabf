<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Filament\Clusters\Settings;
use App\Settings\SocialLoginSettings;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage as PagesSettingsPage;
use Illuminate\Support\HtmlString;

class ManageSocialLogin extends PagesSettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-globe-alt';

    protected static string $settings = SocialLoginSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 4;

    protected static ?string $title = 'Đăng nhập mạng xã hội';

    public function form(Form $form): Form
    {
        $providers = ['facebook', 'google', 'zalo'];
        $schema = [];

        foreach ($providers as $provider) {
            $schema[] = Forms\Components\Section::make()
                ->heading(ucfirst($provider))
                ->schema([
                    Forms\Components\Checkbox::make("{$provider}_enabled")
                        ->label('Kích hoạt')
                        ->reactive()
                        ->helperText(sprintf('Cho phép người dùng đăng nhập bằng %s.', ucfirst($provider))),
                    Forms\Components\Group::make()
                        ->hidden(fn(Forms\Get $get): bool => ! $get("{$provider}_enabled"))
                        ->schema([
                            Forms\Components\TextInput::make("{$provider}_client_id")
                                ->label('Client ID')
                                ->password(app()->isProduction())
                                ->required(fn(Forms\Get $get): mixed => $get("{$provider}_enabled")),
                            Forms\Components\TextInput::make("{$provider}_client_secret")
                                ->label('Client Secret')
                                ->password(app()->isProduction())
                                ->required(fn(Forms\Get $get): mixed => $get("{$provider}_enabled")),

                            Placeholder::make("{$provider}_callback_url")
                                ->label('Callback URL')
                                ->content(new HtmlString(sprintf(
                                    '<code>%s</code>',
                                    route('socialite.callback', $provider),
                                ))),
                        ]),
                ]);
        }

        return $form->schema($schema);
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Clusters\Settings\Pages;

use App\Enums\MailDriver;
use App\Filament\Clusters\Settings;
use App\Mail\TestMail;
use App\Settings\MailSettings;
use <PERSON>zhan<PERSON>alleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Action;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Pages\SettingsPage as PagesSettingsPage;
use Illuminate\Support\Facades\Mail;

class ManageMail extends PagesSettingsPage
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-envelope';

    protected static string $settings = MailSettings::class;

    protected static ?string $cluster = Settings::class;

    protected static ?int $navigationSort = 2;

    protected static ?string $title = 'Cài đặt Email';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Select::make('driver')
                            ->options(MailDriver::class)
                            ->live()
                            ->searchable()
                            ->required(),
                        TextInput::make('smtp_encryption')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Smtp)
                            ->label('Mã hóa')
                            ->nullable(),
                        TextInput::make('smtp_host')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Smtp)
                            ->label('Máy chủ')
                            ->required(),
                        TextInput::make('smtp_port')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Smtp)
                            ->label('Cổng')
                            ->numeric()
                            ->required(),
                        TextInput::make('smtp_username')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Smtp)
                            ->label('Tên người dùng')
                            ->nullable(),
                        TextInput::make('smtp_password')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Smtp)
                            ->label('Mật khẩu')
                            ->nullable(),
                        TextInput::make('sendmail_path')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Sendmail)
                            ->nullable(),
                        TextInput::make('mailgun_domain')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Mailgun)
                            ->nullable(),
                        TextInput::make('mailgun_secret')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Mailgun)
                            ->nullable(),
                        TextInput::make('postmark_token')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Postmark)
                            ->nullable(),
                        TextInput::make('ses_key')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Ses)
                            ->nullable(),
                        TextInput::make('ses_secret')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Ses)
                            ->nullable(),
                        TextInput::make('ses_region')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Ses)
                            ->nullable(),
                        TextInput::make('mailersend_api_key')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::MailerSend)
                            ->nullable(),
                        TextInput::make('resend_key')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Resend)
                            ->nullable(),
                        Select::make('log_channel')
                            ->visible(fn(Get $get): bool => $get('driver') === MailDriver::Log)
                            ->label('Kênh log')
                            ->options([
                                'daily' => 'Daily',
                                'errorlog' => 'Error Log',
                                'monolog' => 'Monolog',
                                'papertrail' => 'Papertrail',
                                'single' => 'Single',
                                'slack' => 'Slack',
                                'stack' => 'Stack',
                                'syslog' => 'Syslog',
                            ])
                            ->nullable(),
                        TextInput::make('from_address')
                            ->label('Địa chỉ email người gửi')
                            ->required(),
                        TextInput::make('from_name')
                            ->label('Tên người gửi')
                            ->required(),
                    ])
                    ->columns(),
            ]);
    }

    public function getFormActions(): array
    {
        $data = $this->form->getState();

        return [
            ...parent::getFormActions(),
            Action::make('sendTest')
                ->color('gray')
                ->label('Gửi Email Thử Nghiệm')
                ->form([
                    Group::make()
                        ->schema([
                            TextInput::make('from_address')
                                ->label('Địa chỉ email')
                                ->default($data['from_address'] ?? null)
                                ->required(),
                            TextInput::make('from_name')
                                ->label('Tên người nhận')
                                ->default($data['from_name'] ?? null)
                                ->required(),
                        ])
                        ->columns(),
                ])
                ->action(function (array $data): \Filament\Notifications\Notification {
                    Mail::to($data['from_address'], $data['from_name'])
                        ->send(new TestMail($data['from_name']));

                    return Notification::make()
                        ->title('Email thử nghiệm đã được gửi')
                        ->success()
                        ->send();
                }),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources;

use App\Enums\Bank;
use App\Filament\Affiliate\Resources\BankAccountResource\Pages;
use App\Models\Affiliate;
use App\Models\BankAccount;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class BankAccountResource extends Resource
{
    protected static ?string $model = BankAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-credit-card';

    protected static ?string $navigationLabel = 'Tài khoản ngân hàng';

    protected static ?string $modelLabel = 'Tài khoản ngân hàng';

    protected static ?string $pluralModelLabel = 'Tài khoản ngân hàng';

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->columns()
                    ->schema([
                        Forms\Components\Select::make('bank_name')
                            ->label('Ngân hàng')
                            ->options(Bank::class)
                            ->required()
                            ->searchable(),
                        Forms\Components\TextInput::make('name')
                            ->label('Tên hiển thị')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('VD: Tài khoản chính'),
                        Forms\Components\TextInput::make('account_number')
                            ->label('Số tài khoản')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('VD: **********'),
                        Forms\Components\TextInput::make('account_holder')
                            ->label('Tên chủ tài khoản')
                            ->required()
                            ->maxLength(255)
                            ->placeholder('VD: NGUYEN VAN A'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên hiển thị')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('bank_name')
                    ->label('Ngân hàng')
                    ->badge()
                    ->color('primary'),
                Tables\Columns\TextColumn::make('account_number')
                    ->label('Số tài khoản')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Đã sao chép số tài khoản'),
                Tables\Columns\TextColumn::make('account_holder')
                    ->label('Chủ tài khoản')
                    ->searchable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('bank_name')
                    ->label('Ngân hàng')
                    ->options(Bank::class),
            ])
            ->actions([
                Tables\Actions\EditAction::make()
                    ->label('Sửa'),
                Tables\Actions\DeleteAction::make()
                    ->label('Xóa'),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('owner_type', Affiliate::class)
            ->where('owner_id', Auth::user()->affiliate->id);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccounts::route('/'),
            'create' => Pages\CreateBankAccount::route('/create'),
            'edit' => Pages\EditBankAccount::route('/{record}/edit'),
        ];
    }
}

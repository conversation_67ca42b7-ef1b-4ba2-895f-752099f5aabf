<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources\BankAccountResource\Pages;

use App\Filament\Affiliate\Resources\BankAccountResource;
use App\Models\Affiliate;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateBankAccount extends CreateRecord
{
    protected static string $resource = BankAccountResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['owner_type'] = Affiliate::class;
        $data['owner_id'] = Auth::user()->affiliate->id;

        return $data;
    }
}

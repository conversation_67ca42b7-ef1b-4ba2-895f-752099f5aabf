<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources\BankAccountResource\Pages;

use App\Filament\Affiliate\Resources\BankAccountResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBankAccounts extends ListRecords
{
    protected static string $resource = BankAccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

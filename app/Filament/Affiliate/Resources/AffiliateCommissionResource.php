<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources;

use App\Enums\AffiliateCommissionStatus;
use App\Filament\Affiliate\Resources\AffiliateCommissionResource\Pages;
use App\Models\Affiliate;
use App\Models\AffiliateCommission;
use App\Support\Helper;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class AffiliateCommissionResource extends Resource
{
    protected static ?string $model = AffiliateCommission::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationLabel = 'Hoa hồng';

    protected static ?string $modelLabel = 'Hoa hồng';

    protected static ?string $pluralModelLabel = 'Hoa hồng';

    public static function canCreate(): bool
    {
        return false;
    }

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return false;
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns()
                    ->schema([
                        TextEntry::make('purchased_account_id')
                            ->label('Mã đơn hàng'),

                        TextEntry::make('order.user.username')
                            ->label('Người mua'),

                        TextEntry::make('order_amount')
                            ->label('Giá trị đơn hàng')
                            ->formatStateUsing(fn(AffiliateCommission $record): string => Helper::formatCurrency((int) $record->order_amount)),

                        TextEntry::make('commission_rate')
                            ->label('Tỷ lệ hoa hồng')
                            ->suffix('%'),

                        TextEntry::make('commission_amount')
                            ->label('Số tiền hoa hồng')
                            ->formatStateUsing(fn(AffiliateCommission $record): string => Helper::formatCurrency((int) $record->commission_amount)),

                        TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge(),

                        TextEntry::make('created_at')
                            ->label('Ngày tạo')
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('approved_at')
                            ->label('Ngày duyệt'),

                        TextEntry::make('description')
                            ->label('Mô tả'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('purchased_account_id')
                    ->label('Mã đơn hàng')
                    ->searchable()
                    ->sortable(),

                Tables\Columns\TextColumn::make('order_amount')
                    ->label('Giá trị đơn hàng')
                    ->money('VND')
                    ->sortable(),

                Tables\Columns\TextColumn::make('commission_rate')
                    ->label('Tỷ lệ hoa hồng')
                    ->suffix('%')
                    ->sortable(),

                Tables\Columns\TextColumn::make('commission_amount')
                    ->label('Số tiền hoa hồng')
                    ->money('VND')
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(AffiliateCommissionStatus::class),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->whereBelongsTo(Affiliate::query()->firstWhere('user_id', Auth::id()));
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAffiliateCommissions::route('/'),
            'view' => Pages\ViewAffiliateCommission::route('/{record}'),
        ];
    }
}

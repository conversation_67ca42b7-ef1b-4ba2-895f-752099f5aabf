<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources\WithdrawalResource\Pages;

use App\Filament\Affiliate\Resources\WithdrawalResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListWithdrawals extends ListRecords
{
    protected static string $resource = WithdrawalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

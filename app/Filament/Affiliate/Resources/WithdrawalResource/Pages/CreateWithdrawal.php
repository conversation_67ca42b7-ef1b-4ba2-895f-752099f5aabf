<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources\WithdrawalResource\Pages;

use App\Filament\Affiliate\Resources\WithdrawalResource;
use App\Models\Affiliate;
use App\Enums\WithdrawalStatus;
use App\Support\Helper;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class CreateWithdrawal extends CreateRecord
{
    protected static string $resource = WithdrawalResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $affiliate = Affiliate::query()->firstWhere('user_id', Auth::id());

        if ($affiliate->available_commission < $data['amount']) {
            Notification::make()
                ->title('Số dư không đủ')
                ->body('Số dư khả dụng: ' . Helper::formatCurrency((int) $affiliate->available_commission))
                ->danger()
                ->send();

            $this->halt();
        }

        if ($data['amount'] < 100000) {
            Notification::make()
                ->title('Số tiền rút tối thiểu là 100,000đ.')
                ->danger()
                ->send();

            $this->halt();
        }

        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $affiliate = Affiliate::query()->firstWhere('user_id', Auth::id());

        $data['withdrawable_type'] = Affiliate::class;
        $data['withdrawable_id'] = $affiliate->getKey();
        $data['status'] = WithdrawalStatus::Pending;

        $affiliate->decrement('available_commission', (int) $data['amount']);

        return parent::handleRecordCreation($data);
    }
}

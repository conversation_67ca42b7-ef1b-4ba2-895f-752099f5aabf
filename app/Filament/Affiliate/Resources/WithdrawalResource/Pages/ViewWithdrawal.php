<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources\WithdrawalResource\Pages;

use App\Actions\CancelWithdrawalAction;
use App\Filament\Affiliate\Resources\WithdrawalResource;
use App\Enums\WithdrawalStatus;
use App\Models\Withdrawal;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;

class ViewWithdrawal extends ViewRecord
{
    protected static string $resource = WithdrawalResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->label('Hủy yêu cầu')
                ->modalHeading('Hủy yêu cầu rút tiền')
                ->modalDescription('Bạn có chắc chắn muốn hủy yêu cầu rút tiền này không?')
                ->modalSubmitActionLabel('Hủy yêu cầu')
                ->modalCancelActionLabel('Hủy')
                ->action(function (Action $action, Withdrawal $record): void {
                    (new CancelWithdrawalAction())($record);

                    $action->success();
                })
                ->visible(fn(): bool => $this->record->status === WithdrawalStatus::Pending),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Resources;

use App\Enums\WithdrawalStatus;
use App\Filament\Affiliate\Resources\WithdrawalResource\Pages;
use App\Forms\Components\CurrencyInput;
use App\Models\Affiliate;
use App\Models\Withdrawal;
use App\Support\Helper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\Auth;

class WithdrawalResource extends Resource
{
    protected static ?string $model = Withdrawal::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationLabel = 'Rút tiền';

    protected static ?string $modelLabel = 'Rút tiền';

    protected static ?string $pluralModelLabel = 'Rút tiền';

    public static function canEdit($record): bool
    {
        return false;
    }

    public static function canDelete($record): bool
    {
        return $record->status === WithdrawalStatus::Pending;
    }

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns()
                    ->schema([
                        TextEntry::make('amount')
                            ->label('Số tiền rút')
                            ->formatStateUsing(fn(Withdrawal $record): string => Helper::formatCurrency((int) $record->amount)),

                        TextEntry::make('bankAccount.display_name')
                            ->label('Tài khoản ngân hàng'),

                        TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge(),

                        TextEntry::make('created_at')
                            ->label('Ngày yêu cầu')
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('note')
                            ->label('Ghi chú')
                            ->visible(fn(Withdrawal $record): bool => ! is_null($record->note))
                            ->columnSpanFull(),

                        TextEntry::make('approved_at')
                            ->label('Ngày duyệt')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Approved)
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('completed_at')
                            ->label('Ngày hoàn thành')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Completed)
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('rejected_at')
                            ->label('Ngày từ chối')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Rejected)
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('rejection_reason')
                            ->label('Lý do từ chối')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Rejected),
                    ]),
            ]);
    }

    public static function form(Form $form): Form
    {
        $affiliate = Affiliate::query()->firstWhere('user_id', Auth::id());
        $availableBalance = $affiliate ? (int) $affiliate->available_commission : 0;

        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin rút tiền')
                    ->schema([
                        Forms\Components\Placeholder::make('available_balance')
                            ->label('Số dư khả dụng')
                            ->columnSpanFull()
                            ->content(Helper::formatCurrency((int) $availableBalance)),

                        CurrencyInput::make('amount')
                            ->label('Số tiền rút')
                            ->required()
                            ->minValue(100000)
                            ->maxValue($availableBalance)
                            ->helperText('Số tiền rút tối thiểu là ' . Helper::formatCurrency(100000)),

                        Forms\Components\Select::make('bank_account_id')
                            ->label('Tài khoản ngân hàng')
                            ->options(function () {
                                return Affiliate::query()
                                    ->firstWhere('user_id', Auth::id())
                                    ->bankAccounts()
                                    ->where('is_visible', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->helperText('Chọn tài khoản ngân hàng để nhận tiền'),

                        Forms\Components\Textarea::make('note')
                            ->label('Ghi chú')
                            ->columnSpanFull()
                            ->maxLength(1000),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('VND')
                    ->sortable(),

                Tables\Columns\TextColumn::make('bankAccount.display_name')
                    ->label('Tài khoản ngân hàng'),

                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày yêu cầu')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('approved_at')
                    ->label('Ngày duyệt')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('completed_at')
                    ->label('Ngày hoàn thành')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(WithdrawalStatus::class),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Pending),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->where('withdrawable_type', Affiliate::class)
            ->where('withdrawable_id', Affiliate::query()->firstWhere('user_id', Auth::id())->getKey());
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWithdrawals::route('/'),
            'create' => Pages\CreateWithdrawal::route('/create'),
            'view' => Pages\ViewWithdrawal::route('/{record}'),
        ];
    }
}

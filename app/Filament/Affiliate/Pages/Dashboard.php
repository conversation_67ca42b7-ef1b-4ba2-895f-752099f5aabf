<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Pages;

use App\Filament\Affiliate\Widgets\AffiliateOverviewWidget;
use App\Filament\Affiliate\Widgets\AffiliateStatsWidget;
use App\Filament\Affiliate\Widgets\AffiliateUpgradeProgressWidget;
use Filament\Pages\Dashboard as BaseDashboard;

class Dashboard extends BaseDashboard
{
    protected static ?string $navigationIcon = 'heroicon-o-home';

    protected static string $view = 'filament.affiliate.pages.dashboard';

    public function getTitle(): string
    {
        return 'Dashboard';
    }

    public function getWidgets(): array
    {
        return [
            AffiliateOverviewWidget::class,
            AffiliateStatsWidget::class,
            AffiliateUpgradeProgressWidget::class,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Widgets;

use App\Enums\AffiliateCommissionStatus;
use App\Enums\WithdrawalStatus;
use App\Models\Affiliate;
use App\Models\AffiliateCommission;
use App\Models\Withdrawal;
use App\Support\Helper;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Facades\Auth;

class AffiliateOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $affiliate = Affiliate::query()->firstWhere('user_id', Auth::id());

        $totalCommissions = AffiliateCommission::where('affiliate_id', $affiliate->id)
            ->where('status', AffiliateCommissionStatus::Approved)
            ->sum('commission_amount');

        $pendingCommissions = AffiliateCommission::where('affiliate_id', $affiliate->id)
            ->where('status', AffiliateCommissionStatus::Pending)
            ->sum('commission_amount');

        $totalWithdrawals = Withdrawal::query()
            ->where('withdrawable_id', $affiliate->getKey())
            ->where('withdrawable_type', Affiliate::class)
            ->whereIn('status', [WithdrawalStatus::Approved, WithdrawalStatus::Completed])
            ->sum('amount');

        return [
            Stat::make('Tổng lượt click', number_format((int) $affiliate->total_clicks))
                ->description('Số lượt click vào link'),

            Stat::make('Tổng lượt chuyển đổi', number_format((int) $affiliate->total_conversions))
                ->description('Số lượt mua tài khoản thành công'),

            Stat::make('Tỷ lệ chuyển đổi', number_format((float) $affiliate->conversion_rate, 2) . '%')
                ->description('Tỷ lệ click thành đăng ký'),

            Stat::make('Tổng hoa hồng', Helper::formatCurrency((int) $totalCommissions))
                ->description('Tổng hoa hồng đã kiếm được'),

            Stat::make('Hoa hồng khả dụng', Helper::formatCurrency((int) $affiliate->available_commission))
                ->description('Số tiền có thể rút'),

            Stat::make('Tổng doanh số', Helper::formatCurrency((int) $affiliate->total_sales))
                ->description('Tổng doanh số từ giới thiệu'),

            Stat::make('Đã rút tiền', Helper::formatCurrency((int) $totalWithdrawals))
                ->description('Tổng tiền đã rút'),

            Stat::make('Hoa hồng chờ duyệt', Helper::formatCurrency((int) $pendingCommissions))
                ->description('Hoa hồng đang chờ duyệt'),

            Stat::make('Tổng người giới thiệu', number_format((int) $affiliate->total_referrals))
                ->description('Số người đã đăng ký qua link của bạn'),
        ];
    }
}

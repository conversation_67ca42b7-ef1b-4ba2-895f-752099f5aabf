<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Widgets;

use App\Models\Affiliate;
use Filament\Widgets\Widget;
use Illuminate\Support\Facades\Auth;

class AffiliateUpgradeProgressWidget extends Widget
{
    protected static string $view = 'filament.affiliate.widgets.affiliate-upgrade-progress-widget';

    protected int | string | array $columnSpan = [
        'md' => 1,
    ];

    public function getData(): array
    {
        $affiliate = Affiliate::where('user_id', Auth::id())->first();

        $progress = $affiliate->getUpgradeProgress();

        return [
            'has_affiliate' => true,
            'current_tier' => $progress['current_tier'],
            'next_tier' => $progress['next_tier'],
            'can_upgrade' => $progress['can_upgrade'],
            'progress_percentage' => $progress['progress_percentage'],
            'remaining_amount' => $progress['remaining_amount'],
            'total_commission' => $affiliate->getTotalApprovedCommission(),
        ];
    }
}

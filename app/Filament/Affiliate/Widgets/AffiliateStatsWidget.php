<?php

declare(strict_types=1);

namespace App\Filament\Affiliate\Widgets;

use App\Models\Affiliate;
use Filament\Widgets\ChartWidget;
use Illuminate\Support\Facades\Auth;

class AffiliateStatsWidget extends ChartWidget
{
    protected static ?string $heading = 'Thống kê hoạt động';

    protected int | string | array $columnSpan = [
        'md' => 1,
    ];

    protected function getData(): array
    {
        $affiliate = Affiliate::where('user_id', Auth::id())->first();

        return [
            'datasets' => [
                [
                    'label' => 'Lượt click',
                    'data' => [$affiliate->total_clicks],
                    'backgroundColor' => '#3b82f6',
                    'borderColor' => '#1d4ed8',
                    'borderWidth' => 2,
                ],
                [
                    'label' => 'Lượt chuyển đổi',
                    'data' => [$affiliate->total_conversions],
                    'backgroundColor' => '#10b981',
                    'borderColor' => '#047857',
                    'borderWidth' => 2,
                ],
                [
                    'label' => 'Người giới thiệu',
                    'data' => [$affiliate->total_referrals],
                    'backgroundColor' => '#f59e0b',
                    'borderColor' => '#d97706',
                    'borderWidth' => 2,
                ],
            ],
            'labels' => ['Thống kê tổng quan'],
        ];
    }

    protected function getType(): string
    {
        return 'bar';
    }

    protected function getOptions(): array
    {
        return [
            'responsive' => true,
            'maintainAspectRatio' => false,
            'plugins' => [
                'legend' => [
                    'display' => true,
                    'position' => 'top',
                ],
                'tooltip' => [
                    'enabled' => true,
                ],
            ],
            'scales' => [
                'y' => [
                    'beginAtZero' => true,
                    'ticks' => [
                        'stepSize' => 1,
                    ],
                ],
            ],
        ];
    }
}

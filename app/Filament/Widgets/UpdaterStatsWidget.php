<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Services\UpdaterService;
use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Carbon\Carbon;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class UpdaterStatsWidget extends BaseWidget
{
    use HasWidgetShield;

    protected static ?int $sort = 2;

    protected function getStats(): array
    {
        $updaterService = app(UpdaterService::class);
        $currentVersion = $updaterService->getCurrentVersion();
        $updateHistory = $updaterService->getUpdateHistory();

        $lastUpdate = collect($updateHistory)->last();
        $totalUpdates = count($updateHistory);

        return [
            Stat::make('Phiên bản hiện tại', $currentVersion)
                ->description('Phiên bản hệ thống đang chạy'),

            Stat::make('<PERSON>ần cập nhật cuối', $lastUpdate ? $lastUpdate['version'] : 'Chưa có')
                ->description($lastUpdate ? 'Cập nhật lúc ' . Carbon::parse($lastUpdate['timestamp'])->format('d/m/Y H:i') : 'Chưa có lần cập nhật nào'),

            Stat::make('Tổng số lần cập nhật', $totalUpdates)
                ->description('Số lần cập nhật đã thực hiện'),
        ];
    }
}

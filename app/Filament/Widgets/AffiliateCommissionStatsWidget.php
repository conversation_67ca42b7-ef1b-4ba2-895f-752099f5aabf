<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Models\AffiliateCommission;
use App\Support\Helper;
use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;

class AffiliateCommissionStatsWidget extends BaseWidget
{
    use HasWidgetShield;

    protected static ?string $pollingInterval = null;

    protected static ?int $sort = 1;

    protected function getStats(): array
    {
        $stats = AffiliateCommission::query()
            ->selectRaw('
                COUNT(*) as totalCommissions,
                SUM(commission_amount) as totalAmount,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as pendingCommissions,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as approvedCommissions,
                SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as paidCommissions,
                SUM(CASE WHEN status = ? THEN commission_amount ELSE 0 END) as pendingAmount,
                SUM(CASE WHEN status = ? THEN commission_amount ELSE 0 END) as approvedAmount,
                SUM(CASE WHEN status = ? THEN commission_amount ELSE 0 END) as paidAmount
            ', [
                'pending',
                'approved',
                'paid',
                'pending',
                'approved',
                'paid',
            ])
            ->first();

        $totalCommissions = $stats->totalCommissions ?? 0;
        $pendingCommissions = $stats->pendingCommissions ?? 0;
        $approvedCommissions = $stats->approvedCommissions ?? 0;
        $paidCommissions = $stats->paidCommissions ?? 0;

        $totalAmount = $stats->totalAmount ?? 0;
        $pendingAmount = $stats->pendingAmount ?? 0;
        $approvedAmount = $stats->approvedAmount ?? 0;
        $paidAmount = $stats->paidAmount ?? 0;

        return [
            Stat::make('Tổng hoa hồng', Helper::formatCurrency((int) $totalAmount))
                ->description("{$totalCommissions} đơn hoa hồng"),

            Stat::make('Chờ duyệt', Helper::formatCurrency((int) $pendingAmount))
                ->description("{$pendingCommissions} đơn hoa hồng"),

            Stat::make('Đã duyệt', Helper::formatCurrency((int) $approvedAmount))
                ->description("{$approvedCommissions} đơn hoa hồng"),

            Stat::make('Đã thanh toán', Helper::formatCurrency((int) $paidAmount))
                ->description("{$paidCommissions} đơn hoa hồng"),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Widgets;

use App\Enums\AccountStatus;
use App\Models\Account;
use App\Models\Recharge;
use App\Models\User;
use App\Support\Helper;
use <PERSON>zhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\Concerns\InteractsWithPageFilters;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class StatsOverview extends BaseWidget
{
    use HasWidgetShield;
    use InteractsWithPageFilters;

    protected function getStats(): array
    {
        [$startDate, $endDate] = explode(' - ', $this->filters['date_range']);

        $calculate = function (string $model) use ($startDate, $endDate): Builder {
            return $model::query()
                ->whereDate('created_at', '>=', Carbon::createFromFormat('d/m/Y', $startDate)->startOfDay())
                ->whereDate('created_at', '<=', Carbon::createFromFormat('d/m/Y', $endDate)->endOfDay());
        };

        $users = $calculate(User::class)->count();
        $balance = $calculate(User::class)->sum('balance');
        $recharges = $calculate(Recharge::class)->count();
        $rechargeAmount = $calculate(Recharge::class)->sum('amount');
        /** @var object{total: int, pending: int, selling: int, sold: int} $accounts */
        $accounts = $calculate(Account::class)
            ->select([
                DB::raw('COUNT(*) as total'),
                DB::raw("SUM(CASE WHEN status = ' " . AccountStatus::Pending->value . " ' THEN 1 ELSE 0 END) as pending"),
                DB::raw("SUM(CASE WHEN status = ' " . AccountStatus::Selling->value . " ' THEN 1 ELSE 0 END) as selling"),
                DB::raw("SUM(CASE WHEN status = ' " . AccountStatus::Sold->value . " ' THEN 1 ELSE 0 END) as sold"),
            ])
            ->first();

        return [
            Stat::make('Người dùng', number_format($users)),
            Stat::make('Tổng số dư', Helper::formatCurrency((int) $balance)),
            Stat::make('Thẻ đã nạp', number_format($recharges)),
            Stat::make('Tổng tiền nạp', Helper::formatCurrency((int) $rechargeAmount)),
            Stat::make('Tổng tài khoản', number_format((int) $accounts->total ?: 0)),
            Stat::make('Tài khoản chờ duyệt', number_format((int) $accounts->pending ?: 0)),
            Stat::make('Tài khoản đang bán', number_format((int) $accounts->selling ?: 0)),
            Stat::make('Tài khoản đã bán', number_format((int) $accounts->sold ?: 0)),
        ];
    }
}

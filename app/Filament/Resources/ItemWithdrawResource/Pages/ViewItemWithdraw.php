<?php

declare(strict_types=1);

namespace App\Filament\Resources\ItemWithdrawResource\Pages;

use App\Enums\ItemWithdrawStatus;
use App\Filament\Resources\ItemWithdrawResource;
use App\Models\ItemWithdraw;
use Filament\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\ToggleButtons;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewItemWithdraw extends ViewRecord
{
    protected static string $resource = ItemWithdrawResource::class;

    protected function getActions(): array
    {
        /** @var ItemWithdraw $record */
        $record = $this->getRecord();

        return [
            Action::make('moderate')
                ->label('Duyệt đơn')
                ->icon('heroicon-o-check-circle')
                ->form([
                    ToggleButtons::make('status')
                        ->label('Trạng thái')
                        ->inline()
                        ->options(ItemWithdrawStatus::class)
                        ->reactive()
                        ->default($record->status),
                    Checkbox::make('return')
                        ->label('Trả lại vật phẩm')
                        ->visible(fn(Get $get): bool => $get('status') === ItemWithdrawStatus::Rejected),
                ])
                ->action(function (array $data) use ($record): void {
                    $record->update([
                        'status' => $data['status'],
                    ]);

                    if ($data['status'] === ItemWithdrawStatus::Rejected && $data['return']) {
                        $record->gameItem->userItem()->increment('quantity', $record->quantity);

                        Notification::make()
                            ->title('Đơn rút vật phẩm bị từ chối')
                            ->body("Đơn rút vật phẩm #{$record->id} đã bị từ chối và vật phẩm đã được trả lại cho người dùng.");
                        return;
                    }

                    Notification::make()
                        ->title('Đơn rút vật phẩm đã được duyệt')
                        ->body("Đơn rút vật phẩm #{$record->id} đã được duyệt thành công.");
                }),
        ];
    }
}

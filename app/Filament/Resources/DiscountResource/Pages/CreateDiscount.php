<?php

declare(strict_types=1);

namespace App\Filament\Resources\DiscountResource\Pages;

use App\Filament\Resources\DiscountResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class CreateDiscount extends CreateRecord
{
    protected static string $resource = DiscountResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $startAt = Carbon::parse($data['start_date'])
            ->timezone(config('app.timezone'))
            ->setTimeFrom($data['start_time']);
        $endAt = null;

        if (! $data['is_permanent']) {
            $endAt = Carbon::parse($data['end_date'])
                ->timezone(config('app.timezone'))
                ->setTimeFrom($data['end_time']);
        }

        Arr::forget($data, ['is_permanent', 'start_date', 'start_time', 'end_date', 'end_time']);

        return [
            ...$data,
            'start_at' => $startAt,
            'end_at' => $endAt,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources\DiscountResource\Pages;

use App\Enums\DiscountScope;
use App\Filament\Resources\DiscountResource;
use App\Models\Discount;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Carbon;

class EditDiscount extends EditRecord
{
    protected static string $resource = DiscountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        $startAt = Carbon::parse($data['start_at'])->timezone(config('app.timezone'));
        $endAt = $data['end_at'] ? Carbon::parse($data['end_at'])->timezone(config('app.timezone')) : null;

        return [
            ...$data,
            'is_permanent' => ! $endAt instanceof Carbon,
            'start_date' => $startAt->format('Y-m-d'),
            'start_time' => $startAt->format('H:i:s'),
            'end_date' => $endAt?->format('Y-m-d'),
            'end_time' => $endAt?->format('H:i:s'),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {
        $startAt = Carbon::parse($data['start_date'])
            ->timezone(config('app.timezone'))
            ->setTimeFrom($data['start_time']);
        $endAt = null;

        if (! $data['is_permanent']) {
            $endAt = Carbon::parse($data['end_date'])
                ->timezone(config('app.timezone'))
                ->setTimeFrom($data['end_time']);
        }

        Arr::forget($data, ['is_permanent', 'start_date', 'start_time', 'end_date', 'end_time']);

        return [
            ...$data,
            'start_at' => $startAt,
            'end_at' => $endAt,
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        /** @var Discount $record */
        $record = parent::handleRecordUpdate($record, $data);

        $detachMethods = [
            DiscountScope::All->value => ['categories', 'accounts', 'users'],
            DiscountScope::Category ->value => ['accounts', 'users'],
            DiscountScope::Account->value => ['categories', 'users'],
            DiscountScope::User->value => ['categories', 'accounts'],
        ];

        foreach ($detachMethods[$record->applied_to->value] as $relation) {
            $record->$relation()->detach();
        }

        return $record;
    }
}

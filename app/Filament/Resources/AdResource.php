<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AdResource\Pages;
use App\Models\Ad;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AdResource extends Resource
{
    protected static ?string $model = Ad::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $navigationGroup = 'Quản lý Nội dung';

    protected static ?string $navigationLabel = 'Quảng cáo';

    protected static ?int $navigationSort = 3;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cơ bản')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên quảng cáo')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(3)
                            ->maxLength(1000),
                        Forms\Components\Select::make('position')
                            ->label('Vị trí hiển thị')
                            ->options(Ad::getPositions())
                            ->required()
                            ->searchable(),
                        Forms\Components\Select::make('type')
                            ->label('Loại quảng cáo')
                            ->options(Ad::getTypes())
                            ->required()
                            ->reactive(),
                    ])
                    ->columns(2),

                Forms\Components\Section::make('Nội dung quảng cáo')
                    ->schema([
                        Forms\Components\Textarea::make('content')
                            ->label('Nội dung')
                            ->required()
                            ->rows(10)
                            ->helperText(function (Forms\Get $get): string {
                                $type = $get('type');
                                return match ($type) {
                                    'html' => 'Nhập HTML tùy chỉnh',
                                    'image' => 'Nhập URL hình ảnh',
                                    'banner' => 'Nhập URL banner',
                                    'script' => 'Nhập script (Google Ads, Facebook Pixel, etc.)',
                                    default => 'Nhập nội dung quảng cáo',
                                };
                            }),
                        Forms\Components\TextInput::make('link_url')
                            ->label('URL khi click')
                            ->url()
                            ->helperText('URL sẽ mở khi người dùng click vào quảng cáo'),
                    ]),

                Forms\Components\Section::make('Cài đặt hiển thị')
                    ->schema([
                        Forms\Components\Toggle::make('is_active')
                            ->label('Kích hoạt')
                            ->default(true),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Thứ tự hiển thị')
                            ->numeric()
                            ->default(0)
                            ->helperText('Số càng nhỏ hiển thị càng trước'),
                        Forms\Components\DateTimePicker::make('start_at')
                            ->label('Thời gian bắt đầu')
                            ->helperText('Để trống để hiển thị ngay'),
                        Forms\Components\DateTimePicker::make('end_at')
                            ->label('Thời gian kết thúc')
                            ->helperText('Để trống để hiển thị vĩnh viễn')
                            ->after('start_at'),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('position')
                    ->label('Vị trí')
                    ->formatStateUsing(fn(string $state): string => Ad::getPositions()[$state] ?? $state)
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Loại')
                    ->formatStateUsing(fn(string $state): string => Ad::getTypes()[$state] ?? $state)
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_active')
                    ->label('Trạng thái')
                    ->boolean()
                    ->sortable(),
                Tables\Columns\TextColumn::make('sort_order')
                    ->label('Thứ tự')
                    ->sortable(),
                Tables\Columns\TextColumn::make('start_at')
                    ->label('Bắt đầu')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_at')
                    ->label('Kết thúc')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('position')
                    ->label('Vị trí')
                    ->options(Ad::getPositions()),
                Tables\Filters\SelectFilter::make('type')
                    ->label('Loại')
                    ->options(Ad::getTypes()),
                Tables\Filters\TernaryFilter::make('is_active')
                    ->label('Trạng thái'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAds::route('/'),
            'create' => Pages\CreateAd::route('/create'),
            'edit' => Pages\EditAd::route('/{record}/edit'),
        ];
    }
}

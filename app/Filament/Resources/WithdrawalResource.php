<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\WithdrawalStatus;
use App\Enums\TransactionType;
use App\Filament\Resources\WithdrawalResource\Pages;
use App\Forms\Components\CurrencyInput;
use App\Models\Affiliate;
use App\Models\User;
use App\Models\Withdrawal;
use App\Support\Helper;
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Infolist;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Filament\Forms;
use Illuminate\Support\Facades\Auth;

class WithdrawalResource extends Resource
{
    protected static ?string $model = Withdrawal::class;

    protected static ?string $navigationIcon = 'heroicon-o-banknotes';

    protected static ?string $navigationGroup = 'Quản lý Tài chính';

    protected static ?string $navigationLabel = 'Yêu cầu rút tiền';

    protected static ?string $modelLabel = 'Rút tiền';

    protected static ?int $navigationSort = 4;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns()
                    ->schema([
                        TextEntry::make('withdrawable_id')
                            ->label('Người rút')
                            ->formatStateUsing(fn(Withdrawal $record): string => $record->withdrawable->getWithdrawableName()),

                        TextEntry::make('amount')
                            ->label('Số tiền rút')
                            ->formatStateUsing(fn(Withdrawal $record): string => Helper::formatCurrency((int) $record->amount)),

                        TextEntry::make('bankAccount.bank_name')
                            ->label('Ngân hàng'),

                        TextEntry::make('bankAccount.account_holder')
                            ->label('Tên chủ tài khoản'),

                        TextEntry::make('bankAccount.account_number')
                            ->label('Số tài khoản'),

                        TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge(),

                        TextEntry::make('created_at')
                            ->label('Ngày yêu cầu')
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('note')
                            ->label('Ghi chú')
                            ->visible(fn(Withdrawal $record): bool => $record->note !== null)
                            ->columnSpanFull(),

                        TextEntry::make('approved_at')
                            ->label('Ngày duyệt')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Approved || $record->status === WithdrawalStatus::Completed)
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('completed_at')
                            ->label('Ngày hoàn thành')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Completed)
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('rejected_at')
                            ->label('Ngày từ chối')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Rejected)
                            ->dateTime('d/m/Y H:i'),

                        TextEntry::make('rejection_reason')
                            ->label('Lý do từ chối')
                            ->visible(fn(Withdrawal $record): bool => $record->status === WithdrawalStatus::Rejected)
                            ->columnSpanFull(),
                    ]),
            ]);
    }

    public static function form(Form $form): Form
    {
        $availableBalance = Auth::user()->balance;

        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin rút tiền')
                    ->schema([
                        Forms\Components\Placeholder::make('available_balance')
                            ->label('Số dư khả dụng')
                            ->columnSpanFull()
                            ->content(Helper::formatCurrency((int) $availableBalance)),

                        CurrencyInput::make('amount')
                            ->label('Số tiền rút')
                            ->required()
                            ->minValue(100000)
                            ->maxValue($availableBalance)
                            ->helperText('Số tiền rút tối thiểu là ' . Helper::formatCurrency(100000)),

                        Forms\Components\Select::make('bank_account_id')
                            ->label('Tài khoản ngân hàng')
                            ->options(function () {
                                return Auth::user()
                                    ->bankAccounts()
                                    ->where('is_visible', true)
                                    ->pluck('name', 'id');
                            })
                            ->required()
                            ->searchable()
                            ->helperText('Chọn tài khoản ngân hàng để nhận tiền'),

                        Forms\Components\Textarea::make('note')
                            ->label('Ghi chú')
                            ->columnSpanFull()
                            ->maxLength(1000),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('withdrawable_id')
                    ->label('Người rút')
                    ->visible(fn() => Auth::user()->hasRole('collaborator'))
                    ->formatStateUsing(fn(Withdrawal $record): string => $record->withdrawable->getWithdrawableName()),

                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->money('VND')
                    ->sortable(),

                Tables\Columns\TextColumn::make('bankAccount.display_name')
                    ->label('Tài khoản ngân hàng')
                    ->limit(30),

                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge(),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Ngày yêu cầu')
                    ->dateTime('d/m/Y H:i')
                    ->sortable(),

                Tables\Columns\TextColumn::make('approved_at')
                    ->label('Ngày duyệt')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),

                Tables\Columns\TextColumn::make('completed_at')
                    ->label('Ngày hoàn thành')
                    ->dateTime('d/m/Y H:i')
                    ->sortable()
                    ->toggleable(isToggledHiddenByDefault: true),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(WithdrawalStatus::class),

                Tables\Filters\SelectFilter::make('withdrawable_type')
                    ->label('Loại')
                    ->visible(fn() => Auth::user()->hasRole('collaborator'))
                    ->options([
                        Affiliate::class => 'Affiliate',
                        User::class => 'Cộng tác viên',
                    ]),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('approve_selected')
                        ->label('Duyệt đã chọn')
                        ->visible(fn() => Auth::user()->hasRole('collaborator'))
                        ->icon('heroicon-o-check-circle')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            $approvedCount = 0;

                            foreach ($records as $record) {
                                if ($record->status === WithdrawalStatus::Pending) {
                                    $record->approve();

                                    if ($record->withdrawable_type === User::class) {
                                        $record->withdrawable->recordTransaction(
                                            TransactionType::Withdrawal,
                                            $record->amount,
                                            $record,
                                            'Rút tiền CTV #' . $record->id,
                                        );
                                    } elseif ($record->withdrawable_type === Affiliate::class) {
                                        $record->withdrawable->decrement('available_commission', $record->amount);
                                    }

                                    $approvedCount++;
                                }
                            }

                            Notification::make()
                                ->title("Đã duyệt {$approvedCount} yêu cầu rút tiền")
                                ->success()
                                ->send();
                        }),
                ]),
            ]);
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->when(Auth::user()->hasRole('collaborator'), function (Builder $query): void {
                $query
                    ->where('withdrawable_type', User::class)
                    ->where('withdrawable_id', Auth::id());
            });
    }

    public static function getNavigationBadge(): ?string
    {
        if (Auth::user()->hasRole('collaborator')) {
            return null;
        }

        $count = Withdrawal::query()->where('status', WithdrawalStatus::Pending)->count();

        return $count > 0 ? number_format($count) : null;
    }

    public static function getNavigationBadgeColor(): string|null
    {
        return 'warning';
    }

    public static function canCreate(): bool
    {
        return Auth::user()->hasRole('collaborator');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWithdrawals::route('/'),
            'create' => Pages\CreateWithdrawal::route('/create'),
            'view' => Pages\ViewWithdrawal::route('/{record}'),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\CustomFieldType;
use App\Filament\Resources\GameItemResource\Pages;
use App\Models\GameItem;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class GameItemResource extends Resource
{
    protected static ?string $model = GameItem::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Vật Phẩm';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 8;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên vật phẩm')
                    ->columnSpanFull()
                    ->required(),
                Forms\Components\Section::make()
                    ->visibleOn('edit')
                    ->heading('Cấu hình gói rút')
                    ->collapsible()
                    ->collapsed()
                    ->persistCollapsed()
                    ->schema([
                        Forms\Components\Repeater::make('withdraw_packages')
                            ->hiddenLabel()
                            ->label('Gói rút')
                            ->columns()
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Tên gói rút')
                                    ->required(),
                                Forms\Components\TextInput::make('value')
                                    ->label('Giá trị')
                                    ->numeric()
                                    ->required(),
                                Forms\Components\Textarea::make('description')
                                    ->label('Mô tả')
                                    ->columnSpanFull(),
                            ]),
                    ]),
                Forms\Components\Section::make()
                    ->visibleOn('edit')
                    ->heading('Cấu hình trường tùy chỉnh')
                    ->collapsible()
                    ->collapsed()
                    ->persistCollapsed()
                    ->schema([
                        Forms\Components\Repeater::make('custom_fields')
                            ->hiddenLabel()
                            ->label('Trường tùy chỉnh')
                            ->columns()
                            ->relationship('customFields')
                            ->orderColumn('order')
                            ->schema([
                                Forms\Components\TextInput::make('name')
                                    ->label('Tên trường')
                                    ->required(),
                                Forms\Components\Select::make('type')
                                    ->label('Loại trường')
                                    ->options(CustomFieldType::class)
                                    ->live()
                                    ->required(),
                                Forms\Components\TextInput::make('description')
                                    ->label('Mô tả')
                                    ->columnSpanFull(),
                                Forms\Components\TextInput::make('default_value')
                                    ->label('Giá trị mặc định')
                                    ->columnSpanFull()
                                    ->nullable(),
                                Forms\Components\Toggle::make('required')
                                    ->label('Bắt buộc'),
                                Forms\Components\Repeater::make('options')
                                    ->label('Tùy chọn')
                                    ->required()
                                    ->visible(fn(Forms\Get $get): bool => $get('type') && CustomFieldType::tryFrom($get('type'))->requiresOptions())
                                    ->columnSpanFull()
                                    ->simple(
                                        Forms\Components\TextInput::make('value')
                                            ->required(),
                                    ),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên vật phẩm')
                    ->searchable(),
                DateTimeColumn::make('created_at')
                    ->toggledHiddenByDefault(false)
                    ->label('Ngày tạo'),
                DateTimeColumn::make('updated_at')
                    ->label('Ngày cập nhật'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListGameItems::route('/'),
            'edit' => Pages\EditGameItem::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

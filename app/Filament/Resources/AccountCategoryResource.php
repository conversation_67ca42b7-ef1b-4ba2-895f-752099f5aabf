<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\GameResource\Pages;
use App\Models\AccountCategory;
use App\Tables\Columns\DateTimeColumn;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class AccountCategoryResource extends Resource
{
    protected static ?string $model = AccountCategory::class;

    protected static ?string $navigationIcon = 'heroicon-o-puzzle-piece';

    protected static ?string $modelLabel = 'Danh mục tài khoản';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('game_id')
                            ->label('Game')
                            ->relationship('game', 'name')
                            ->columnSpanFull()
                            ->required(),
                        SelectTree::make('parent_id')
                            ->label('Danh mục cha')
                            ->relationship('parent', 'name', 'parent_id')
                            ->searchable()
                            ->withCount()
                            ->enableBranchNode()
                            ->placeholder('Chọn danh mục cha (để trống nếu là danh mục chính)')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('name')
                            ->label('Tên danh mục')
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn(Forms\Set $set, ?string $state): mixed => $set('slug', Str::slug($state)))
                            ->required(),
                        Forms\Components\TextInput::make('slug')
                            ->unique(ignoreRecord: true)
                            ->required(),
                        Forms\Components\FileUpload::make('image')
                            ->label('Ảnh')
                            ->columnSpanFull()
                            ->directory('account-categories')
                            ->image(),
                        Forms\Components\RichEditor::make('description')
                            ->label('Mô tả')
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('order')
                            ->label('Thứ tự hiển thị')
                            ->numeric()
                            ->default(0)
                            ->helperText('Số càng nhỏ thì vị trí hiển thị càng cao')
                            ->required(),
                        Forms\Components\Toggle::make('is_visible')
                            ->label('Hiển thị')
                            ->required()
                            ->default(true),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->reorderable('order')
            ->columns([
                Tables\Columns\TextColumn::make('game.name')
                    ->label('Game')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('parent.name')
                    ->label('Danh mục cha')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên danh mục')
                    ->searchable(),
                Tables\Columns\ImageColumn::make('image')
                    ->label('Ảnh'),
                Tables\Columns\TextColumn::make('children_count')
                    ->label('Số danh mục con')
                    ->counts('children'),
                Tables\Columns\TextColumn::make('order')
                    ->label('Thứ tự')
                    ->sortable()
                    ->badge()
                    ->color('primary'),
                Tables\Columns\ToggleColumn::make('is_visible')
                    ->label('Hiển thị'),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAccountCategory::route('/'),
            'create' => Pages\CreateAccountCategory::route('/create'),
            'edit' => Pages\EditAccountCategory::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

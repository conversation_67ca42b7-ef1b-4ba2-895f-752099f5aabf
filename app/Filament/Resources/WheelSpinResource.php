<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\WheelSpinResource\Pages;
use App\Models\WheelSpin;
use App\Tables\Columns\DateTimeColumn;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class WheelSpinResource extends Resource
{
    protected static ?string $model = WheelSpin::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Lịch sử quay vòng quay';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 11;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                TextEntry::make('user.username')
                    ->url(fn(WheelSpin $wheelSpin): string => UserResource::getUrl('edit', [$wheelSpin->user]))
                    ->label('Người dùng'),
                TextEntry::make('wheel.name')
                    ->url(fn(WheelSpin $wheelSpin): string => WheelResource::getUrl('edit', [$wheelSpin->wheel]))
                    ->label('Vòng quay'),
                TextEntry::make('price')
                    ->label('Giá tiền')
                    ->money(),
                TextEntry::make('result')
                    ->label('Kết quả'),
                TextEntry::make('created_at')
                    ->label('Quay lúc'),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.username')
                    ->label('Người dùng')
                    ->sortable(),
                Tables\Columns\TextColumn::make('wheel.name')
                    ->label('Vòng quay')
                    ->sortable(),
                Tables\Columns\TextColumn::make('result')
                    ->label('Kết quả')
                    ->searchable(),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWheelSpins::route('/'),
            'view' => Pages\ViewWheelSpin::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

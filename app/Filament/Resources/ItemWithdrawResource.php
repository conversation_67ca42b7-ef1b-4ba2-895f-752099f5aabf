<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\ItemWithdrawStatus;
use App\Filament\Resources\ItemWithdrawResource\Pages;
use App\Models\ItemWithdraw;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms\Components\ToggleButtons;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ItemWithdrawResource extends Resource
{
    protected static ?string $model = ItemWithdraw::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Đơn rút vật phẩm';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 9;

    public static function infolist(Infolist $infolist): Infolist
    {
        $schema[] = Section::make()
            ->columns(3)
            ->schema([
                TextEntry::make('id')
                    ->label('ID'),
                TextEntry::make('gameItem.name')
                    ->label('Vật phẩm'),
                TextEntry::make('user.username')
                    ->label('Người dùng'),
                TextEntry::make('quantity')
                    ->label('Số lượng')
                    ->numeric(),
                TextEntry::make('status')
                    ->label('Trạng thái')
                    ->badge(),
                TextEntry::make('created_at')
                    ->label('Ngày tạo')
                    ->dateTime(),
            ]);

        $schema[] = Section::make()
            ->columns(3)
            ->visible(fn(ItemWithdraw $itemWithdraw) => $itemWithdraw->customFieldResponses->isNotEmpty())
            ->schema(function (ItemWithdraw $itemWithdraw): array {
                $schema = [];

                foreach ($itemWithdraw->customFieldResponses as $response) {
                    $schema[] = TextEntry::make($response->customField->name)
                        ->label($response->customField->name)
                        ->state($response->value);
                }

                return $schema;
            });

        return $infolist->schema($schema);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('gameItem.name')
                    ->label('Vật phẩm')
                    ->sortable(),
                Tables\Columns\TextColumn::make('user.username')
                    ->label('Người dùng')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('quantity')
                    ->label('Số lượng')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->searchable(),
                DateTimeColumn::make('created_at', false)
                    ->label('Ngày tạo'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\BulkAction::make('moderate')
                        ->label('Duyệt đơn')
                        ->color('primary')
                        ->icon('heroicon-o-check-circle')
                        ->form([
                            ToggleButtons::make('status')
                                ->label('Trạng thái')
                                ->inline()
                                ->reactive()
                                ->options(ItemWithdrawStatus::class)
                                ->default(ItemWithdrawStatus::Pending),
                        ])
                        ->action(fn(Collection $records, $data) =>  $records->each->update($data)),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListItemWithdraws::route('/'),
            'view' => Pages\ViewItemWithdraw::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

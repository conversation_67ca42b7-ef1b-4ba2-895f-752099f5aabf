<?php

declare(strict_types=1);

namespace App\Filament\Resources\AttributeSetResource\RelationManagers;

use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Contracts\Support\Htmlable;
use Illuminate\Support\Str;

class AttributesRelationManager extends RelationManager
{
    protected static string $relationship = 'attributes';

    protected static ?string $modelLabel = 'Thuộc tính';

    protected function getTableHeading(): string|Htmlable|null
    {
        return 'Danh sách thuộc tính';
    }

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->label('Tên thuộc tính')
                    ->live(onBlur: true)
                    ->afterStateUpdated(fn(Forms\Set $set, ?string $state): mixed => $set('slug', Str::slug($state)))
                    ->required(),
                Forms\Components\TextInput::make('slug')
                    ->required(),
                Forms\Components\Toggle::make('is_visible')
                    ->label('Hiển thị')
                    ->default(true)
                    ->required()
                    ->columnSpanFull(),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên thuộc tính')
                    ->searchable(),
                Tables\Columns\ToggleColumn::make('is_visible')
                    ->label('Hiển thị'),
                DateTimeColumn::make('created_at')
                    ->label('Ngày tạo'),
                DateTimeColumn::make('updated_at')
                    ->label('Ngày cập nhật'),
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }
}

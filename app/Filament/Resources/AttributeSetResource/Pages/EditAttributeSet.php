<?php

declare(strict_types=1);

namespace App\Filament\Resources\AttributeSetResource\Pages;

use App\Filament\Resources\AttributeSetResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAttributeSet extends EditRecord
{
    protected static string $resource = AttributeSetResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }
}

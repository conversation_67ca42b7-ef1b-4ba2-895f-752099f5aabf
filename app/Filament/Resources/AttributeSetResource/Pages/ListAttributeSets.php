<?php

declare(strict_types=1);

namespace App\Filament\Resources\AttributeSetResource\Pages;

use App\Filament\Resources\AttributeSetResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAttributeSets extends ListRecords
{
    protected static string $resource = AttributeSetResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\RechargeStatus;
use App\Filament\Resources\RechargeResource\Pages;
use App\Models\Recharge;
use App\Tables\Columns\DateTimeColumn;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class RechargeResource extends Resource
{
    protected static ?string $model = Recharge::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Đơn nạp thẻ';

    protected static ?string $navigationGroup = 'Quản lý Tài chính';

    protected static ?int $navigationSort = 2;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('user.display_name')
                            ->label('Người dùng')
                            ->url(fn(Recharge $recharge): string => UserResource::getUrl('edit', [$recharge->user])),
                        TextEntry::make('provider')
                            ->default('Nạp thủ công')
                            ->label('Cổng tích hợp'),
                        TextEntry::make('type')
                            ->label('Loại thẻ'),
                        TextEntry::make('declared_amount')
                            ->label('Mệnh giá')
                            ->money(),
                        TextEntry::make('amount')
                            ->label('Thực nhận')
                            ->default(0)
                            ->money(),
                        TextEntry::make('serial')
                            ->label('Số serial'),
                        TextEntry::make('pin')
                            ->label('Mã thẻ'),
                        TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge(),
                        TextEntry::make('created_at')
                            ->label('Nạp lúc'),
                        TextEntry::make('updated_at')
                            ->label('Cập nhật lúc'),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('user.username')
                    ->label('Người dùng')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Loại thẻ')
                    ->searchable(),
                Tables\Columns\TextColumn::make('declared_amount')
                    ->label('Mệnh giá')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Thực nhận')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('serial')
                    ->searchable(),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->searchable(),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListRecharges::route('/'),
            'view' => Pages\ViewRecharge::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }

    public static function getNavigationBadge(): ?string
    {
        $count = static::getModel()::query()->where('status', RechargeStatus::Pending)->count();

        if ($count === 0) {
            return null;
        }

        return number_format($count);
    }

    public static function getNavigationBadgeColor(): string|array|null
    {
        return 'warning';
    }
}

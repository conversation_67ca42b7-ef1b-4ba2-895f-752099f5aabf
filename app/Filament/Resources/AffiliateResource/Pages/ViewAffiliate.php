<?php

declare(strict_types=1);

namespace App\Filament\Resources\AffiliateResource\Pages;

use App\Actions\Affiliate\ActivateAffiliateAction;
use App\Actions\Affiliate\ApproveAffiliateAction;
use App\Actions\Affiliate\RejectAffiliateAction;
use App\Actions\Affiliate\SuspendAffiliateAction;
use App\Enums\AffiliateStatus;
use App\Filament\Resources\AffiliateResource;
use Filament\Actions\Action;
use Filament\Actions\DeleteAction;
use Filament\Resources\Pages\ViewRecord;

class ViewAffiliate extends ViewRecord
{
    protected static string $resource = AffiliateResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('approve')
                ->label('Duyệt')
                ->icon('heroicon-o-check')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn(): bool => $this->record->status === AffiliateStatus::Pending)
                ->action(function (): void {
                    (new ApproveAffiliateAction())($this->record);
                    $this->refreshFormData(['status']);
                }),
            Action::make('reject')
                ->label('Từ chối')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->requiresConfirmation()
                ->visible(fn(): bool => $this->record->status === AffiliateStatus::Pending)
                ->action(function (): void {
                    (new RejectAffiliateAction())($this->record);
                    $this->refreshFormData(['status']);
                }),
            Action::make('suspend')
                ->label('Tạm khóa')
                ->icon('heroicon-o-pause')
                ->color('warning')
                ->requiresConfirmation()
                ->visible(fn(): bool => $this->record->status === AffiliateStatus::Approved)
                ->action(function (): void {
                    (new SuspendAffiliateAction())($this->record);
                    $this->refreshFormData(['status']);
                }),
            Action::make('activate')
                ->label('Kích hoạt')
                ->icon('heroicon-o-play')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn(): bool => $this->record->status === AffiliateStatus::Suspended)
                ->action(function (): void {
                    (new ActivateAffiliateAction())($this->record);
                    $this->refreshFormData(['status']);
                }),
            DeleteAction::make(),
        ];
    }
}

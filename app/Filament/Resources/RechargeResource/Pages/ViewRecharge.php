<?php

declare(strict_types=1);

namespace App\Filament\Resources\RechargeResource\Pages;

use App\Actions\UpdateRechargeAction;
use App\Enums\RechargeStatus;
use App\Filament\Resources\RechargeResource;
use App\Models\Recharge;
use Filament\Actions\Action;
use Filament\Forms\Components\ToggleButtons;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;

class ViewRecharge extends ViewRecord
{
    protected static string $resource = RechargeResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Action::make('edit')
                ->visible(function () {
                    /** @var Recharge */
                    $record = $this->getRecord();

                    return $record->status === RechargeStatus::Pending;
                })
                ->label('Chỉnh sửa')
                ->fillForm(fn(Recharge $record): array => [
                    'status' => $record->status,
                ])
                ->form([
                    ToggleButtons::make('status')
                        ->label('Trạng thái')
                        ->inline()
                        ->options(RechargeStatus::class),
                ])
                ->action(function (array $data): void {
                    /** @var Recharge $record */
                    $record = $this->getRecord();

                    app(UpdateRechargeAction::class)($record, $data['status'], $record->declared_amount);

                    Notification::make()
                        ->success()
                        ->title('Cập nhật trạng thái thành công')
                        ->body('Trạng thái của giao dịch nạp tiền đã được cập nhật.')
                        ->send();
                }),
        ];
    }
}

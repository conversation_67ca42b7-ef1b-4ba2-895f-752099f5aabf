<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Actions\Affiliate\ApproveAffiliateAction;
use App\Actions\Affiliate\RejectAffiliateAction;
use App\Actions\Affiliate\SuspendAffiliateAction;
use App\Actions\Affiliate\ActivateAffiliateAction;
use App\Enums\AffiliateStatus;
use App\Filament\Resources\AffiliateResource\Pages;
use App\Models\Affiliate;
use App\Support\Helper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;
use Illuminate\Support\Collection;

class AffiliateResource extends Resource
{
    protected static ?string $model = Affiliate::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Tiếp thị liên kết';

    protected static ?string $modelLabel = 'Tiếp thị liên kết';

    protected static ?int $navigationSort = 2;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns()
                    ->schema([
                        TextEntry::make('user.name')
                            ->label('Người dùng'),
                        TextEntry::make('referral_code')
                            ->label('Mã giới thiệu'),
                        TextEntry::make('tier.name')
                            ->label('Cấp bậc'),
                        TextEntry::make('tier.commission_rate')
                            ->label('Tỷ lệ hoa hồng')
                            ->formatStateUsing(fn(Affiliate $record): string => number_format((int) $record->tier->commission_rate, 0, ',', '.') . '%'),
                        TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge(),
                        TextEntry::make('created_at')
                            ->label('Tạo lúc')
                            ->dateTime(),
                    ]),
                Section::make()
                    ->columns()
                    ->schema([
                        TextEntry::make('total_commission')
                            ->label('Tổng hoa hồng')
                            ->formatStateUsing(fn(Affiliate $record): string => Helper::formatCurrency((int) $record->total_commission)),
                        TextEntry::make('available_commission')
                            ->label('Hoa hồng khả dụng')
                            ->formatStateUsing(fn(Affiliate $record): string => Helper::formatCurrency((int) $record->available_commission)),
                        TextEntry::make('total_clicks')
                            ->label('Tổng click')
                            ->formatStateUsing(fn(Affiliate $record): string => number_format((int) $record->total_clicks, 0, ',', '.')),
                        TextEntry::make('total_conversions')
                            ->label('Tổng conversion')
                            ->formatStateUsing(fn(Affiliate $record): string => number_format((int) $record->total_conversions, 0, ',', '.')),
                        TextEntry::make('conversion_rate')
                            ->label('Tỷ lệ chuyển đổi')
                            ->formatStateUsing(fn(Affiliate $record): string => number_format((int) $record->conversion_rate, 0, ',', '.') . '%'),
                        TextEntry::make('total_referrals')
                            ->label('Tổng giới thiệu')
                            ->formatStateUsing(fn(Affiliate $record): string => number_format((int) $record->total_referrals, 0, ',', '.')),
                        TextEntry::make('total_sales')
                            ->label('Tổng doanh số')
                            ->formatStateUsing(fn(Affiliate $record): string => Helper::formatCurrency((int) $record->total_sales)),
                    ]),
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin Affiliate')
                    ->schema([
                        Forms\Components\Select::make('user_id')
                            ->label('Người dùng')
                            ->relationship('user', 'name')
                            ->searchable()
                            ->required(),
                        Forms\Components\TextInput::make('referral_code')
                            ->label('Mã giới thiệu')
                            ->unique(ignoreRecord: true)
                            ->required()
                            ->maxLength(255),
                        Forms\Components\Select::make('tier_id')
                            ->label('Cấp bậc')
                            ->relationship('tier', 'name')
                            ->required(),
                        Forms\Components\ToggleButtons::make('status')
                            ->label('Trạng thái')
                            ->inline()
                            ->options(AffiliateStatus::class)
                            ->required(),
                        Forms\Components\Textarea::make('bio')
                            ->label('Mô tả')
                            ->rows(3)
                            ->columnSpanFull()
                            ->maxLength(1000),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.name')
                    ->label('Người dùng')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('referral_code')
                    ->label('Mã giới thiệu')
                    ->copyable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('tier.name')
                    ->label('Cấp bậc'),
                Tables\Columns\TextColumn::make('tier.commission_rate')
                    ->label('Tỷ lệ hoa hồng')
                    ->formatStateUsing(fn(Affiliate $record): string => number_format((int) $record->tier->commission_rate, 0, ',', '.') . '%'),
                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->label('Trạng thái')
                    ->color(fn(Affiliate $record): string => match ($record->status) {
                        AffiliateStatus::Pending => 'warning',
                        AffiliateStatus::Approved => 'success',
                        AffiliateStatus::Suspended => 'danger',
                        AffiliateStatus::Rejected => 'danger',
                        default => 'gray',
                    }),
                Tables\Columns\TextColumn::make('total_commission')
                    ->label('Tổng/Khả dụng')
                    ->formatStateUsing(function (Affiliate $record): string {
                        $total = Helper::formatCurrency((int) $record->total_commission);
                        $available = Helper::formatCurrency((int) $record->available_commission);
                        return "{$total} / {$available}";
                    })
                    ->tooltip('Tổng hoa hồng / Hoa hồng khả dụng')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(AffiliateStatus::class),
                Tables\Filters\SelectFilter::make('tier_id')
                    ->label('Cấp bậc')
                    ->relationship('tier', 'name'),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\Action::make('approve')
                        ->label('Duyệt')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn(Affiliate $record): bool => $record->status === AffiliateStatus::Pending)
                        ->action(function (Affiliate $record): void {
                            (new ApproveAffiliateAction())($record);
                        }),
                    Tables\Actions\Action::make('reject')
                        ->label('Từ chối')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->form([
                            Forms\Components\Textarea::make('rejection_reason')
                                ->label('Lý do từ chối')
                                ->required(),
                        ])
                        ->visible(fn(Affiliate $record): bool => $record->status === AffiliateStatus::Pending)
                        ->action(function (Affiliate $record, array $data): void {
                            (new RejectAffiliateAction())($record, $data);
                        }),
                    Tables\Actions\Action::make('suspend')
                        ->label('Tạm khóa')
                        ->icon('heroicon-o-pause')
                        ->color('warning')
                        ->requiresConfirmation()
                        ->visible(fn(Affiliate $record): bool => $record->status === AffiliateStatus::Approved)
                        ->action(function (Affiliate $record): void {
                            (new SuspendAffiliateAction())($record);
                        }),
                    Tables\Actions\Action::make('activate')
                        ->label('Kích hoạt')
                        ->icon('heroicon-o-play')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn(Affiliate $record): bool => $record->status === AffiliateStatus::Suspended)
                        ->action(function (Affiliate $record): void {
                            (new ActivateAffiliateAction())($record);
                        }),
                    Tables\Actions\EditAction::make(),
                    Tables\Actions\ViewAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('approve')
                        ->label('Duyệt hàng loạt')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (Collection $records): void {
                            $records->each(function (Affiliate $record) {
                                if ($record->status === AffiliateStatus::Pending) {
                                    (new ApproveAffiliateAction())($record);
                                }
                            });
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getNavigationBadge(): ?string
    {
        $count = Affiliate::query()
            ->where('status', AffiliateStatus::Pending)
            ->count();

        return $count > 0 ? number_format($count) : null;
    }

    public static function getNavigationBadgeColor(): string|null
    {
        return 'warning';
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAffiliates::route('/'),
            'create' => Pages\CreateAffiliate::route('/create'),
            'edit' => Pages\EditAffiliate::route('/{record}/edit'),
            'view' => Pages\ViewAffiliate::route('/{record}'),
        ];
    }
}

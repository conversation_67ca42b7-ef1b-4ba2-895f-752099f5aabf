<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\DiscountScope;
use App\Enums\DiscountType;
use App\Filament\Resources\DiscountResource\Pages;
use App\Models\Account;
use App\Models\Discount;
use App\Support\Helper;
use App\Tables\Columns\DateTimeColumn;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class DiscountResource extends Resource
{
    protected static ?string $model = Discount::class;

    protected static ?string $navigationIcon = 'heroicon-o-tag';

    protected static ?string $modelLabel = 'Mã Giảm Giá';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 6;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('code')
                            ->label('Mã')
                            ->required()
                            ->columnSpanFull()
                            ->suffixAction(
                                Forms\Components\Actions\Action::make('generate')
                                    ->label(__('Generate'))
                                    ->icon('heroicon-o-arrow-path')
                                    ->action(fn(Forms\Set $set): mixed => $set('code', Str::upper(Str::random(10)))),
                            ),
                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\Select::make('type')
                                    ->label('Loại')
                                    ->required()
                                    ->options(DiscountType::class)
                                    ->default(DiscountType::Percentage)
                                    ->afterStateUpdated(fn(Forms\Set $set): mixed => $set('value', null))
                                    ->reactive(),
                                Forms\Components\TextInput::make('value')
                                    ->label(fn(Forms\Get $get): string => DiscountType::parse($get('type'))->getLabel())
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->maxValue(fn(Forms\Get $get): int => match (DiscountType::parse($get('type'))) {
                                        DiscountType::Percentage => 100,
                                        DiscountType::Fixed => 100000000,
                                    })
                                    ->suffix(fn(Forms\Get $get): string => match (DiscountType::parse($get('type'))) {
                                        DiscountType::Percentage => '%',
                                        DiscountType::Fixed => '$',
                                    })
                                    ->inlineSuffix()
                                    ->default(0),
                            ]),
                        Forms\Components\Checkbox::make('is_unlimited')
                            ->label('Không giới hạn số lượng')
                            ->default(true)
                            ->reactive(),
                        Forms\Components\TextInput::make('limit')
                            ->label('Số lượng')
                            ->required(fn(Forms\Get $get): bool => ! $get('is_unlimited'))
                            ->hidden(fn(Forms\Get $get): mixed => $get('is_unlimited'))
                            ->numeric(),
                    ]),
                Forms\Components\Section::make(__('Applies to'))
                    ->heading('Áp dụng cho')
                    ->schema([
                        Forms\Components\Radio::make('applied_to')
                            ->hiddenLabel()
                            ->options(DiscountScope::class)
                            ->default(DiscountScope::All)
                            ->inline()
                            ->inlineLabel(false)
                            ->required()
                            ->enum(DiscountScope::class)
                            ->reactive(),
                        Forms\Components\Select::make('categories')
                            ->label('Danh mục')
                            ->required()
                            ->hidden(fn(Forms\Get $get): bool => DiscountScope::parse($get('applied_to')) !== DiscountScope::Category)
                            ->multiple()
                            ->relationship('categories', 'name'),
                        Forms\Components\Select::make('accounts')
                            ->label('Tài khoản')
                            ->required()
                            ->hidden(fn(Forms\Get $get): bool => DiscountScope::parse($get('applied_to')) !== DiscountScope::Account)
                            ->multiple()
                            ->relationship(
                                name: 'accounts',
                                modifyQueryUsing: fn(Builder $query) => $query->leftJoin('account_categories as category', 'category.id', '=', 'accounts.category_id'),
                            )
                            ->getOptionLabelFromRecordUsing(fn(Account $account): string => sprintf('Tài khoản %s #%s', $account->category->name, $account->id))
                            ->searchable(['accounts.id', 'category.name']),
                        Forms\Components\Select::make('users')
                            ->label('Người dùng')
                            ->required()
                            ->hidden(fn(Forms\Get $get): bool => DiscountScope::parse($get('applied_to')) !== DiscountScope::User)
                            ->multiple()
                            ->relationship('users', 'name')
                            ->searchable(['users.id', 'users.name']),
                    ]),
                Forms\Components\Section::make('Thời gian')
                    ->schema([
                        Forms\Components\Grid::make()
                            ->schema([
                                Forms\Components\DatePicker::make('start_date')
                                    ->label('Ngày bắt đầu')
                                    ->required()
                                    ->date()
                                    ->default(now()),
                                Forms\Components\TimePicker::make('start_time')
                                    ->label('Giờ bắt đầu')
                                    ->seconds(false)
                                    ->required()
                                    ->time()
                                    ->default(now()),
                            ]),
                        Forms\Components\Checkbox::make('is_permanent')
                            ->label('Không giới hạn thời gian')
                            ->default(true)
                            ->reactive(),
                        Forms\Components\Grid::make()
                            ->hidden(fn(Forms\Get $get): mixed => $get('is_permanent'))
                            ->schema([
                                Forms\Components\DatePicker::make('end_date')
                                    ->label('Ngày kết thúc')
                                    ->required(fn(Forms\Get $get): bool => ! $get('is_permanent'))
                                    ->date()
                                    ->maxDate(fn(Forms\Get $get) => Carbon::parse($get('start_date'))->addYear())
                                    ->minDate(fn(Forms\Get $get): mixed => $get('start_date')),
                                Forms\Components\TimePicker::make('end_time')
                                    ->label('Giờ kết thúc')
                                    ->seconds(false)
                                    ->required(fn(Forms\Get $get): bool => ! $get('is_permanent'))
                                    ->time(),
                            ]),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('code')
                    ->label('Mã')
                    ->description(function (Discount $discount): string {
                        $value = match ($discount->type) {
                            DiscountType::Percentage => sprintf('%s%%', $discount->value),
                            DiscountType::Fixed => Helper::formatCurrency($discount->value),
                        };

                        $appliedTo = match ($discount->applied_to) {
                            DiscountScope::All => 'tất cả',
                            DiscountScope::Category => $discount->categories->count(),
                            DiscountScope::Account => $discount->accounts->count(),
                            DiscountScope::User => $discount->users->count(),
                        };

                        return sprintf('Giảm %s cho %s %s', $value, $appliedTo, $discount->applied_to->getLabel());
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('usage')
                    ->label('Số lần sử dụng')
                    ->numeric()
                    ->formatStateUsing(fn(Discount $discount): string => __(':count/:limit', [
                        'count' => number_format($discount->usage),
                        'limit' => $discount->is_unlimited ? '∞' : number_format($discount->limit),
                    ]))
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge()
                    ->color(fn(Discount $discount): string => match (true) {
                        $discount->is_expired => 'danger',
                        $discount->is_upcoming => 'warning',
                        default => 'success',
                    }),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Kích hoạt'),
                DateTimeColumn::make('created_at')
                    ->label('Ngày tạo'),
                DateTimeColumn::make('updated_at')
                    ->label('Ngày cập nhật'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDiscounts::route('/'),
            'create' => Pages\CreateDiscount::route('/create'),
            'edit' => Pages\EditDiscount::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

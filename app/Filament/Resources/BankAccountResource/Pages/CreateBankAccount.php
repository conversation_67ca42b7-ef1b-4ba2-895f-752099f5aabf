<?php

declare(strict_types=1);

namespace App\Filament\Resources\BankAccountResource\Pages;

use App\Filament\Resources\BankAccountResource;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Support\Facades\Auth;

class CreateBankAccount extends CreateRecord
{
    protected static string $resource = BankAccountResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $user = Auth::user();

        if ($user->hasRole('super_admin')) {
            return parent::mutateFormDataBeforeCreate($data);
        }

        $data['owner_type'] = $user::class;
        $data['owner_id'] = $user->getKey();

        return $data;
    }
}

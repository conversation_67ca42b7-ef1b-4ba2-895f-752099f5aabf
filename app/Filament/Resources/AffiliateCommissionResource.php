<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Actions\Affiliate\ApproveAffiliateCommissionAction;
use App\Actions\Affiliate\RejectAffiliateCommissionAction;
use App\Enums\AffiliateCommissionStatus;
use App\Filament\Resources\AffiliateCommissionResource\Pages;
use App\Models\AffiliateCommission;
use App\Support\Helper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Table;

class AffiliateCommissionResource extends Resource
{
    protected static ?string $model = AffiliateCommission::class;

    protected static ?string $navigationIcon = 'heroicon-o-currency-dollar';

    protected static ?string $navigationGroup = 'Tiếp thị liên kết';

    protected static ?string $modelLabel = 'Hoa hồng';

    protected static ?int $navigationSort = 3;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns()
                    ->schema([
                        TextEntry::make('affiliate.user.name')
                            ->label('Người giới thiệu'),
                        TextEntry::make('referral_code')
                            ->label('Mã giới thiệu'),
                        TextEntry::make('purchased_account_id')
                            ->label('Mã đơn hàng'),
                        TextEntry::make('order_amount')
                            ->label('Số tiền đơn hàng')
                            ->formatStateUsing(fn(AffiliateCommission $record): string => Helper::formatCurrency((int) $record->order_amount)),
                        TextEntry::make('commission_rate')
                            ->label('Tỷ lệ hoa hồng')
                            ->formatStateUsing(fn(AffiliateCommission $record): string => $record->commission_rate . '%'),
                        TextEntry::make('commission_amount')
                            ->label('Số tiền hoa hồng')
                            ->formatStateUsing(fn(AffiliateCommission $record): string => Helper::formatCurrency((int) $record->commission_amount)),
                        TextEntry::make('status')
                            ->label('Trạng thái')
                            ->badge(),
                        TextEntry::make('created_at')
                            ->label('Tạo lúc')
                            ->dateTime(),
                        TextEntry::make('approved_at')
                            ->label('Duyệt lúc')
                            ->visible(fn(AffiliateCommission $record): bool => $record->status === AffiliateCommissionStatus::Approved)
                            ->dateTime(),
                        TextEntry::make('description')
                            ->label('Mô tả'),
                    ]),
            ]);
    }

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin Hoa hồng')
                    ->schema([
                        Forms\Components\Select::make('affiliate_id')
                            ->label('Người giới thiệu')
                            ->relationship('affiliate.user', 'username')
                            ->searchable()
                            ->required(),
                        Forms\Components\TextInput::make('order_amount')
                            ->label('Số tiền đơn hàng')
                            ->numeric()
                            ->required()
                            ->minValue(0)
                            ->step(1000),
                        Forms\Components\TextInput::make('commission_rate')
                            ->label('Tỷ lệ hoa hồng (%)')
                            ->numeric()
                            ->required()
                            ->minValue(0)
                            ->maxValue(100)
                            ->step(0.01),
                        Forms\Components\TextInput::make('commission_amount')
                            ->label('Số tiền hoa hồng')
                            ->numeric()
                            ->required()
                            ->minValue(0)
                            ->step(1000),
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(2)
                            ->columnSpanFull()
                            ->maxLength(1000),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('affiliate.user.name')
                    ->label('Người giới thiệu')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('order_amount')
                    ->label('Số tiền đơn hàng')
                    ->formatStateUsing(fn(AffiliateCommission $record): string => Helper::formatCurrency((int) $record->order_amount))
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->label('Tỷ lệ')
                    ->formatStateUsing(fn(AffiliateCommission $record): string => $record->commission_rate . '%'),
                Tables\Columns\TextColumn::make('commission_amount')
                    ->label('Hoa hồng')
                    ->formatStateUsing(fn(AffiliateCommission $record): string => Helper::formatCurrency((int) $record->commission_amount))
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->badge(),
                Tables\Columns\TextColumn::make('approved_at')
                    ->label('Duyệt lúc')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(AffiliateCommissionStatus::class),
                Tables\Filters\SelectFilter::make('affiliate_id')
                    ->label('Affiliate')
                    ->relationship('affiliate.user', 'username'),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\Action::make('approve')
                        ->label('Duyệt')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->visible(fn(AffiliateCommission $record): bool => $record->status === AffiliateCommissionStatus::Pending)
                        ->action(function (AffiliateCommission $record): void {
                            app(ApproveAffiliateCommissionAction::class)($record);
                        }),
                    Tables\Actions\Action::make('reject')
                        ->label('Từ chối')
                        ->icon('heroicon-o-x-mark')
                        ->color('danger')
                        ->requiresConfirmation()
                        ->visible(fn(AffiliateCommission $record): bool => $record->status === AffiliateCommissionStatus::Pending)
                        ->form([
                            Forms\Components\Textarea::make('reason')
                                ->label('Lý do từ chối')
                                ->required()
                                ->rows(3),
                        ])
                        ->action(function (AffiliateCommission $record, array $data): void {
                            app(RejectAffiliateCommissionAction::class)($record, $data['reason']);
                        }),
                    Tables\Actions\ViewAction::make(),
                    Tables\Actions\EditAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\BulkAction::make('approve')
                        ->label('Duyệt hàng loạt')
                        ->icon('heroicon-o-check')
                        ->color('success')
                        ->requiresConfirmation()
                        ->action(function (\Illuminate\Support\Collection $records): void {
                            $records->each(function (AffiliateCommission $record) {
                                if ($record->status === AffiliateCommissionStatus::Pending) {
                                    app(ApproveAffiliateCommissionAction::class)($record);
                                }
                            });
                        }),
                ]),
            ])
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAffiliateCommissions::route('/'),
            'edit' => Pages\EditAffiliateCommission::route('/{record}/edit'),
            'view' => Pages\ViewAffiliateCommission::route('/{record}/view'),
        ];
    }

    public static function getNavigationBadge(): ?string
    {
        $count = static::getModel()::where('status', AffiliateCommissionStatus::Pending)->count();

        return $count > 0 ? number_format($count) : null;
    }

    public static function canCreate(): bool
    {
        return false;
    }

    public static function getNavigationBadgeColor(): string|null
    {
        return 'warning';
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\PurchasedAccountResource\Pages;
use App\Models\PurchasedAccount;
use App\Support\Helper;
use App\Tables\Columns\DateTimeColumn;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class PurchasedAccountResource extends Resource
{
    protected static ?string $model = PurchasedAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Tài Khoản Đã Bán';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 7;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->columns(3)
                    ->schema([
                        TextEntry::make('user.display_name')
                            ->label('Người mua')
                            ->url(fn(PurchasedAccount $account): string => UserResource::getUrl('edit', [$account->user_id])),
                        TextEntry::make('account.user.display_name')
                            ->label('Người bán')
                            ->url(fn(PurchasedAccount $account): string => UserResource::getUrl('edit', [$account->account->user_id])),
                        TextEntry::make('account.category.name')
                            ->label('Loại game')
                            ->url(fn(PurchasedAccount $account): string => AccountCategoryResource::getUrl('edit', [$account->account->category_id])),
                        TextEntry::make('account_id')
                            ->label('ID tài khoản')
                            ->formatStateUsing(fn(PurchasedAccount $account): string => "#$account->account_id")
                            ->url(fn(PurchasedAccount $account): string => AccountResource::getUrl('edit', [$account->account_id])),
                        TextEntry::make('price')
                            ->label('Giá bán')
                            ->money(),
                        TextEntry::make('discount_amount')
                            ->label('Giảm giá')
                            ->visible(fn(PurchasedAccount $account): bool => $account->discount_amount > 0)
                            ->formatStateUsing(fn(PurchasedAccount $account): string => sprintf('%s (%s)', Helper::formatCurrency($account->discount_amount), $account->discount_code)),
                        TextEntry::make('total')
                            ->label('Tổng tiền')
                            ->money(),
                        TextEntry::make('created_at')
                            ->label('Thời gian mua'),
                        TextEntry::make('seen_at')
                            ->label('Đã xem lúc'),
                    ]),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('user.display_name')
                    ->label('Người mua')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('account.category.name')
                    ->label('Loại game')
                    ->formatStateUsing(fn(PurchasedAccount $account): string => "{$account->account->category->name} #{$account->account_id}")
                    ->sortable(),
                Tables\Columns\TextColumn::make('total')
                    ->label('Tổng tiền')
                    ->numeric()
                    ->sortable(),
                DateTimeColumn::make('created_at')
                    ->toggleable(false)
                    ->label('Thời gian'),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListPurchasedAccounts::route('/'),
            'view' => Pages\ViewPurchasedAccount::route('/{record}'),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources\WithdrawalResource\Pages;

use App\Enums\TransactionType;
use App\Enums\WithdrawalStatus;
use App\Filament\Resources\WithdrawalResource;
use App\Models\User;
use App\Support\Helper;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class CreateWithdrawal extends CreateRecord
{
    protected static string $resource = WithdrawalResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $user = Auth::user();

        if ($user->hasRole('super_admin')) {
            return parent::mutateFormDataBeforeCreate($data);
        }

        if ($user->balance < $data['amount']) {
            Notification::make()
                ->title('Số dư không đủ')
                ->body('Số dư khả dụng: ' . Helper::formatCurrency((int) $user->available_commission))
                ->danger()
                ->send();

            $this->halt();
        }

        if ($data['amount'] < 100000) {
            Notification::make()
                ->title('Số tiền rút tối thiểu là 100,000đ.')
                ->danger()
                ->send();

            $this->halt();
        }

        return $data;
    }

    protected function handleRecordCreation(array $data): Model
    {
        $user = Auth::user();

        $data['withdrawable_type'] = User::class;
        $data['withdrawable_id'] = $user->getKey();
        $data['status'] = WithdrawalStatus::Pending;

        $user->decrement('balance', (int) $data['amount']);

        $withdrawal = parent::handleRecordCreation($data);

        $user->recordTransaction(
            TransactionType::Withdrawal,
            $data['amount'],
            $withdrawal,
            'Tạo yêu cầu rút tiền #' . $withdrawal->getKey(),
        );

        return $withdrawal;
    }
}

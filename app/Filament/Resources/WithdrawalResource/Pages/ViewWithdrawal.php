<?php

declare(strict_types=1);

namespace App\Filament\Resources\WithdrawalResource\Pages;

use App\Actions\ApproveWithdrawalAction;
use App\Actions\CancelWithdrawalAction;
use App\Enums\WithdrawalStatus;
use App\Actions\RejectWithdrawalAction;
use App\Filament\Resources\WithdrawalResource;
use Filament\Actions\Action;
use Filament\Resources\Pages\ViewRecord;
use Filament\Notifications\Notification;
use Filament\Forms;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;

class ViewWithdrawal extends ViewRecord
{
    protected static string $resource = WithdrawalResource::class;

    protected function getHeaderActions(): array
    {
        if (! Auth::user()->hasRole('super_admin')) {
            return [
                Action::make('cancel')
                    ->label('Hủy')
                    ->icon('heroicon-o-x-circle')
                    ->color('danger')
                    ->visible(fn(): bool => $this->record->status === WithdrawalStatus::Pending)
                    ->requiresConfirmation()
                    ->modalHeading('Hủy yêu cầu rút tiền')
                    ->modalDescription('Bạn có chắc chắn muốn hủy yêu cầu rút tiền này?')
                    ->action(function (): void {
                        (new CancelWithdrawalAction())($this->record);

                        Notification::make()
                            ->title('Đã hủy yêu cầu rút tiền')
                            ->success()
                            ->send();
                    }),
            ];
        }

        return [
            Action::make('approve')
                ->label('Duyệt')
                ->icon('heroicon-o-check-circle')
                ->color('success')
                ->visible(fn(): bool => $this->record->status === WithdrawalStatus::Pending)
                ->requiresConfirmation()
                ->modalHeading('Duyệt yêu cầu rút tiền')
                ->modalDescription('Bạn có chắc chắn muốn duyệt yêu cầu rút tiền này?')
                ->action(function (): void {
                    (new ApproveWithdrawalAction())($this->record);

                    Notification::make()
                        ->title('Đã duyệt yêu cầu rút tiền')
                        ->success()
                        ->send();
                }),

            Action::make('reject')
                ->label('Từ chối')
                ->icon('heroicon-o-x-circle')
                ->color('danger')
                ->visible(fn(): bool => $this->record->status === WithdrawalStatus::Pending)
                ->modalWidth(MaxWidth::Medium)
                ->form([
                    Forms\Components\Textarea::make('rejection_reason')
                        ->label('Lý do từ chối')
                        ->required()
                        ->maxLength(1000),
                ])
                ->action(function (array $data): void {
                    (new RejectWithdrawalAction())($this->record, $data['rejection_reason']);

                    Notification::make()
                        ->title('Đã từ chối yêu cầu rút tiền')
                        ->success()
                        ->send();
                }),
        ];
    }
}

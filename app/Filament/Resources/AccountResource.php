<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\AccountStatus;
use App\Filament\Resources\AccountResource\Pages;
use App\Forms\Components\CurrencyInput;
use App\Models\Account;
use App\Models\AttributeSet;
use App\Models\User;
use App\Tables\Columns\DateTimeColumn;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Closure;
use CodeWithDennis\FilamentSelectTree\SelectTree;
use Illuminate\Support\Facades\Auth;

class AccountResource extends Resource
{
    protected static ?string $model = Account::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Tài khoản game';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        $isSuperAdmin = Auth::user()->hasRole('super_admin');

        return $form
            ->disabled(fn(Account $account): bool => $account->status === AccountStatus::Sold)
            ->schema([
                Forms\Components\Group::make()
                    ->columnSpan(fn() => $isSuperAdmin ? 2 : 3)
                    ->schema([
                        Section::make()
                            ->heading('Thông tin cơ bản')
                            ->columns()
                            ->schema([
                                Forms\Components\Select::make('user_id')
                                    ->label('Người bán')
                                    ->searchable()
                                    ->visible(fn() => $isSuperAdmin)
                                    ->relationship('user', 'username')
                                    ->default(Auth::id())
                                    ->required(),
                                SelectTree::make('category_id')
                                    ->label('Danh mục game')
                                    ->relationship('category', 'name', 'parent_id')
                                    ->searchable()
                                    ->withCount()
                                    ->enableBranchNode()
                                    ->disabled(fn(Account $account) => ! $isSuperAdmin && $account->exists)
                                    ->afterStateUpdated(function (SelectTree $component): void {
                                        $component
                                            ->getContainer()
                                            ->getParentComponent()
                                            ->getContainer()
                                            ->getComponent('attributes')
                                            ?->getChildComponentContainer()
                                            ->fill();
                                    })
                                    ->required(),
                                Forms\Components\ToggleButtons::make('bulk_mode')
                                    ->label('Chế độ nhập')
                                    ->options([
                                        'single' => 'Nhập từng tài khoản',
                                        'bulk' => 'Nhập nhiều tài khoản',
                                    ])
                                    ->default('single')
                                    ->inline()
                                    ->live()
                                    ->hiddenOn('edit'),
                            ]),

                        Section::make()
                            ->heading('Thông tin tài khoản')
                            ->visible(fn(Get $get): bool => $get('bulk_mode') === 'single' || ! $get('bulk_mode'))
                            ->columns()
                            ->schema([
                                Forms\Components\TextInput::make('acc_name')
                                    ->label('Tên tài khoản')
                                    ->required(),
                                Forms\Components\TextInput::make('acc_pass')
                                    ->label('Mật khẩu')
                                    ->nullable(),
                            ]),

                        Section::make()
                            ->heading('Nhập nhiều tài khoản')
                            ->description('Nhập danh sách tài khoản theo format: taikhoan|matkhau (mỗi dòng một tài khoản)')
                            ->visible(fn(Get $get): bool => $get('bulk_mode') === 'bulk')
                            ->schema([
                                Forms\Components\Textarea::make('bulk_accounts')
                                    ->label('Danh sách tài khoản')
                                    ->placeholder("taikhoan1|matkhau1\ntaikhoan2|matkhau2\ntaikhoan3|matkhau3")
                                    ->rows(10)
                                    ->helperText('Mỗi dòng một tài khoản theo format: taikhoan|matkhau')
                                    ->required()
                                    ->rules([
                                        'required',
                                        fn(): Closure => function (string $attribute, $value, Closure $fail) {
                                            if (empty($value)) {
                                                return;
                                            }

                                            $lines = array_filter(array_map('trim', explode("\n", $value)));
                                            $validLines = 0;

                                            foreach ($lines as $line) {
                                                $parts = explode('|', $line, 2);
                                                if (count($parts) >= 1 && !empty(trim($parts[0]))) {
                                                    $validLines++;
                                                }
                                            }

                                            if ($validLines === 0) {
                                                $fail('Ít nhất phải có một tài khoản hợp lệ.');
                                            }
                                        },
                                    ]),
                            ]),

                        Section::make()
                            ->heading('Thông tin chung')
                            ->columns()
                            ->schema([
                                Forms\Components\Textarea::make('description')
                                    ->label('Mô tả ngắn')
                                    ->columnSpanFull(),
                                Forms\Components\RichEditor::make('content')
                                    ->label('Nội dung')
                                    ->columnSpanFull(),
                                CurrencyInput::make('price')
                                    ->label('Giá bán')
                                    ->required()
                                    ->minValue(0),
                                CurrencyInput::make('compare_at_price')
                                    ->label('Giá so sánh')
                                    ->helperText('Để hiển thị giá so sánh, hãy nhập giá gốc vào đây và nhập giá thấp hơn vào ô "Giá bán".')
                                    ->minValue(0),
                            ]),

                        Section::make()
                            ->heading('Hình ảnh')
                            ->columns()
                            ->collapsible()
                            ->persistCollapsed()
                            ->schema([
                                Forms\Components\FileUpload::make('images')
                                    ->hiddenLabel()
                                    ->image()
                                    ->multiple()
                                    ->reorderable()
                                    ->directory('accounts')
                                    ->appendFiles()
                                    ->columnSpanFull(),
                            ]),

                        Section::make()
                            ->heading('Thuộc tính')
                            ->collapsible()
                            ->persistCollapsed()
                            ->columns()
                            ->key('attributes')
                            ->hidden(function (Get $get): bool {
                                if (! $get('category_id')) {
                                    return true;
                                }

                                return ! AttributeSet::query()
                                    ->where('category_id', $get('category_id'))
                                    ->exists();
                            })
                            ->schema(function (Get $get): array {
                                if (! $get('category_id')) {
                                    return [];
                                }

                                $schema = [];

                                $attributeSets = AttributeSet::query()
                                    ->where('category_id', $get('category_id'))
                                    ->with('attributes')
                                    ->get();

                                foreach ($attributeSets as $attributeSet) {
                                    $schema[] = Forms\Components\Select::make("attributes.{$attributeSet->getKey()}")
                                        ->label($attributeSet->name)
                                        ->options($attributeSet->attributes->pluck('name', 'id'))
                                        ->required($attributeSet->is_required);
                                }

                                return $schema;
                            }),
                    ]),
                Forms\Components\Group::make()
                    ->visible(fn() => $isSuperAdmin)
                    ->columnSpan(1)
                    ->schema([
                        Section::make()
                            ->schema([
                                Forms\Components\ToggleButtons::make('status')
                                    ->label('Trạng thái')
                                    ->required()
                                    ->inline()
                                    ->disabled(fn(Account $account): bool => $account->status === AccountStatus::Sold)
                                    ->options(AccountStatus::class)
                                    ->default(AccountStatus::Pending),
                            ]),
                    ]),
            ])
            ->columns(3);
    }

    /**
     * @throws Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->modifyQueryUsing(function (Builder $query): void {})
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->url(fn(Account $account) => route('accounts.show', $account), true)
                    ->sortable(),
                Tables\Columns\ImageColumn::make('image')
                    ->label('Hình ảnh')
                    ->width(60),
                Tables\Columns\TextColumn::make('user.username')
                    ->label('Người bán')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('category.name')
                    ->label('Danh mục game')
                    ->numeric()
                    ->sortable(),
                Tables\Columns\TextColumn::make('acc_name')
                    ->label('Tên tài khoản')
                    ->searchable(),
                Tables\Columns\TextColumn::make('price')
                    ->label('Giá bán')
                    ->money()
                    ->sortable(),
                Tables\Columns\TextColumn::make('status')
                    ->label('Trạng thái')
                    ->sortable()
                    ->badge(),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                SelectFilter::make('status')
                    ->label('Trạng thái')
                    ->options(AccountStatus::class)
                    ->multiple(),
                SelectFilter::make('user')
                    ->label('Người bán')
                    ->options(
                        fn() => User::query()
                            ->select(['id', 'name', 'username'])
                            ->get()
                            ->pluck('display_name', 'id')
                            ->all(),
                    )
                    ->searchable()
                    ->preload(),
                SelectFilter::make('category')
                    ->label('Danh mục game')
                    ->relationship('category', 'name'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function canDelete(Model $record): bool
    {
        /** @var Account $record */

        return $record->status !== AccountStatus::Sold;
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAccounts::route('/'),
            'create' => Pages\CreateAccount::route('/create'),
            'edit' => Pages\EditAccount::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->when(! Auth::user()->hasRole('super_admin'), function (Builder $query): void {
                $query->where('user_id', Auth::id());
            })
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

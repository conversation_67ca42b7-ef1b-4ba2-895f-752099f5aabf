<?php

declare(strict_types=1);

namespace App\Filament\Resources\AffiliateCommissionResource\Pages;

use App\Actions\Affiliate\ApproveAffiliateCommissionAction;
use App\Actions\Affiliate\RejectAffiliateCommissionAction;
use App\Enums\AffiliateCommissionStatus;
use App\Filament\Resources\AffiliateCommissionResource;
use App\Models\AffiliateCommission;
use Filament\Actions;
use Filament\Forms;
use Filament\Resources\Pages\EditRecord;

class EditAffiliateCommission extends EditRecord
{
    protected static string $resource = AffiliateCommissionResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('approve')
                ->label('Duyệt')
                ->icon('heroicon-o-check')
                ->color('success')
                ->requiresConfirmation()
                ->visible(fn(AffiliateCommission $record): bool => $record->status === AffiliateCommissionStatus::Pending)
                ->action(function (AffiliateCommission $record): void {
                    app(ApproveAffiliateCommissionAction::class)($record);
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
            Actions\Action::make('reject')
                ->label('Từ chối')
                ->icon('heroicon-o-x-mark')
                ->color('danger')
                ->requiresConfirmation()
                ->visible(fn(AffiliateCommission $record): bool => $record->status === AffiliateCommissionStatus::Pending)
                ->form([
                    Forms\Components\Textarea::make('reason')
                        ->label('Lý do từ chối')
                        ->required()
                        ->rows(3),
                ])
                ->action(function (AffiliateCommission $record, array $data): void {
                    app(RejectAffiliateCommissionAction::class)($record, $data['reason']);
                    $this->redirect($this->getResource()::getUrl('index'));
                }),
        ];
    }

    protected function mutateFormDataBeforeSave(array $data): array
    {

        return $data;
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources\GameResource\Pages;

use App\Filament\Resources\AccountCategoryResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAccountCategory extends ListRecords
{
    protected static string $resource = AccountCategoryResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}

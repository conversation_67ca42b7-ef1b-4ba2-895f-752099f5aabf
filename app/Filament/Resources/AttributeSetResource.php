<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\AttributeType;
use App\Filament\Resources\AttributeSetResource\Pages;
use App\Filament\Resources\AttributeSetResource\RelationManagers\AttributesRelationManager;
use App\Models\AttributeSet;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\ActionGroup;
use Filament\Tables\Grouping\Group;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class AttributeSetResource extends Resource
{
    protected static ?string $model = AttributeSet::class;

    protected static ?string $navigationIcon = 'heroicon-o-list-bullet';

    protected static ?string $modelLabel = 'Thuộc tính game';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('category_id')
                            ->label('Danh mục')
                            ->relationship('category', 'name')
                            ->columnSpanFull()
                            ->required(),
                        Forms\Components\TextInput::make('name')
                            ->label('Tên thuộc tính')
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn(Forms\Set $set, ?string $state): mixed => $set('slug', Str::slug($state)))
                            ->required(),
                        Forms\Components\TextInput::make('slug')
                            ->required(),
                        Forms\Components\Select::make('type')
                            ->label('Loại thuộc tính')
                            ->options(AttributeType::class)
                            ->default(AttributeType::Dropdown)
                            ->required()
                            ->columnSpanFull(),
                        Forms\Components\Toggle::make('is_required')
                            ->label('Bắt buộc'),
                        Forms\Components\Toggle::make('is_visible')
                            ->label('Hiển thị')
                            ->default(true),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->groups([
                Group::make('category.name')
                    ->collapsible(),
            ])
            ->defaultGroup('category.name')
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên thuộc tính')
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Loại')
                    ->badge(),
                Tables\Columns\ToggleColumn::make('is_required')
                    ->label('Bắt buộc'),
                Tables\Columns\ToggleColumn::make('is_visible')
                    ->label('Hiển thị'),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            AttributesRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAttributeSets::route('/'),
            'edit' => Pages\EditAttributeSet::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

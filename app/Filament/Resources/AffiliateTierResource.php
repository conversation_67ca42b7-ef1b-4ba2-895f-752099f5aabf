<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AffiliateTierResource\Pages;
use App\Models\AffiliateTier;
use App\Support\Helper;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;

class AffiliateTierResource extends Resource
{
    protected static ?string $model = AffiliateTier::class;

    protected static ?string $navigationIcon = 'heroicon-o-trophy';

    protected static ?string $navigationGroup = 'Tiếp thị liên kết';

    protected static ?string $modelLabel = 'Cấp bậc hoa hồng';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make('Thông tin cấp bậc')
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên cấp bậc')
                            ->required()
                            ->maxLength(255),
                        Forms\Components\TextInput::make('slug')
                            ->label('Slug')
                            ->required()
                            ->unique(ignoreRecord: true)
                            ->maxLength(255),
                        Forms\Components\TextInput::make('commission_rate')
                            ->label('Tỷ lệ hoa hồng (%)')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->maxValue(100)
                            ->step(0.01),
                        Forms\Components\TextInput::make('min_commission_required')
                            ->label('Hoa hồng tối thiểu để lên cấp (VNĐ)')
                            ->required()
                            ->numeric()
                            ->minValue(0)
                            ->step(1000),
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->rows(3)
                            ->maxLength(1000),
                        Forms\Components\ColorPicker::make('color')
                            ->label('Màu hiển thị')
                            ->default('#6B7280'),
                        Forms\Components\Toggle::make('is_default')
                            ->label('Cấp mặc định')
                            ->helperText('Cấp bậc mặc định cho affiliate mới'),
                        Forms\Components\TextInput::make('sort_order')
                            ->label('Thứ tự hiển thị')
                            ->numeric()
                            ->default(0),
                    ])
                    ->columns(2),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->searchable()
                    ->sortable(),
                Tables\Columns\TextColumn::make('commission_rate')
                    ->label('Tỷ lệ hoa hồng')
                    ->formatStateUsing(fn(AffiliateTier $record): string => (float) $record->commission_rate . '%')
                    ->sortable(),
                Tables\Columns\TextColumn::make('min_commission_required')
                    ->label('Hoa hồng tối thiểu')
                    ->formatStateUsing(fn(AffiliateTier $record): string => Helper::formatCurrency((float) $record->min_commission_required))
                    ->sortable(),
                Tables\Columns\TextColumn::make('affiliates_count')
                    ->label('Số affiliate')
                    ->counts('affiliates')
                    ->sortable(),
                Tables\Columns\TextColumn::make('created_at')
                    ->label('Tạo lúc')
                    ->dateTime()
                    ->sortable(),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('is_default')
                    ->label('Cấp mặc định'),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->defaultSort('sort_order')
            ->defaultSort('created_at', 'desc');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAffiliateTiers::route('/'),
            'create' => Pages\CreateAffiliateTier::route('/create'),
            'edit' => Pages\EditAffiliateTier::route('/{record}/edit'),
        ];
    }
}

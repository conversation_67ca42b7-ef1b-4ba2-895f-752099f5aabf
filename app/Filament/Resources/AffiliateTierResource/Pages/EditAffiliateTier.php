<?php

declare(strict_types=1);

namespace App\Filament\Resources\AffiliateTierResource\Pages;

use App\Filament\Resources\AffiliateTierResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAffiliateTier extends EditRecord
{
    protected static string $resource = AffiliateTierResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\Bank;
use App\Filament\Resources\BankAccountResource\Pages;
use App\Models\BankAccount;
use App\Models\User;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

class BankAccountResource extends Resource
{
    protected static ?string $model = BankAccount::class;

    protected static ?string $navigationIcon = 'heroicon-o-building-library';

    protected static ?string $modelLabel = 'Tài khoản ngân hàng';

    protected static ?string $navigationGroup = 'Quản lý Tài chính';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\Section::make()
                    ->schema([
                        Forms\Components\Select::make('bank_name')
                            ->label('Ngân hàng')
                            ->options(Bank::class)
                            ->searchable()
                            ->required(),
                        Forms\Components\TextInput::make('name')
                            ->label('Tên gợi nhớ')
                            ->helperText('Tên gợi nhớ giúp bạn nhận biết tài khoản này')
                            ->required(),
                        Forms\Components\TextInput::make('account_number')
                            ->label('Số tài khoản')
                            ->required(),
                        Forms\Components\TextInput::make('account_holder')
                            ->label('Chủ tài khoản')
                            ->required(),
                        Forms\Components\Toggle::make('is_visible')
                            ->label('Hiển thị')
                            ->default(true)
                            ->visible(fn() => Auth::user()->hasRole('super_admin'))
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('logo')
                            ->columnSpanFull()
                            ->image()
                            ->visible(fn() => Auth::user()->hasRole('super_admin'))
                            ->directory('banks')
                            ->label('Logo'),
                    ])
                    ->columns(),
                Forms\Components\Section::make('Sieuthicode API')
                    ->collapsible()
                    ->visible(fn() => Auth::user()->hasRole('super_admin'))
                    ->persistCollapsed()
                    ->schema([
                        Forms\Components\Toggle::make('sieuthicode_enabled')
                            ->label('Bật tích hợp Sieuthicode cho tài khoản này')
                            ->helperText('Khi bật, hệ thống sẽ tự động kiểm tra giao dịch từ tài khoản này qua Sieuthicode API')
                            ->live()
                            ->columnSpanFull(),
                        Forms\Components\TextInput::make('sieuthicode_password')
                            ->label('Mật khẩu đăng nhập Sieuthicode')
                            ->password()
                            ->helperText('Mật khẩu đăng nhập tài khoản Sieuthicode của bạn (giống nhau cho tất cả ngân hàng)')
                            ->visible(fn(Forms\Get $get): bool => $get('sieuthicode_enabled')),
                        Forms\Components\TextInput::make('sieuthicode_token')
                            ->label('Token ngân hàng')
                            ->password()
                            ->helperText('Token API riêng cho ngân hàng này từ Sieuthicode')
                            ->visible(fn(Forms\Get $get): bool => $get('sieuthicode_enabled')),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        $isSuperAdmin = Auth::user()->hasRole('super_admin');

        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('logo')
                    ->visible(fn() => $isSuperAdmin)
                    ->searchable(),
                Tables\Columns\TextColumn::make('bank_name')
                    ->label('Ngân hàng')
                    ->searchable(),
                Tables\Columns\TextColumn::make('account_holder')
                    ->label('Tài khoản')
                    ->description(fn(BankAccount $record) => $record->account_number),
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên gợi nhớ')
                    ->searchable(),
                Tables\Columns\IconColumn::make('is_visible')
                    ->label('Hiển thị')
                    ->visible(fn() => $isSuperAdmin)
                    ->boolean(),
                Tables\Columns\IconColumn::make('sieuthicode_enabled')
                    ->label('Sieuthicode')
                    ->visible(fn() => $isSuperAdmin)
                    ->boolean()
                    ->tooltip('Tích hợp Sieuthicode API'),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make()
                    ->visible(fn() => $isSuperAdmin),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make()
                    ->visible(fn() => $isSuperAdmin),
                Tables\Actions\RestoreAction::make()
                    ->visible(fn() => $isSuperAdmin),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make()
                        ->visible(fn() => $isSuperAdmin),
                    Tables\Actions\RestoreBulkAction::make()
                        ->visible(fn() => $isSuperAdmin),
                ]),
            ]);
    }

    public static function canEdit(Model $record): bool
    {
        return ! app()->environment('demo');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListBankAccounts::route('/'),
            'create' => Pages\CreateBankAccount::route('/create'),
            'edit' => Pages\EditBankAccount::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        $isCollaborator = Auth::user()->hasRole('collaborator');

        return parent::getEloquentQuery()
            ->when($isCollaborator, function (Builder $query): void {
                $query
                    ->where('owner_type', User::class)
                    ->where('owner_id', Auth::id());
            }, function (Builder $query) {
                $query->where('owner_id', null);
            })
            ->withoutGlobalScopes($isCollaborator ? [] : [
                SoftDeletingScope::class,
            ]);
    }
}

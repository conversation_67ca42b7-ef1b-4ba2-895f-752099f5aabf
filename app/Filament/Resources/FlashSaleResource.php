<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\DiscountType;
use App\Filament\Resources\FlashSaleResource\Pages;
use App\Models\Account;
use App\Models\FlashSale;
use App\Models\FlashSaleAccount;
use App\Support\Helper;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\HtmlString;

class FlashSaleResource extends Resource
{
    protected static ?string $model = FlashSale::class;

    protected static ?string $navigationIcon = 'heroicon-o-bolt';

    protected static ?string $modelLabel = 'Flash Sale';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 5;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->label('Tên Flash Sale')
                            ->columnSpanFull()
                            ->required(),
                        Forms\Components\Textarea::make('description')
                            ->label('Mô tả')
                            ->columnSpanFull(),
                    ])
                    ->columnSpan(['sm' => 2])
                    ->columns(),
                Section::make()
                    ->schema([
                        Forms\Components\DateTimePicker::make('start_at')
                            ->label('Ngày bắt đầu')
                            ->default(now())
                            ->required(),
                        Forms\Components\DateTimePicker::make('end_at')
                            ->label('Ngày kết thúc')
                            ->default(now()->addDay())
                            ->after('start_at')
                            ->required(),
                        Forms\Components\Toggle::make('is_active')
                            ->label('Kích hoạt')
                            ->default(true)
                            ->required(),
                    ])
                    ->columnSpan(['sm' => 1]),

                Repeater::make('accounts')
                    ->hiddenLabel()
                    ->label('Danh sách tài khoản')
                    ->relationship()
                    ->columnSpanFull()
                    ->itemLabel(function (array $state) {
                        $account = Account::query()->find($state['account_id']);

                        return $account ? "{$account->category->name} #{$account->id}" : null;
                    })
                    ->schema([
                        Select::make('account_id')
                            ->label('Tài khoản')
                            ->relationship(
                                'account',
                                'id',
                                modifyQueryUsing: fn(Builder $query) => $query->with('category'),
                            )
                            ->reactive()
                            ->afterStateUpdated(function (Set $set, $state): void {
                                $account = Account::query()->find($state);
                                $set('value', $account->price);
                            })
                            ->getOptionLabelFromRecordUsing(fn(Account $record): string => "{$record->category->name} #{$record->id}")
                            ->searchable()
                            ->required(),
                        Forms\Components\Grid::make()
                            ->visible(fn(Get $get): bool => $get('account_id') !== null)
                            ->schema([
                                Forms\Components\Radio::make('type')
                                    ->label('Kiểu giảm giá')
                                    ->required()
                                    ->options(DiscountType::class)
                                    ->default(DiscountType::Fixed)
                                    ->afterStateUpdated(fn(Set $set): mixed => $set('value', null))
                                    ->reactive(),
                                TextInput::make('value')
                                    ->label(fn(Get $get): string => DiscountType::parse($get('type')) === DiscountType::Fixed ? 'Giá bán' : 'Phần trăm')
                                    ->required()
                                    ->numeric()
                                    ->minValue(0)
                                    ->default(0)
                                    ->prefix(fn(Get $get): string => DiscountType::parse($get('type')) === DiscountType::Fixed ? '₫' : '%')
                                    ->maxValue(fn(Get $get, ?FlashSaleAccount $record): ?int => DiscountType::parse($get('type')) === DiscountType::Fixed ? $record?->account?->original_price : 100)
                                    ->helperText(fn(?FlashSaleAccount $record): ?\Illuminate\Support\HtmlString => $record ? new HtmlString('Giá gốc: <strong>' . Helper::formatCurrency($record->account?->original_price) . '</strong>') : null),
                            ]),
                    ]),
            ])
            ->columns(3);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên')
                    ->searchable(),
                Tables\Columns\TextColumn::make('start_at')
                    ->label('Ngày bắt đầu')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\TextColumn::make('end_at')
                    ->label('Ngày kết thúc')
                    ->dateTime()
                    ->sortable(),
                Tables\Columns\IconColumn::make('is_available')
                    ->label('Đang diễn ra')
                    ->icon(fn(FlashSale $record): string => $record->is_available ? 'heroicon-o-check-circle' : 'heroicon-o-x-circle')
                    ->color(fn(FlashSale $record): string => $record->is_available ? 'success' : 'danger'),
                Tables\Columns\ToggleColumn::make('is_active')
                    ->label('Kích hoạt'),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListFlashSales::route('/'),
            'create' => Pages\CreateFlashSale::route('/create'),
            'edit' => Pages\EditFlashSale::route('/{record}/edit'),
        ];
    }
}

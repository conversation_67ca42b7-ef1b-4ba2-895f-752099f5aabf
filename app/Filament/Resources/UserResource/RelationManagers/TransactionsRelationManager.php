<?php

declare(strict_types=1);

namespace App\Filament\Resources\UserResource\RelationManagers;

use App\Enums\TransactionType;
use App\Filament\Resources\TransactionResource;
use App\Models\Transaction;
use App\Tables\Columns\DateTimeColumn;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;

class TransactionsRelationManager extends RelationManager
{
    protected static string $relationship = 'transactions';

    public function table(Table $table): Table
    {
        return $table
            ->heading('Lịch sử giao dịch')
            ->recordTitleAttribute('type')
            ->columns([
                DateTimeColumn::make('created_at')
                    ->toggleable(false)
                    ->label('Thời gian'),
                Tables\Columns\TextColumn::make('id')
                    ->prefix('#')
                    ->url(fn(Transaction $record): string => TransactionResource::getUrl('view', [$record]))
                    ->label('ID'),
                Tables\Columns\TextColumn::make('type')
                    ->label('Loại giao dịch')
                    ->formatStateUsing(fn(TransactionType $state): ?string => $state->getLabel()),
                Tables\Columns\TextColumn::make('amount')
                    ->money()
                    ->color(fn(Transaction $record): string => $record->type->isPositive() ? 'success' : 'danger')
                    ->label('Số tiền'),
                Tables\Columns\TextColumn::make('balance')
                    ->money()
                    ->label('Số dư cuối'),
                Tables\Columns\TextColumn::make('description')
                    ->label('Mô tả'),
            ]);
    }
}

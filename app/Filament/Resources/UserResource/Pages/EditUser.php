<?php

declare(strict_types=1);

namespace App\Filament\Resources\UserResource\Pages;

use App\Filament\Resources\UserResource;
use Filament\Actions;
use Filament\Actions\DeleteAction;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Auth;

class EditUser extends EditRecord
{
    protected static string $resource = UserResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make()
                ->before(function (DeleteAction $action): void {
                    if ($this->getRecord()->isNot(Auth::user())) {
                        return;
                    }

                    Notification::make()
                        ->warning()
                        ->title(__('You cannot delete yourself.'))
                        ->send();

                    $action->cancel();
                }),
        ];
    }
}

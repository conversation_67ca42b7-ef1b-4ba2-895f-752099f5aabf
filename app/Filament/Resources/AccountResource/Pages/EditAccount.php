<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Enums\AttributeType;
use App\Filament\Resources\AccountResource;
use App\Models\Account;
use App\Models\AttributeSet;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class EditAccount extends EditRecord
{
    protected static string $resource = AccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Account $record */
        $record = $this->getRecord();

        $attributes = [];

        foreach ($record->accountAttributes as $accountAttribute) {
            $attributeSetId = $accountAttribute->attribute_set_id;

            if ($accountAttribute->value) {
                // Text attribute - use the value
                $attributes[$attributeSetId] = $accountAttribute->value;
            } else {
                // Dropdown attribute - use the attribute_id
                $attributes[$attributeSetId] = $accountAttribute->attribute_id;
            }
        }

        return [
            ...$data,
            'attributes' => $attributes,
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $attributes = array_filter(Arr::pull($data, 'attributes', []));

        /** @var \App\Models\Account $model */
        $model = parent::handleRecordUpdate($record, $data);

        // Clear existing account attributes
        $model->accountAttributes()->delete();

        if (! empty($attributes)) {
            $this->attachAttributes($model, $attributes);
        }

        return $model;
    }

    private function attachAttributes(Account $account, array $attributes): void
    {
        $attributeSets = AttributeSet::query()
            ->where('category_id', $account->category_id)
            ->get()
            ->keyBy('id');

        foreach ($attributes as $attributeSetId => $value) {
            $attributeSet = $attributeSets->get($attributeSetId);

            if (! $attributeSet || empty($value)) {
                continue;
            }

            // Use consistent approach for both dropdown and text attributes
            DB::table('account_attributes')->insert([
                'account_id' => $account->id,
                'attribute_id' => $attributeSet->type === AttributeType::Dropdown ? $value : null,
                'attribute_set_id' => $attributeSetId,
                'value' => $attributeSet->type === AttributeType::Text ? $value : null,
            ]);
        }
    }
}

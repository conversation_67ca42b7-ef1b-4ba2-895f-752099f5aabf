<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Filament\Resources\AccountResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class EditAccount extends EditRecord
{
    protected static string $resource = AccountResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
            Actions\ForceDeleteAction::make(),
            Actions\RestoreAction::make(),
        ];
    }

    protected function mutateFormDataBeforeFill(array $data): array
    {
        /** @var \App\Models\Account $record */
        $record = $this->getRecord();

        /** @var \Illuminate\Support\Collection<\App\Models\Attribute> $attributes */
        $attributes = $record->attributes->pluck('id', 'attribute_set_id');

        return [
            ...$data,
            'attributes' => $attributes,
        ];
    }

    protected function handleRecordUpdate(Model $record, array $data): Model
    {
        $attributes = array_filter(Arr::pull($data, 'attributes', []));

        /** @var \App\Models\Account $model */
        $model = parent::handleRecordUpdate($record, $data);

        if (! empty($attributes)) {
            $model->attributes()->sync($attributes);
        }

        return $model;
    }
}

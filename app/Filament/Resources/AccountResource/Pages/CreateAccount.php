<?php

declare(strict_types=1);

namespace App\Filament\Resources\AccountResource\Pages;

use App\Enums\AccountStatus;
use App\Enums\AttributeType;
use App\Filament\Resources\AccountResource;
use App\Models\Account;
use App\Models\AttributeSet;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class CreateAccount extends CreateRecord
{
    protected static string $resource = AccountResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $attributes = array_filter(Arr::pull($data, 'attributes', []));
        $bulkMode = Arr::pull($data, 'bulk_mode', 'single');
        $bulkAccounts = Arr::pull($data, 'bulk_accounts', '');

        if ($bulkMode === 'bulk' && ! empty($bulkAccounts)) {
            $accounts = $this->parseBulkAccounts($bulkAccounts);

            if (! empty($accounts)) {
                $firstAccount = $this->createAccount($data, $accounts[0], $attributes);

                for ($i = 1; $i < count($accounts); $i++) {
                    $this->createAccount($data, $accounts[$i], $attributes);
                }

                return $firstAccount;
            }
        }

        return $this->createAccount($data, [
            'acc_name' => $data['acc_name'] ?? '',
            'acc_pass' => $data['acc_pass'] ?? '',
        ], $attributes);
    }

    private function createAccount(array $data, array $accountData, array $attributes): Account
    {
        if (! Auth::user()->hasRole('super_admin')) {
            $accountData['user_id'] = Auth::id();
            $accountData['status'] = AccountStatus::Pending;
        }

        $accountData = array_merge($data, $accountData);

        /** @var Account $model */
        $model = static::getModel()::create($accountData);

        if (! empty($attributes)) {
            $this->attachAttributes($model, $attributes);
        }

        return $model;
    }

    private function parseBulkAccounts(string $bulkAccounts): array
    {
        $lines = array_filter(array_map('trim', explode("\n", $bulkAccounts)));
        $accounts = [];

        foreach ($lines as $line) {
            $parts = explode('|', $line, 2);

            if (count($parts) === 2) {
                $accounts[] = [
                    'acc_name' => trim($parts[0]),
                    'acc_pass' => trim($parts[1]),
                ];
            } elseif (count($parts) === 1 && !empty(trim($parts[0]))) {
                $accounts[] = [
                    'acc_name' => trim($parts[0]),
                    'acc_pass' => null,
                ];
            }
        }

        return $accounts;
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    private function attachAttributes(Account $account, array $attributes): void
    {
        $attributeSets = AttributeSet::query()
            ->where('category_id', $account->category_id)
            ->get()
            ->keyBy('id');

        foreach ($attributes as $attributeSetId => $value) {
            $attributeSet = $attributeSets->get($attributeSetId);

            if (!$attributeSet || empty($value)) {
                continue;
            }

            // Use consistent approach for both dropdown and text attributes
            DB::table('account_attributes')->insert([
                'account_id' => $account->id,
                'attribute_id' => $attributeSet->type === AttributeType::Dropdown ? $value : null,
                'attribute_set_id' => $attributeSetId,
                'value' => $attributeSet->type === AttributeType::Text ? $value : null,
            ]);
        }
    }
}

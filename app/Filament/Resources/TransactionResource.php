<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\TransactionResource\Pages;
use App\Models\Account;
use App\Models\Deposit;
use App\Models\Recharge;
use App\Models\Transaction;
use App\Models\User;
use App\Models\Wheel;
use App\Support\Helper;
use App\Tables\Columns\DateTimeColumn;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class TransactionResource extends Resource
{
    protected static ?string $model = Transaction::class;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path';

    protected static ?string $modelLabel = 'Lịch sử giao dịch';

    protected static ?string $navigationGroup = 'Quản lý <PERSON> ch<PERSON>h';

    protected static ?int $navigationSort = 3;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('user.username')
                            ->url(fn(Transaction $record): string => UserResource::getUrl('edit', [$record->user]))
                            ->label('Người dùng'),
                        TextEntry::make('created_at')
                            ->label('Thời gian'),
                        TextEntry::make('type')
                            ->label('Giao dịch'),
                        TextEntry::make('reference_type')
                            ->label('Đối tượng')
                            ->formatStateUsing(function (string $state, Transaction $record): string {
                                $label = match ($state) {
                                    User::class => UserResource::getModelLabel(),
                                    Account::class => AccountResource::getModelLabel(),
                                    Wheel::class => WheelResource::getModelLabel(),
                                    Recharge::class => RechargeResource::getModelLabel(),
                                    default => $state,
                                };

                                return sprintf('%s #%s', $label, $record->reference_id);
                            })
                            ->url(function (Transaction $record) {
                                $resource = match ($record->reference_type) {
                                    User::class => UserResource::class,
                                    Account::class => AccountResource::class,
                                    Wheel::class => WheelResource::class,
                                    Recharge::class => RechargeResource::class,
                                    Deposit::class => DepositResource::class,
                                    default => null,
                                };

                                return ($resource && $resource::hasPage('edit')) ? $resource::getUrl('edit', [$record->reference_id]) : $resource::getUrl('view', [$record->reference_id]);
                            }),
                        TextEntry::make('amount')
                            ->label('Số tiền')
                            ->color(fn(Transaction $record): string => $record->type->isPositive() ? 'success' : 'danger')
                            ->formatStateUsing(function (int $state, Transaction $record): string {
                                $amount = Helper::formatCurrency($state);

                                return $record->type->isPositive() ? "+$amount" : "-$amount";
                            }),
                        TextEntry::make('balance')
                            ->label('Số dư cuối')
                            ->money(),
                        TextEntry::make('description')
                            ->columnSpanFull()
                            ->label('Mô tả'),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('created_at', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('user.username')
                    ->label('Người dùng')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('type')
                    ->label('Giao dịch')
                    ->sortable()
                    ->searchable(),
                Tables\Columns\TextColumn::make('amount')
                    ->label('Số tiền')
                    ->sortable()
                    ->color(fn(Transaction $record): string => $record->type->isPositive() ? 'success' : 'danger')
                    ->formatStateUsing(function (int $state, Transaction $record): string {
                        $amount = Helper::formatCurrency($state);

                        return $record->type->isPositive() ? "+$amount" : "-$amount";
                    })
                    ->searchable(),
                Tables\Columns\TextColumn::make('balance')
                    ->label('Số dư cuối')
                    ->sortable()
                    ->money('VND', locale: app()->getLocale())
                    ->searchable(),
                DateTimeColumn::make('created_at', false)
                    ->since()
                    ->label('Thời gian'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListTransactions::route('/'),
            'view' => Pages\ViewTransaction::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

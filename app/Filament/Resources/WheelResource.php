<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\RewardType;
use App\Filament\Resources\WheelResource\Pages;
use App\Forms\Components\CurrencyInput;
use App\Models\GameItem;
use App\Models\Wheel;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Components\Section;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Str;

class WheelResource extends Resource
{
    protected static ?string $model = Wheel::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Vòng Quay';

    protected static ?string $navigationGroup = 'Quản lý Game';

    protected static ?int $navigationSort = 10;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('name')
                            ->live(onBlur: true)
                            ->afterStateUpdated(fn(Forms\Set $set, ?string $state): mixed => $set('slug', Str::slug($state)))
                            ->label('Tên vòng quay')
                            ->required(),
                        Forms\Components\TextInput::make('slug')
                            ->unique(ignoreRecord: true)
                            ->required(),
                        CurrencyInput::make('price')
                            ->label('Giá tiền mỗi lượt quay')
                            ->columnSpanFull()
                            ->minValue(0)
                            ->required(),
                        Forms\Components\RichEditor::make('description')
                            ->label('Mô tả')
                            ->columnSpanFull(),
                        Forms\Components\FileUpload::make('image')
                            ->label('Ảnh vòng quay')
                            ->directory('wheels')
                            ->image()
                            ->required(),
                        Forms\Components\FileUpload::make('thumbnail')
                            ->label('Ảnh đại diện')
                            ->directory('wheels')
                            ->image(),
                        Section::make()
                            ->heading('Cấu hình vòng quay')
                            ->visibleOn('edit')
                            ->schema([
                                Forms\Components\Repeater::make('wheelSegments')
                                    ->hiddenLabel()
                                    ->label('Phần thưởng')
                                    ->relationship('wheelSegments')
                                    ->columnSpanFull()
                                    ->columns()
                                    ->orderColumn('order')
                                    ->schema([
                                        Forms\Components\Radio::make('reward_type')
                                            ->label('Loại phần thưởng')
                                            ->options(RewardType::class)
                                            ->inline()
                                            ->inlineLabel(false)
                                            ->live()
                                            ->columnSpanFull()
                                            ->default(RewardType::Money)
                                            ->required(),
                                        Forms\Components\TextInput::make('name')
                                            ->label('Tên phần thưởng')
                                            ->required(),
                                        Forms\Components\TextInput::make('probability')
                                            ->label('Xác suất (%)')
                                            ->default(10)
                                            ->numeric()
                                            ->required(),
                                        Forms\Components\Select::make('game_item_id')
                                            ->label('Vật phẩm')
                                            ->options(fn() => GameItem::query()->pluck('name', 'id')->all())
                                            ->hidden(fn(Forms\Get $get): bool => RewardType::parse($get('reward_type')) !== RewardType::Item)
                                            ->required(),
                                        CurrencyInput::make('value')
                                            ->label(fn(Get $get): string => RewardType::parse($get('reward_type')) === RewardType::Money ? 'Số tiền' : 'Số lượng')
                                            ->prefix(fn(Get $get): ?string => RewardType::parse($get('reward_type')) === RewardType::Money ? '₫' : null)
                                            ->hidden(fn(Forms\Get $get): bool => in_array(RewardType::parse($get('reward_type')), [RewardType::Account, RewardType::None]))
                                            ->default(0)
                                            ->required(),
                                    ]),
                            ]),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')
                    ->label('Tên vòng quay')
                    ->url(fn(Wheel $wheel) => route('wheels.show', $wheel))
                    ->openUrlInNewTab()
                    ->searchable(),
                Tables\Columns\ImageColumn::make('thumbnail')
                    ->label('Ảnh đại diện'),
                Tables\Columns\TextColumn::make('price')
                    ->label('Giá tiền')
                    ->money()
                    ->sortable(),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
                Tables\Actions\ForceDeleteAction::make(),
                Tables\Actions\RestoreAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListWheels::route('/'),
            'create' => Pages\CreateWheel::route('/create'),
            'edit' => Pages\EditWheel::route('/{record}/edit'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

<?php

declare(strict_types=1);

namespace App\Filament\Resources\AdResource\Pages;

use App\Filament\Resources\AdResource;
use App\Jobs\SendAdToGmailUsers;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;

class EditAd extends EditRecord
{
    protected static string $resource = AdResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\Action::make('sendGmail')
                ->label('Gửi email')
                ->requiresConfirmation()
                ->modalHeading('Gửi email tới tất cả người dùng')
                ->modalDescription('Bạn có chắc chắn muốn gửi email tới tất cả người dùng không?')
                ->modalSubmitActionLabel('Gửi')
                ->modalCancelActionLabel('Hủy')
                ->action(function (): void {
                    SendAdToGmailUsers::dispatch($this->record);

                    Notification::make()
                        ->success()
                        ->title('<PERSON>ail đã được đưa vào hàng đợi')
                        ->send();
                }),
            Actions\DeleteAction::make(),
        ];
    }
}

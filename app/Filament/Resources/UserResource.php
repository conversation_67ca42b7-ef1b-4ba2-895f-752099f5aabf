<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Enums\TransactionType;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers\TransactionsRelationManager;
use App\Models\User;
use App\Support\Helper;
use App\Tables\Columns\DateTimeColumn;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Resource;
use Filament\Support\Enums\MaxWidth;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Actions\DeleteBulkAction;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Filters\TernaryFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Auth;

/**
 * @property string $avatar_url
 */
class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $modelLabel = 'Người dùng';

    protected static ?string $navigationGroup = 'Quản lý Người dùng';

    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                Section::make()
                    ->schema([
                        Forms\Components\TextInput::make('username')
                            ->label('Tên tài khoản')
                            ->unique(ignoreRecord: true)
                            ->required(),
                        Forms\Components\TextInput::make('email')
                            ->email()
                            ->unique(ignoreRecord: true)
                            ->required(),
                        Forms\Components\TextInput::make('name')
                            ->label('Họ và tên'),
                        Forms\Components\TextInput::make('phone')
                            ->label('Số điện thoại')
                            ->unique(ignoreRecord: true)
                            ->tel(),
                        Forms\Components\TextInput::make('password')
                            ->label('Mật khẩu')
                            ->password()
                            ->minLength(6)
                            ->maxLength(255)
                            ->columnSpanFull()
                            ->required(fn(Page $livewire): bool => $livewire instanceof CreateRecord)
                            ->dehydrated(fn(?string $state) => filled($state)),
                        Placeholder::make('balance')
                            ->hiddenOn('create')
                            ->label('Số dư')
                            ->content(fn(int $state): string => Helper::formatCurrency($state)),
                        Forms\Components\FileUpload::make('avatar')
                            ->label('Ảnh đại diện')
                            ->directory('avatars')
                            ->columnSpanFull()
                            ->image()
                            ->avatar(),
                        Forms\Components\CheckboxList::make('roles')
                            ->label('Vai trò')
                            ->relationship('roles', 'name')
                            ->searchable(),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\ImageColumn::make('avatar')
                    ->circular()
                    ->defaultImageUrl(fn(User $record) => $record->avatar_url)
                    ->label('Ảnh đại diện'),
                Tables\Columns\TextColumn::make('name')
                    ->label('Họ và tên')
                    ->searchable(),
                Tables\Columns\TextColumn::make('email')
                    ->searchable(),
                Tables\Columns\TextColumn::make('balance')
                    ->label('Số dư')
                    ->numeric()
                    ->sortable(),
                TextColumn::make('roles.name')
                    ->label('Vai trò')
                    ->badge(),
                DateTimeColumn::make('created_at'),
                DateTimeColumn::make('updated_at'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
                TernaryFilter::make('email_verified_at')
                    ->label('Xác thực email')
                    ->nullable(),
                SelectFilter::make('roles.name')
                    ->relationship('roles', 'name')
                    ->label('Vai trò'),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\EditAction::make(),
                    Action::make('add-balance')
                        ->icon('heroicon-o-currency-dollar')
                        ->label('Cộng trừ tiền')
                        ->modal()
                        ->modalWidth(MaxWidth::Medium)
                        ->modalHeading(function (Action $action) {
                            /** @var User $record */
                            $record = $action->getRecord();

                            return __('Cộng trừ tiền cho :name', ['name' => $record->name]);
                        })
                        ->modalSubmitActionLabel('Cộng trừ tiền')
                        ->form([
                            Placeholder::make('balance')
                                ->label('Số dư')
                                ->content(function (Placeholder $placeholder): string {
                                    /** @var User $record */
                                    $record = $placeholder->getRecord();

                                    return Helper::formatCurrency($record->balance);
                                }),
                            Radio::make('type')
                                ->label('Loại')
                                ->options([
                                    'add' => 'Cộng tiền',
                                    'subtract' => 'Trừ tiền',
                                ])
                                ->default('add')
                                ->inline()
                                ->inlineLabel(false)
                                ->required(),
                            Forms\Components\TextInput::make('amount')
                                ->label('Số tiền')
                                ->numeric()
                                ->minValue(1)
                                ->maxValue(9999999999)
                                ->required(),
                            Textarea::make('description')
                                ->placeholder('Nhập mô tả cho giao dịch'),
                        ])
                        ->action(function (array $data, User $record): void {
                            $amount = (int) $data['amount'];

                            if ($data['type'] === 'add') {
                                $type = TransactionType::AddBalance;
                                $description = 'Cộng tiền thành công';
                            } else {
                                if ($record->balance < $amount) {
                                    Notification::make()
                                        ->warning()
                                        ->title('Số dư không đủ để thực hiện giao dịch.')
                                        ->send();

                                    return;
                                }

                                $type = TransactionType::SubtractBalance;
                                $description = 'Trừ tiền thành công';
                            }

                            $record->recordTransaction($type, $amount, description: $data['description'] ?: $description);

                            Notification::make()
                                ->success()
                                ->title('Cộng trừ tiền thành công.')
                                ->send();
                        }),
                    Tables\Actions\DeleteAction::make()
                        ->before(function (Action $action): void {
                            if ($action->getRecord()->is(Auth::user())) {
                                Notification::make()
                                    ->warning()
                                    ->title('Bạn không thể xóa chính mình.')
                                    ->send();

                                $action->cancel();
                            }
                        }),
                    Tables\Actions\ForceDeleteAction::make(),
                    Tables\Actions\RestoreAction::make(),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()
                        ->before(function (DeleteBulkAction $action): void {
                            $action->getRecords()->each(function (Model $model) use ($action): void {
                                if ($model->isNot(Auth::user())) {
                                    return;
                                }

                                Notification::make()
                                    ->warning()
                                    ->title(__('You cannot delete yourself.'))
                                    ->send();

                                $action->cancel();
                            });
                        }),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    /**
     * @param User $record
     */
    public static function canDelete(Model $record): bool
    {
        return $record->isNot(Auth::user()) && ! $record->hasRole('super_admin');
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getRelations(): array
    {
        return [
            TransactionsRelationManager::class,
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

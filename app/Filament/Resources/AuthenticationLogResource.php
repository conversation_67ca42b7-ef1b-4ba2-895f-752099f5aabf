<?php

declare(strict_types=1);

namespace App\Filament\Resources;

use App\Filament\Resources\AuthenticationLogResource\Pages;
use App\Models\AuthenticationLog;
use Filament\Infolists\Components\Section;
use Filament\Infolists\Components\TextEntry;
use Filament\Infolists\Infolist;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Jenssegers\Agent\Agent;

class AuthenticationLogResource extends Resource
{
    protected static ?string $model = AuthenticationLog::class;

    protected static ?string $navigationIcon = 'heroicon-o-rectangle-stack';

    protected static ?string $modelLabel = 'Nhật ký đăng nhập';

    protected static ?string $navigationGroup = 'Quản lý Người dùng';

    protected static ?int $navigationSort = 2;

    public static function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Section::make()
                    ->schema([
                        TextEntry::make('id')
                            ->label('ID'),
                        TextEntry::make('authenticatable_type')
                            ->label('Người dùng')
                            ->url(fn(AuthenticationLog $authenticationLog): string => UserResource::getUrl('edit', [$authenticationLog->authenticatable]))
                            ->formatStateUsing(fn(AuthenticationLog $authenticationLog): string => $authenticationLog->authenticatable->getFilamentName()),
                        TextEntry::make('ip_address')
                            ->url(fn(string $state): string => "https://ipinfo.io/$state", true)
                            ->label('Địa chỉ IP'),
                        TextEntry::make('user_agent')
                            ->label('User Agent'),
                        TextEntry::make('user_agent')
                            ->label('Trình duyệt')
                            ->formatStateUsing(function (string $state): string {
                                $agent = tap(new Agent(), fn($agent) => $agent->setUserAgent($state));

                                return sprintf('%s - %s', $agent->platform(), $agent->browser());
                            }),
                        TextEntry::make('login_at')
                            ->label('Đăng nhập lúc')
                            ->visible(fn(AuthenticationLog $authenticationLog): bool => $authenticationLog->login_at !== null),
                        TextEntry::make('logout_at')
                            ->label('Đăng xuất lúc')
                            ->visible(fn(AuthenticationLog $authenticationLog): bool => $authenticationLog->logout_at !== null),
                        TextEntry::make('location')
                            ->visible(fn(AuthenticationLog $authenticationLog): bool => $authenticationLog->location !== null),
                    ])
                    ->columns(),
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->defaultSort('id', 'desc')
            ->columns([
                Tables\Columns\TextColumn::make('id')
                    ->label('ID')
                    ->sortable(),
                Tables\Columns\TextColumn::make('authenticatable_type')
                    ->label('Người dùng')
                    ->searchable()
                    ->formatStateUsing(fn(AuthenticationLog $record): string => $record->authenticatable->getFilamentName())
                    ->url(fn(AuthenticationLog $record): string => UserResource::getUrl('edit', [$record->authenticatable])),
                Tables\Columns\TextColumn::make('ip_address')
                    ->url(fn(string $state): string => "https://ipinfo.io/$state", true)
                    ->searchable()
                    ->sortable()
                    ->label('Địa chỉ IP'),
                Tables\Columns\TextColumn::make('user_agent')
                    ->searchable()
                    ->label('Trình duyệt')
                    ->formatStateUsing(function (string $state): string {
                        $agent = tap(new Agent(), fn($agent) => $agent->setUserAgent($state));

                        return sprintf('%s - %s', $agent->platform(), $agent->browser());
                    }),
                Tables\Columns\TextColumn::make('login_at')
                    ->sortable()
                    ->since()
                    ->label('Đăng nhập lúc'),
                Tables\Columns\TextColumn::make('logout_at')
                    ->since()
                    ->sortable()
                    ->label('Đăng xuất lúc'),
            ])
            ->filters([
                Tables\Filters\TrashedFilter::make(),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                    Tables\Actions\ForceDeleteBulkAction::make(),
                    Tables\Actions\RestoreBulkAction::make(),
                ]),
            ]);
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListAuthenticationLogs::route('/'),
            'view' => Pages\ViewAuthenticationLog::route('/{record}'),
        ];
    }

    public static function getEloquentQuery(): Builder
    {
        return parent::getEloquentQuery()
            ->withoutGlobalScopes([
                SoftDeletingScope::class,
            ]);
    }
}

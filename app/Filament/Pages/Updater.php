<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Filament\Widgets\UpdaterStatsWidget;
use App\Services\UpdaterService;
use BezhanSalleh\FilamentShield\Traits\HasPageShield;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Pages\Page;

class Updater extends Page
{
    use HasPageShield;

    protected static ?string $navigationIcon = 'heroicon-o-arrow-path';

    protected static ?string $navigationGroup = 'Cài đặt';

    protected static ?string $title = 'Cập nhật hệ thống';

    protected static ?string $slug = 'updater';

    protected static ?int $navigationSort = 5;

    protected static string $view = 'filament.pages.updater';

    public ?array $updateInfo = null;

    public ?array $updateHistory = [];

    public function mount(): void
    {
        $this->updateHistory = app(UpdaterService::class)->getUpdateHistory();
    }

    public function checkForUpdates(): void
    {
        $updaterService = app(UpdaterService::class);

        $licenseResult = $updaterService->validateLicense();

        if (! $licenseResult['success']) {
            Notification::make()
                ->title('Lỗi License')
                ->body($licenseResult['message'])
                ->danger()
                ->send();
            return;
        }

        $result = $updaterService->checkForUpdates();

        if ($result['success'] && $result['has_update']) {
            $this->updateInfo = $result;

            Notification::make()
                ->title('Có bản cập nhật mới!')
                ->body('Phiên bản ' . $result['latest_version'] . ' đã sẵn sàng.')
                ->success()
                ->send();
        } else {
            Notification::make()
                ->title('Không có bản cập nhật mới')
                ->body($result['message'] ?? 'Hệ thống đã được cập nhật đến phiên bản mới nhất.')
                ->info()
                ->send();
        }
    }

    public function downloadUpdate(): void
    {
        if (! $this->updateInfo) {
            Notification::make()
                ->title('Lỗi')
                ->body('Không có thông tin update.')
                ->danger()
                ->send();
            return;
        }

        $updaterService = app(UpdaterService::class);
        $result = $updaterService->downloadUpdate($this->updateInfo['latest_version']);

        if ($result['success']) {
            $updaterService->logUpdate($this->updateInfo['latest_version'], 'downloaded');

            Notification::make()
                ->title('Download thành công')
                ->body('Đã tải xuống bản cập nhật ' . $this->updateInfo['latest_version'])
                ->success()
                ->send();

            $this->updateInfo['file_path'] = $result['file_path'];
            $this->updateHistory = $updaterService->getUpdateHistory();
        } else {
            $updaterService->logUpdate($this->updateInfo['latest_version'], 'failed');

            Notification::make()
                ->title('Lỗi download')
                ->body($result['message'])
                ->danger()
                ->send();
        }
    }

    public function installUpdate(): void
    {
        if (! $this->updateInfo || !isset($this->updateInfo['file_path'])) {
            Notification::make()
                ->title('Lỗi')
                ->body('Không có file update để cài đặt.')
                ->danger()
                ->send();
            return;
        }

        $updaterService = app(UpdaterService::class);
        $result = $updaterService->installUpdate($this->updateInfo['file_path'], $this->updateInfo['latest_version']);

        if ($result['success']) {
            $updaterService->logUpdate($this->updateInfo['latest_version'], 'installed');

            Notification::make()
                ->title('Cài đặt thành công')
                ->body('Hệ thống đã được cập nhật thành công!')
                ->success()
                ->send();

            $this->updateInfo = null;
            $this->updateHistory = $updaterService->getUpdateHistory();
        } else {
            $updaterService->logUpdate($this->updateInfo['latest_version'], 'failed');

            Notification::make()
                ->title('Lỗi cài đặt')
                ->body($result['message'])
                ->danger()
                ->send();
        }
    }

    public function autoUpdate(): void
    {
        $updaterService = app(UpdaterService::class);
        $result = $updaterService->autoUpdate();

        if ($result['success']) {
            // Get the version from the result or use a default
            $version = $result['new_version'] ?? 'unknown';
            $updaterService->logUpdate($version, 'installed');

            Notification::make()
                ->title('Tự động cập nhật thành công')
                ->body('Hệ thống đã được cập nhật tự động!')
                ->success()
                ->send();

            $this->updateInfo = null;
            $this->updateHistory = $updaterService->getUpdateHistory();
        } else {
            Notification::make()
                ->title('Lỗi tự động cập nhật')
                ->body($result['message'])
                ->danger()
                ->send();
        }
    }

    protected function getHeaderActions(): array
    {
        $updaterService = app(UpdaterService::class);
        $requirements = $updaterService->getServerRequirements();

        $actions = [
            Action::make('checkForUpdates')
                ->label('Kiểm tra cập nhật')
                ->icon('heroicon-o-arrow-path')
                ->action('checkForUpdates')
                ->visible(fn() => $requirements['can_update']),
        ];

        if ($requirements['can_update']) {
            $actions[] = Action::make('autoUpdate')
                ->label('Tự động cập nhật')
                ->icon('heroicon-o-play')
                ->color('success')
                ->requiresConfirmation()
                ->modalHeading('Xác nhận tự động cập nhật')
                ->modalDescription('Hệ thống sẽ tự động kiểm tra, tải xuống và cài đặt bản cập nhật mới nhất. Bạn có chắc chắn muốn tiếp tục?')
                ->action('autoUpdate')
                ->visible(fn() => $this->updateInfo !== null);
        }

        return $actions;
    }

    protected function getViewData(): array
    {
        $updaterService = app(UpdaterService::class);

        return [
            'currentVersion' => $updaterService->getCurrentVersion(),
            'updateInfo' => $this->updateInfo,
            'updateHistory' => $this->updateHistory,
            'requirements' => $updaterService->getServerRequirements(),
        ];
    }

    public function getActions(): array
    {
        $actions = [];
        $updaterService = app(UpdaterService::class);
        $requirements = $updaterService->getServerRequirements();

        if (! $requirements['can_update']) {
            return $actions;
        }

        if ($this->updateInfo && isset($this->updateInfo['file_path'])) {
            $actions[] = Action::make('installUpdate')
                ->label('Cài đặt cập nhật')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Xác nhận cài đặt cập nhật')
                ->modalDescription('Hệ thống sẽ được cập nhật lên phiên bản ' . $this->updateInfo['latest_version'] . '. Bạn có chắc chắn muốn tiếp tục?')
                ->action('installUpdate');
        }

        if ($this->updateInfo && !isset($this->updateInfo['file_path'])) {
            $actions[] = Action::make('downloadUpdate')
                ->label('Tải xuống cập nhật')
                ->icon('heroicon-o-arrow-down-tray')
                ->color('primary')
                ->action('downloadUpdate');
        }

        return $actions;
    }

    protected function getHeaderWidgets(): array
    {
        return [
            UpdaterStatsWidget::class,
        ];
    }
}

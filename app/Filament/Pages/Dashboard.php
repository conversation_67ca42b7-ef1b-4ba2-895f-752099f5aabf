<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use Filament\Forms\Form;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Concerns\HasFiltersForm;
use Malzariey\FilamentDaterangepickerFilter\Fields\DateRangePicker;

class Dashboard extends BaseDashboard
{
    use HasFiltersForm;

    public function filtersForm(Form $form): Form
    {
        return $form
            ->schema([
                DateRangePicker::make('date_range')
                    ->timezone(config('app.timezone'))
                    ->defaultToday()
                    ->hiddenLabel(),
            ]);
    }
}

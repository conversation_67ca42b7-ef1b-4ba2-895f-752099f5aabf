<?php

declare(strict_types=1);

namespace App\Randomizer;

use Exception;

class Element
{
    /**
     * @throws Exception
     */
    public function __construct(protected mixed $data, protected float $weight = 1.0)
    {
        if ($weight < 0) {
            throw new Exception('Weight must be greater than or equal to 0');
        }
    }

    public function getData(): mixed
    {
        return $this->data;
    }

    public function getWeight(): float
    {
        return $this->weight;
    }
}

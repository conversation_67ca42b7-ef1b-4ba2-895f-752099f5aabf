<?php

declare(strict_types=1);

namespace App\Randomizer;

use Exception;

class Randomizer
{
    /**
     * @var Element[]
     */
    protected array $elements = [];

    public function get(): mixed
    {
        $total = $this->getTotalWeight();
        $random = mt_rand(0, (int) $total);

        foreach ($this->elements as $element) {
            $random -= $element->getWeight();

            if ($random <= 0) {
                return $element->getData();
            }
        }

        return $this->elements[random_int(0, count($this->elements) - 1)]->getData();
    }

    /**
     * @throws Exception
     */
    public function add(Element $element): static
    {
        if ($element->getData() == null) {
            throw new Exception("Invalid Element data: null is not allowed.");
        }

        if ($this->elementExistsWith($element->getData())) {
            $this->addWeightToExisting($element);
        } else {
            $this->elements[] = $element;
        }

        return $this;
    }

    protected function getTotalWeight(): float
    {
        return array_reduce(
            $this->elements,
            fn($carry, $element): float => $carry + $element->getWeight(),
            0.0,
        );
    }

    protected function elementExistsWith(mixed $data): bool
    {
        $found = null;
        array_map(function ($element) use ($data, &$found): void {
            if ($element->getData() === $data) {
                $found = $element;
            }
        }, $this->elements);

        return $found instanceof Element;
    }

    /**
     * @throws Exception
     */
    protected function addWeightToExisting(Element $newElement): void
    {
        $elements = $this->elements;

        array_map(function ($existingElement, $index) use (&$elements, &$newElement): void {
            if ($existingElement->getData() === $newElement->getData()) {
                $newWeight = $existingElement->getWeight() + $newElement->getWeight();
                $elements[$index] = new Element($existingElement->getData(), $newWeight);
            }
        }, $elements, array_keys($elements));

        $this->elements = $elements;
    }
}

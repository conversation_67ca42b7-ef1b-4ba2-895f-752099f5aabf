<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\Affiliate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AffiliateApprovedNotification extends Notification implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly Affiliate $affiliate,
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        return (new MailMessage())
            ->subject('🎉 Chúc mừng! Tài khoản Affiliate của bạn đã được duyệt')
            ->greeting('Xin chào ' . $this->affiliate->user->display_name . '!')
            ->line('Chúng tôi rất vui mừng thông báo rằng đơn đăng ký Affiliate của bạn đã được **duyệt thành công**!')
            ->line('')
            ->line('**Thông tin tài khoản Affiliate:**')
            ->line('• **Mã giới thiệu:** ' . $this->affiliate->referral_code)
            ->line('• **Cấp bậc:** ' . $this->affiliate->tier->name)
            ->line('• **Tỷ lệ hoa hồng:** ' . $this->affiliate->tier->commission_rate . '%')
            ->line('• **Link giới thiệu:** ' . $this->affiliate->referral_url)
            ->line('')
            ->line('**Bước tiếp theo:**')
            ->line('1. Đăng nhập vào tài khoản của bạn')
            ->line('2. Truy cập trang Affiliate để xem thống kê')
            ->line('3. Chia sẻ link giới thiệu với bạn bè')
            ->line('4. Bắt đầu kiếm hoa hồng!')
            ->line('')
            ->line('**Hỗ trợ:**')
            ->line('Nếu bạn cần hỗ trợ, vui lòng tham gia nhóm Zalo: https://zalo.me/g/cmhxkg023')
            ->action('Truy cập trang Affiliate', url('/user/affiliate'))
            ->line('Cảm ơn bạn đã tham gia chương trình Affiliate của chúng tôi!')
            ->salutation('Trân trọng,');
    }

    public function toArray(): array
    {
        return [
            'affiliate_id' => $this->affiliate->id,
            'status' => $this->affiliate->status->value,
            'approved_at' => $this->affiliate->approved_at,
        ];
    }
}

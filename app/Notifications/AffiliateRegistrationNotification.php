<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\Affiliate;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use NotificationChannels\Telegram\TelegramMessage;

class AffiliateRegistrationNotification extends Notification implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly User $user,
        private readonly Affiliate $affiliate,
        private readonly array $requestData,
    ) {}

    public function via(): array
    {
        return ['telegram'];
    }

    public function toTelegram(): TelegramMessage
    {
        $message = "🔔 *Đăng ký Affiliate Mới*\n\n";
        $message .= "👤 *Thông tin người dùng:*\n";
        $message .= "• ID: `{$this->user->id}`\n";
        $message .= "• Tên: `{$this->user->display_name}`\n";
        $message .= "• Email: `{$this->user->email}`\n\n";

        $message .= "📋 *Thông tin affiliate:*\n";
        $message .= "• Họ tên: `{$this->requestData['full_name']}`\n";
        $message .= "• SĐT: `{$this->requestData['phone']}`\n";
        $message .= "• Email: `{$this->requestData['email']}`\n";
        $message .= "• Mã giới thiệu: `{$this->affiliate->referral_code}`\n\n";

        $message .= "📊 *Thống kê:*\n";
        $message .= "• Cấp bậc: `{$this->affiliate->tier->name}`\n";
        $message .= "• Tỷ lệ hoa hồng: `{$this->affiliate->tier->commission_rate}%`\n\n";

        $message .= "🔗 *Link giới thiệu:*\n";
        $message .= "`{$this->affiliate->referral_url}`\n\n";

        $message .= "⏰ Thời gian: `" . now()->format('d/m/Y H:i:s') . "`";
        return (new TelegramMessage())
            ->to(config('services.telegram.chat_id'))
            ->content($message);
    }

    public function toArray(): array
    {
        return [
            'user_id' => $this->user->id,
            'affiliate_id' => $this->affiliate->id,
            'request_data' => $this->requestData,
        ];
    }
}

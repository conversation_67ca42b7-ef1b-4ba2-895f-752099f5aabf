<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\Affiliate;
use App\Models\AffiliateTier;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AffiliateTierUpgraded extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        protected Affiliate $affiliate,
        protected AffiliateTier $oldTier,
        protected AffiliateTier $newTier,
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        $user = $this->affiliate->user;

        return (new MailMessage())
            ->subject('Chúc mừng! Bạn đã được tăng cấp bậc tiếp thị liên kết!')
            ->greeting("Xin chào {$user->display_name}!")
            ->line("Chúc mừng bạn! Bạn đã được tăng cấp từ {$this->oldTier->name} lên {$this->newTier->name}!")
            ->line("Cấp bậc mới: {$this->newTier->name}")
            ->line("Tỷ lệ hoa hồng mới: {$this->newTier->commission_rate}%")
            ->line("Mô tả: {$this->newTier->description}")
            ->line('Hãy tiếp tục nỗ lực để đạt được cấp bậc cao hơn!');
    }

    public function toArray(): array
    {
        return [
            'affiliate_id' => $this->affiliate->id,
            'old_tier_id' => $this->oldTier->id,
            'old_tier_name' => $this->oldTier->name,
            'new_tier_id' => $this->newTier->id,
            'new_tier_name' => $this->newTier->name,
            'new_commission_rate' => $this->newTier->commission_rate,
            'upgraded_at' => now(),
        ];
    }
}

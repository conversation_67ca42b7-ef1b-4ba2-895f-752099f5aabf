<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\Affiliate;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class AffiliateRejectedNotification extends Notification implements ShouldQueue
{
    use Dispatchable;
    use InteractsWithQueue;
    use Queueable;
    use SerializesModels;

    public function __construct(
        private readonly Affiliate $affiliate,
        private readonly string $rejectionReason,
    ) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        return (new MailMessage())
            ->subject('❌ Thông báo về đơn đăng ký Affiliate')
            ->greeting('Xin chào ' . $this->affiliate->user->display_name . '!')
            ->line('Chúng tôi rất tiếc phải thông báo rằng đơn đăng ký Affiliate của bạn **không được chấp thuận**.')
            ->line('')
            ->line('**Lý do từ chối:**')
            ->line($this->rejectionReason)
            ->line('')
            ->line('**Bạn có thể:**')
            ->line('1. Xem xét lại thông tin đã cung cấp')
            ->line('2. Cập nhật thông tin nếu cần thiết')
            ->line('3. Liên hệ hỗ trợ để được tư vấn')
            ->line('4. Đăng ký lại sau khi đã khắc phục các vấn đề')
            ->line('')
            ->line('**Hỗ trợ:**')
            ->line('Nếu bạn cần hỗ trợ hoặc có thắc mắc, vui lòng tham gia nhóm Zalo: https://zalo.me/g/cmhxkg023')
            ->action('Liên hệ hỗ trợ', 'https://zalo.me/g/cmhxkg023')
            ->line('Chúng tôi mong muốn được hỗ trợ bạn trong tương lai!')
            ->salutation('Trân trọng,');
    }

    public function toArray(): array
    {
        return [
            'affiliate_id' => $this->affiliate->id,
            'status' => $this->affiliate->status->value,
            'rejection_reason' => $this->rejectionReason,
            'rejected_at' => now(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Filament\Resources\PurchasedAccountResource;
use App\Models\PurchasedAccount;
use App\Support\Helper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramMessage;

class AccountPurchased extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public PurchasedAccount $purchasedAccount) {}

    public function via(): array
    {
        return ['mail', 'telegram'];
    }

    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage())
            ->subject('🛒 Thông Báo Mua Tài Khoản Game')
            ->greeting("Xin chào {$notifiable->display_name},")
            ->line('📢 Một tài khoản game của bạn đã được mua trên trang web của chúng tôi.')
            ->line('ℹ️ Thông tin tài khoản:')
            ->line(sprintf('🎮 Tài khoản %s #%s', $this->purchasedAccount->account->category->name, $this->purchasedAccount->getKey()))
            ->line('👤 Người mua: ' . str($this->purchasedAccount->user->display_name)->mask('*', 3))
            ->line('💰 Giá: ' . Helper::formatCurrency($this->purchasedAccount->price))
            ->line('⏰ Thời gian mua: ' . $this->purchasedAccount->created_at)
            ->action('🔍 Xem chi tiết', PurchasedAccountResource::getUrl('view', [$this->purchasedAccount]))
            ->line('Cảm ơn bạn đã sử dụng dịch vụ của chúng tôi! 🙏');
    }

    public function toTelegram(): TelegramMessage
    {
        return (new TelegramMessage())
            ->to(config('services.telegram.chat_id'))
            ->content(
                '🔔 *Thông Báo Mua Tài Khoản Game* 🔔' . "\n\n" .
                    '📢 Một tài khoản game đã được bán trên trang web.' . "\n" .
                    'ℹ️ *Thông tin tài khoản:*' . "\n" .
                    "👤 Người bán: {$this->purchasedAccount->user->display_name}" . "\n" .
                    "🎮 Tài khoản {$this->purchasedAccount->account->category->name} #{$this->purchasedAccount->getKey()}" . "\n" .
                    '💰 Giá: ' . Helper::formatCurrency($this->purchasedAccount->price) . "\n" .
                    '👤 Người mua: ' . str($this->purchasedAccount->user->display_name) . "\n" .
                    '⏰ Thời gian mua: ' . $this->purchasedAccount->created_at . "\n\n",
            )
            ->button('🔍 Xem chi tiết', PurchasedAccountResource::getUrl('view', [$this->purchasedAccount]));
    }
}

<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Filament\Resources\DepositResource;
use App\Models\Deposit;
use App\Support\Helper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramMessage;

class DepositCompleted extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public Deposit $deposit) {}

    public function via(): array
    {
        return ['telegram'];
    }

    public function toTelegram(): TelegramMessage
    {
        $deposit = $this->deposit;

        return (new TelegramMessage())
            ->to(config('services.telegram.chat_id'))
            ->line('🔔 *Thông báo Nạp tiền* 🔔')
            ->line(sprintf(
                '👤 *%s* vừa nạp *%s* vào tài khoản',
                $deposit->user->display_name,
                Helper::formatCurrency($deposit->amount),
            ))
            ->line(sprintf('💳 *Phương thức*: %s', $deposit->source_name))
            ->lineIf(!empty($deposit->content), sprintf('📝 *Nội dung*: %s', $deposit->content))
            ->line(sprintf('⏰ *Thời gian*: %s', $deposit->transacted_at))
            ->button('Xem chi tiết', DepositResource::getUrl('view', [$deposit]));
    }
}

<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Filament\Resources\ItemWithdrawResource;
use App\Models\ItemWithdraw;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Notification;
use NotificationChannels\Telegram\TelegramMessage;

class GameItemWithdrawn extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(public ItemWithdraw $withdraw) {}

    public function via(): array
    {
        return ['telegram'];
    }

    public function toTelegram(): TelegramMessage
    {
        return (new TelegramMessage())
            ->to(config('services.telegram.chat_id'))
            ->line('🔔 *THÔNG BÁO ĐƠN RÚT VẬT PHẨM* 🔔')
            ->line('Người dùng: ' . $this->withdraw->gameItem->name)
            ->line('Vật phẩm: ' . $this->withdraw->gameItem->name)
            ->line('S<PERSON> lượng: ' . number_format($this->withdraw->quantity))
            ->line('Thời gian: ' . $this->withdraw->created_at)
            ->button('Xem chi tiết', ItemWithdrawResource::getUrl('view', [$this->withdraw]));
    }
}

<?php

declare(strict_types=1);

namespace App\Notifications;

use App\Models\AffiliateCommission;
use App\Support\Helper;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class AffiliateCommissionCreated extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(protected AffiliateCommission $commission) {}

    public function via(): array
    {
        return ['mail'];
    }

    public function toMail(): MailMessage
    {
        $affiliate = $this->commission->affiliate;

        return (new MailMessage())
            ->subject('Bạn có hoa hồng mới!')
            ->greeting("Xin chào {$affiliate->user->display_name}!")
            ->line("Bạn vừa có một hoa hồng mới từ đơn hàng #{$this->commission->purchased_account_id}.")
            ->line("Số tiền hoa hồng: " . Helper::formatCurrency((int) $this->commission->commission_amount))
            ->line("Tỷ lệ hoa hồng: {$this->commission->commission_rate}%")
            ->line("Trạng thái: Chờ duyệt")
            ->line('Cảm ơn bạn đã tham gia chương trình tiếp thị liên kết của chúng tôi!');
    }

    public function toArray(): array
    {
        return [
            'commission_id' => $this->commission->id,
            'amount' => $this->commission->commission_amount,
            'purchased_account_id' => $this->commission->purchased_account_id,
            'status' => $this->commission->status,
        ];
    }
}

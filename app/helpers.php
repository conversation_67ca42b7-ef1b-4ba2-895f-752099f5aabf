<?php

declare(strict_types=1);

use App\Services\ColorService;
use App\Settings\AppearanceSettings;

if (! function_exists('get_primary_color_variables')) {
    function get_primary_color_variables(string $hex, ?string $textColor = null): array
    {
        return ColorService::generatePrimaryColorVariables($hex, $textColor);
    }
}

if (! function_exists('get_contrast_color')) {
    function get_contrast_color(string $hex): string
    {
        return ColorService::getContrastColor($hex);
    }
}

if (! function_exists('hex_to_hsl')) {
    function hex_to_hsl(string $hex): string
    {
        return ColorService::hexToHslString($hex);
    }
}

if (! function_exists('get_container_css_variables')) {
    function get_container_css_variables(): array
    {
        try {
            $appearanceSettings = app(AppearanceSettings::class);

            $maxWidthMap = [
                'max-w-none' => 'none',
                'max-w-5xl' => '1024px',
                'max-w-6xl' => '1152px',
                'max-w-7xl' => '1280px',
                'max-w-full' => '100%',
                'max-w-screen-sm' => '640px',
                'max-w-screen-md' => '768px',
                'max-w-screen-lg' => '1024px',
                'max-w-screen-xl' => '1280px',
                'max-w-screen-2xl' => '1536px',
            ];

            $maxWidth = $maxWidthMap[$appearanceSettings->container_max_width] ?? '1280px';

            return [
                '--appearance-container-max-width' => $maxWidth,
            ];
        } catch (Exception $e) {
            return [
                '--appearance-container-max-width' => '1280px',
            ];
        }
    }
}

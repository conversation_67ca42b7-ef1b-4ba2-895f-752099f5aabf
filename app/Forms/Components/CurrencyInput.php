<?php

declare(strict_types=1);

namespace App\Forms\Components;

use Filament\Forms\Components\TextInput;
use Filament\Support\RawJs;

class CurrencyInput extends TextInput
{
    public static function make(string $name): static
    {
        return parent::make($name)
            ->numeric()
            ->mask(RawJs::make("\$money(\$input)"))
            ->mutateStateForValidationUsing(fn($state): string => str_replace(',', '', (string) $state))
            ->mutateDehydratedStateUsing(function ($state): ?int {
                $state = str_replace(',', '', (string) $state);

                if ($state === '') {
                    return null;
                }

                return (int) $state;
            })
            ->maxValue(2_147_483_647)
            ->prefix('₫');
    }
}

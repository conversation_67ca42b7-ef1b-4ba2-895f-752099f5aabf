<?php

declare(strict_types=1);

namespace App\Mail;

use App\Models\Ad;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class AdContentMail extends Mailable implements ShouldQueue
{
    use Queueable;
    use SerializesModels;

    public function __construct(public Ad $ad) {}

    public function envelope(): Envelope
    {
        return new Envelope(
            subject: $this->ad->name,
        );
    }

    public function content(): Content
    {
        return new Content(
            markdown: 'mail.ad-content',
            with: [
                'ad' => $this->ad,
            ],
        );
    }
}

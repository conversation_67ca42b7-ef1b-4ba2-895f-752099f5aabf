<?php

declare(strict_types=1);

namespace App\RechargeDrivers;

use App\Contracts\RechargeDriver;
use App\Settings\DepositSettings;
use Illuminate\Http\Client\PendingRequest;
use Illuminate\Support\Facades\Http;
use RuntimeException;

abstract class AbstractDriver implements RechargeDriver
{
    public const BASE_URL = '';

    public function __construct(protected DepositSettings $settings) {}

    abstract public function isSetupCredentials(): bool;

    public function request(): PendingRequest
    {
        if (! $this->isSetupCredentials()) {
            throw new RuntimeException(
                __('Recharge provider credentials are missing. Please configure your settings before attempting a recharge.'),
            );
        }

        return Http::baseUrl(static::BASE_URL);
    }
}

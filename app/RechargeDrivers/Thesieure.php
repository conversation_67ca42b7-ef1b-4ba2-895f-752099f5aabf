<?php

declare(strict_types=1);

namespace App\RechargeDrivers;

use App\DataTransferObjects\RechargeResponse;
use App\Support\Helper;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class Thesieure extends AbstractDriver
{
    public const BASE_URL = 'https://thesieure.com';

    public function isSetupCredentials(): bool
    {
        return $this->getPartnerId() && $this->getPartnerKey();
    }

    public function charge(
        string $telecom,
        int $amount,
        string $code,
        ?string $serial = null,
    ): RechargeResponse {
        $response = $this->request()->post('chargingws/v2', [
            'telco' => $telecom,
            'code' => $code,
            'serial' => $serial,
            'amount' => $amount,
            'request_id' => Str::random(),
            'partner_id' => $this->getPartnerId(),
            'sign' => $this->generateSignature($code, $serial),
            'command' => 'charging',
        ]);

        return $this->formatResponseData($response->json());
    }

    public function getFees(): Collection
    {
        $response = $this->request()->get('chargingws/v2/getfee', ['partner_id' => $this->getPartnerId()]);

        return collect($response->json())
            ->groupBy('telco')
            ->map(fn(Collection $telecom) => $telecom->transform(fn($item) => [
                ...$item,
                'formatted_value' => Helper::formatCurrency($item['value']),
            ]));
    }

    public function generateSignature(string $code, string $serial): string
    {
        return md5(sprintf('%s%s%s', $this->getPartnerKey(), $code, $serial));
    }

    protected function formatResponseData(array $data): RechargeResponse
    {
        return new RechargeResponse(
            Arr::get($data, 'status'),
            Arr::get($data, 'request_id'),
            Arr::get($data, 'trans_id'),
            Arr::get($data, 'amount'),
            Arr::get($data, 'message'),
        );
    }

    protected function getPartnerId(): ?string
    {
        return $this->settings->thesieure_partner_id;
    }

    protected function getPartnerKey(): ?string
    {
        return $this->settings->thesieure_partner_key;
    }
}

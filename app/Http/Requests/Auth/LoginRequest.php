<?php

declare(strict_types=1);

namespace App\Http\Requests\Auth;

use App\Models\User;
use Illuminate\Auth\Events\Lockout;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class LoginRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'username' => ['required', 'string'],
            'password' => ['required', 'string'],
        ];
    }

    public function authenticate(): void
    {
        $this->ensureIsNotRateLimited();

        if (! Auth::attempt($this->credentials(), $this->boolean('remember'))) {
            RateLimiter::hit($this->throttleKey());

            if (! User::query()->where($this->username(), $this->input('username'))->exists()) {
                throw ValidationException::withMessages([
                    'username' => '<PERSON><PERSON><PERSON> kho<PERSON>n không tồn tại trong hệ thống.',
                ]);
            }

            throw ValidationException::withMessages([
                'password' => '<PERSON><PERSON><PERSON> khẩu không chính xác.',
            ]);
        }

        RateLimiter::clear($this->throttleKey());
    }

    public function ensureIsNotRateLimited(): void
    {
        if (! RateLimiter::tooManyAttempts($this->throttleKey(), 5)) {
            return;
        }

        event(new Lockout($this));

        $seconds = RateLimiter::availableIn($this->throttleKey());

        throw ValidationException::withMessages([
            'username' => trans('auth.throttle', [
                'seconds' => $seconds,
                'minutes' => ceil($seconds / 60),
            ]),
        ]);
    }

    public function throttleKey(): string
    {
        return Str::transliterate(Str::lower($this->input('username')) . '|' . $this->ip());
    }

    public function username(): string
    {
        return filter_var($this->input('username'), FILTER_VALIDATE_EMAIL) ? 'email' : 'username';
    }

    public function credentials(): array
    {
        return [
            $this->username() => $this->input('username'),
            'password' => $this->input('password'),
        ];
    }
}

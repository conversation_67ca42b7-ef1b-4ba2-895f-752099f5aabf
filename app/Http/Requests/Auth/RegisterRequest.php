<?php

declare(strict_types=1);

namespace App\Http\Requests\Auth;

use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;

class RegisterRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'name' => ['required', 'string', 'max:255'],
            'username' => ['required', 'string', 'lowercase', 'max:255', Rule::unique(User::class)],
            'email' => ['required', 'string', 'email', 'lowercase', 'max:255', Rule::unique(User::class)],
            'password' => ['required', 'confirmed', Password::defaults()->min(6)],
        ];
    }
}

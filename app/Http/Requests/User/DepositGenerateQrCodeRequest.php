<?php

declare(strict_types=1);

namespace App\Http\Requests\User;

use App\Settings\DepositSettings;
use Illuminate\Foundation\Http\FormRequest;

class DepositGenerateQrCodeRequest extends FormRequest
{
    public function rules(): array
    {
        $depositSettings = app(DepositSettings::class);

        return [
            'amount' => [
                'required',
                'integer',
                'min:' . $depositSettings->auto_pay_min_amount,
                'max:' . $depositSettings->auto_pay_max_amount,
                function ($attribute, $value, $fail): void {
                    if ($value % 1000 !== 0) {
                        $fail("Trường :attribute phải là bội số của 1,000.");
                    }
                }],
        ];
    }

    public function attributes(): array
    {
        return [
            'amount' => 'số tiền',
        ];
    }
}

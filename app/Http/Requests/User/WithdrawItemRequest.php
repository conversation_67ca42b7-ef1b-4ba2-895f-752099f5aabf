<?php

declare(strict_types=1);

namespace App\Http\Requests\User;

use App\Enums\CustomFieldType;
use App\Models\CustomField;
use App\Models\GameItem;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Arr;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;

class WithdrawItemRequest extends FormRequest
{
    /**
     * @var Collection<CustomField>
     */
    protected Collection $customFields;

    public function rules(): array
    {
        $gameItem = GameItem::query()
            ->with(['userItem', 'customFields'])
            ->findOrFail($this->route('gameItem'));

        if (empty($gameItem->withdraw_packages)) {
            throw ValidationException::withMessages([
                'value' => ['Vật phẩm này không thể rút'],
            ]);
        }

        if ($gameItem->userItem?->quantity < $this->integer('value')) {
            throw ValidationException::withMessages([
                'value' => ['Số lượng vật phẩm của bạn không đủ để rút'],
            ]);
        }

        $this->customFields ??= $gameItem->customFields;

        $rules = [
            'value' => [
                'required',
                'integer',
                Rule::in(Arr::pluck($gameItem->withdraw_packages, 'value')),
            ],
            'custom_fields' => ['array'],
        ];

        foreach ($this->customFields as $customField) {
            $rules["custom_fields.{$customField->getKey()}"] = [Rule::when($customField->required, 'required', 'nullable')];

            match ($customField->type) {
                CustomFieldType::Text => $rules["custom_fields.{$customField->getKey()}"][] = 'string',
                CustomFieldType::Number => $rules["custom_fields.{$customField->getKey()}"][] = 'numeric',
                CustomFieldType::Select => $rules["custom_fields.{$customField->getKey()}"][] = ['string', Rule::in($customField->options)],
            };
        }

        return $rules;
    }

    public function attributes(): array
    {
        $attributes = parent::attributes();

        foreach ($this->customFields as $customField) {
            $attributes["custom_fields.{$customField->getKey()}"] = $customField->name;
        }

        return $attributes;
    }
}

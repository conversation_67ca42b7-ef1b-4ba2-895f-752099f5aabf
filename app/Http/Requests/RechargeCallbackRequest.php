<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class RechargeCallbackRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'status' => ['required', 'integer'],
            'request_id' => ['required', 'string'],
            'amount' => ['required', 'numeric'],
            'serial' => ['required', 'string'],
            'code' => ['required', 'string'],
            'telco' => ['required', 'string'],
            'trans_id' => ['required'],
            'callback_sign' => ['required', 'string'],
        ];
    }
}

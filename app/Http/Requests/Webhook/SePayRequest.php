<?php

declare(strict_types=1);

namespace App\Http\Requests\Webhook;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class SePayRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'id' => ['required', 'integer'],
            'content' => ['required', 'string'],
            'gateway' => ['required', 'string'],
            'transferAmount' => ['required', 'integer'],
            'transferType' => ['required', 'string', Rule::in(['in', 'out'])],
            'accountNumber' => ['required', 'string'],
            'transactionDate' => ['required', 'date'],
        ];
    }
}

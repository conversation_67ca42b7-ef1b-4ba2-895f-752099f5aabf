<?php

declare(strict_types=1);

namespace App\Http\Requests\Webhook;

use Illuminate\Foundation\Http\FormRequest;

class Web2MRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'status' => ['required', 'boolean'],
            'data' => ['required', 'array'],
            'data.*.id' => ['required', 'string'],
            'data.*.type' => ['required', 'string', 'in:IN,OUT'],
            'data.*.transactionID' => ['required', 'string'],
            'data.*.amount' => ['required', 'string'],
            'data.*.description' => ['required', 'string'],
            'data.*.date' => ['nullable', 'string'],
            'data.*.bank' => ['required', 'string'],
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\FlashSale
 */
class FlashSaleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'start_at' => $this->start_at,
            'end_at' => $this->end_at,
            'accounts' => FlashSaleAccountResource::collection($this->whenLoaded('accounts')),
        ];
    }
}

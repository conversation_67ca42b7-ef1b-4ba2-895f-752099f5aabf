<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Support\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\ItemWithdraw
 */
class WithdrawItemResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->getKey(),
            'created_at' => Helper::formatDateTime($this->created_at),
            'game_item' => $this->whenLoaded('gameItem', fn(): \App\Http\Resources\GameItemResource => new GameItemResource($this->gameItem)),
            'quantity' => $this->quantity,
            'status' => $this->status->getLabel(),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin \App\Models\Wheel
 */
class WheelResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'slug' => $this->slug,
            'price' => $this->price,
            'thumbnail' => Storage::url($this->thumbnail),
            'image' => Storage::url($this->image),
            'wheel_spins' => $this->whenLoaded('wheelSpins', fn() => WheelSpinResource::collection($this->wheelSpins)),
        ];
    }
}

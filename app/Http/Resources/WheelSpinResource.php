<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\WheelSpin
 */
class WheelSpinResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user' => $this->whenLoaded('user', fn(): \App\Http\Resources\UserResource => new UserResource($this->user)),
            'wheel' => $this->whenLoaded('wheel', fn(): \App\Http\Resources\WheelResource => new WheelResource($this->wheel)),
            'price' => $this->price,
            'result' => $this->result,
            'created_at' => $this->created_at->diffForHumans(),
        ];
    }
}

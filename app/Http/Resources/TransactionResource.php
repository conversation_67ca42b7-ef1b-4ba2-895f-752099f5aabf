<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Support\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\Transaction
 */
class TransactionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->getKey(),
            'type' => $this->type->getLabel(),
            'amount' => $this->amount,
            'balance' => $this->balance,
            'formatted_amount' => Helper::formatCurrency($this->amount),
            'formatted_balance' => Helper::formatCurrency($this->balance),
            'is_positive' => $this->type->isPositive(),
            'description' => $this->description,
            'created_at' => Helper::formatDateTime($this->created_at),
        ];
    }
}

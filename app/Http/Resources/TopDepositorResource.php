<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Models\User;
use App\Support\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @property string $total_amount
 * @property User $user
 */
class TopDepositorResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'amount' => Helper::formatCurrency((int) $this->total_amount),
            'user_display_name' => $this->user?->display_name ?: 'Không có tên',
            'user_avatar' => $this->user?->avatar_url ?: null,
        ];
    }
}

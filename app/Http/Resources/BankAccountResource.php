<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin \App\Models\BankAccount
 */
class BankAccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'bank_name' => $this->bank_name,
            'account_number' => $this->account_number,
            'account_holder' => $this->account_holder,
            'logo' => $this->logo ? Storage::url($this->logo) : null,
        ];
    }
}

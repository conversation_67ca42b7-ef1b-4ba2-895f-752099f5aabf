<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Support\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Str;

/**
 * @mixin \App\Models\Recharge
 */
class RechargeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => "#{$this->getKey()}",
            'type' => $this->type,
            'serial' => $this->serial,
            'pin' => Str::mask($this->pin, '*', -6),
            'status' => $this->status->getLabel(),
            'declared_amount' => Helper::formatCurrency($this->declared_amount),
            'amount' => Helper::formatCurrency($this->amount ?: 0),
            'created_at' => Helper::formatDateTime($this->created_at),
        ];
    }
}

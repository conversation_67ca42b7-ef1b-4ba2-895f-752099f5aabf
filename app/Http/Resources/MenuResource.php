<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \Datlechin\FilamentMenuBuilder\Models\MenuItem
 */
class MenuResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $linkable = $this->linkable;

        return [
            'title' => $this->title,
            'url' => $linkable ? $linkable->getMenuPanelUrlUsing()($linkable) : $this->url,
            'order' => $this->order,
            'target' => $this->target,
            'children' => MenuResource::collection($this->whenLoaded('children')),
        ];
    }
}

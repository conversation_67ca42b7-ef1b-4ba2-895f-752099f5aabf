<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Enums\DiscountType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\FlashSaleAccount
 */
class FlashSaleAccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'account' => new AccountResource($this->account),
            'price' => $this->type === DiscountType::Fixed
                ? $this->value
                : round($this->account->price - ($this->account->price * $this->value / 100)),
            'discount_percent' => round($this->type === DiscountType::Fixed
                ? (($this->account->original_price - $this->value) / $this->account->original_price) * 100
                : $this->value),
        ];
    }
}

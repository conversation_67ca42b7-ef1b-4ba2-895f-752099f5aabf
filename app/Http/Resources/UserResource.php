<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Support\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\User
 */
class UserResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $this->loadMissing('gameItems');

        return [
            'id' => $this->id,
            'display_name' => $this->display_name,
            'username' => $this->username,
            'name' => $this->name,
            'email' => $this->email,
            'email_verified_at' => $this->email_verified_at,
            'balance' => $this->balance,
            'formatted_balance' => Helper::formatCurrency($this->balance),
            'avatar_url' => $this->getAttribute('avatar_url'),
            'created_at' => $this->created_at->toDateTimeString(),
            'game_items' => GameItemResource::collection($this->whenLoaded('gameItems')),
        ];
    }
}

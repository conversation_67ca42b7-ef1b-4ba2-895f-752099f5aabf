<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\AccountAttribute
 */
class AccountAttributeResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'attribute_set' => AttributeSetResource::make($this->whenLoaded('attributeSet')),
            'attribute' => AttributeResource::make($this->whenLoaded('attribute')),
            'display_value' => $this->getDisplayValue(),
            'value' => $this->value,
        ];
    }
}

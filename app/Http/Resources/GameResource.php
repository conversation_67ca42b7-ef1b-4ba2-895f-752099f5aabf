<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin \App\Models\Game
 */
class GameResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image' => $this->image ? Storage::url($this->image) : null,
            'is_visible' => $this->is_visible,
            'categories_count' => $this->whenCounted('categories'),
            'categories' => AccountCategoryResource::collection($this->whenLoaded('categories')),
            'publisher' => [
                'name' => $this->whenLoaded('publisher', fn() => $this->publisher->name),
                'slug' => $this->whenLoaded('publisher', fn() => $this->publisher->slug),
            ],
        ];
    }
}

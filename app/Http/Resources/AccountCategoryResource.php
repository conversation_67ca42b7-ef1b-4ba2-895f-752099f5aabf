<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin \App\Models\AccountCategory
 */
class AccountCategoryResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image' => $this->image ? Storage::url($this->image) : null,
            'parent_id' => (int) $this->parent_id,
            'remaining_accounts_count' => (int) $this->remaining_accounts_count,
            'sold_accounts_count' => (int) $this->sold_accounts_count,
            'accounts' => AccountResource::collection($this->whenLoaded('accounts')),
            'parent' => AccountCategoryResource::make($this->whenLoaded('parent')),
            'children' => AccountCategoryResource::collection($this->whenLoaded('children')),
            'children_count' => $this->whenCounted('children'),
            'game' => GameResource::make($this->whenLoaded('game')),
        ];
    }
}

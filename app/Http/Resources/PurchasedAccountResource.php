<?php

declare(strict_types=1);

namespace App\Http\Resources;

use App\Support\Helper;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\PurchasedAccount
 */
class PurchasedAccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->account->getKey(),
            'acc_name' => $this->account->acc_name,
            'acc_pass' => $this->account->acc_pass,
            'price' => Helper::formatCurrency($this->price),
            'created_at' => Helper::formatDateTime($this->created_at),
            'seen_at' => $this->seen_at ? Helper::formatDateTime($this->seen_at) : null,
            'category' => new AccountCategoryResource($this->account->category),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Jenssegers\Agent\Agent;

/**
 * @mixin \App\Models\AuthenticationLog
 */
class AuthenticationLogResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        $agent = tap(new Agent(), fn($agent) => $agent->setUserAgent($this->user_agent));

        return [
            'ip_address' => $this->ip_address,
            'browser' => sprintf('%s - %s', $agent->platform(), $agent->browser()),
            'location' => $this->location ? "{$this->location['city']}, {$this->location['state']}, {$this->location['country']}" : '-',
            'login_at' => $this->login_at?->diffForHumans() ?: '-',
        ];
    }
}

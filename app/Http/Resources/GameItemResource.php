<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin \App\Models\GameItem
 */
class GameItemResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'quantity' => $this->whenLoaded('userItem', fn() => $this->userItem->quantity, 0),
            'formatted_quantity' => sprintf('%s %s', number_format($this->whenLoaded('userItem', fn() => $this->userItem->quantity, 0) ?? 0), $this->name),
            'withdraw_packages' => $this->withdraw_packages,
            'custom_fields' => $this->whenLoaded('customFields', fn() => CustomFieldResource::collection($this->customFields)),
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Storage;

/**
 * @mixin \App\Models\Account
 */
class AccountResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'description' => $this->description,
            'price' => $this->final_price,
            'original_price' => $this->original_price,
            'image' => $this->image,
            'images' => array_map(fn(string $image) => Storage::url($image), $this->images),
            'attributes' => AttributeResource::collection($this->whenLoaded('attributes')),
            'category' => AccountCategoryResource::make($this->whenLoaded('category')),
            'content' => $this->content,
            'status' => $this->status,
        ];
    }
}

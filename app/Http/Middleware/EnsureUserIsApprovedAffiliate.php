<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class EnsureUserIsApprovedAffiliate
{
    public function handle(Request $request, Closure $next): Response
    {
        $user = Auth::user();

        if (! $user) {
            return redirect()->route('login');
        }

        $affiliate = Affiliate::query()
            ->where('user_id', $user->id)
            ->first();

        if (! $affiliate) {
            return redirect()->route('user.affiliate.index');
        }

        if ($affiliate->status !== AffiliateStatus::Approved) {
            return redirect()->route('user.affiliate.index');
        }

        return $next($request);
    }
}

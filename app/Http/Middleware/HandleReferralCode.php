<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Actions\HandleReferralCodeAction;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class HandleReferralCode
{
    public function __construct(protected HandleReferralCodeAction $handleReferralCodeAction) {}

    public function handle(Request $request, Closure $next): Response
    {
        if ($request->is('*') && ! $request->is('admin/*') && ! $request->is('affiliate/*')) {
            if ($request->isMethod('GET') && ! $request->ajax()) {
                ($this->handleReferralCodeAction)($request);
            }
        }

        return $next($request);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Settings\DepositSettings;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyWebhookToken
{
    public function handle(Request $request, Closure $next): Response
    {
        if (! $request->acceptsJson() || ! $request->hasHeader('Authorization')) {
            abort(404);
        }

        $depositSettings = app(DepositSettings::class);
        $token = str($request->header('Authorization'))
            ->after('Apikey ')
            ->toString();

        if ($token !== $depositSettings->auto_pay_webhook_auth_token) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Http\Resources\MenuResource;
use App\Http\Resources\UserResource;
use App\Services\AdService;
use App\Settings\GeneralSettings;
use App\Settings\SocialLoginSettings;
use Datlechin\FilamentMenuBuilder\Models\Menu;
use Filament\Facades\Filament;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Inertia\Middleware;
use Tabuna\Breadcrumbs\Breadcrumbs;

class HandleInertiaRequests extends Middleware
{
    protected $rootView = 'layouts.app';

    public function share(Request $request): array
    {
        return [
            ...parent::share($request),
            'app' => function () use ($request): array {
                $generalSettings = app(GeneralSettings::class);
                $socialLoginSettings = app(SocialLoginSettings::class);
                $user = $request->user();
                $menu = Menu::query()
                    ->where('is_visible', true)
                    ->whereRelation('locations', 'location', 'header')
                    ->with('menuItems')
                    ->first();

                return [
                    'csrf_token' => csrf_token(),
                    'menu' => $menu ? MenuResource::collection($menu->menuItems) : null,
                    'env' => app()->environment(),
                    'name' => $generalSettings->site_name,
                    'title' => $generalSettings->site_title,
                    'logo' => $generalSettings->logo ? Storage::url($generalSettings->logo) : null,
                    'admin_panel_url' => fn() => $user && $user->hasAnyRole('super_admin', 'collaborator') ? Filament::getUrl() : null,
                    'social_login' => [
                        'google' => $socialLoginSettings->google_enabled,
                        'facebook' => $socialLoginSettings->facebook_enabled,
                        'zalo' => $socialLoginSettings->zalo_enabled,
                    ],
                ];
            },
            'auth' => fn(): array => [
                'user' => $request->user() ? UserResource::make($request->user()) : null,
                'affiliate' => $request->user()?->affiliate?->only('id', 'status'),
            ],
            'flash' => fn(): array => [
                'success' => $request->session()->get('success'),
                'error' => $request->session()->get('error'),
            ],
            'breadcrumbs' => fn() => $request->routeIs('home') ? null : Breadcrumbs::current(),
            'ads' => function () use ($request): array {
                $adService = app(AdService::class);
                return $adService->getAllAdsByPositions();
            },
        ];
    }
}

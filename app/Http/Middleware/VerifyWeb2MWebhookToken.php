<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Settings\DepositSettings;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class VerifyWeb2MWebhookToken
{
    public function handle(Request $request, Closure $next): Response
    {
        if (! $request->acceptsJson() || ! $request->hasHeader('Authorization')) {
            abort(404);
        }

        $depositSettings = app(DepositSettings::class);
        $token = str($request->header('Authorization'))
            ->after('Bearer ')
            ->toString();

        if ($token !== $depositSettings->web2m_webhook_auth_token) {
            return response()->json(['error' => 'Unauthorized'], 401);
        }

        return $next($request);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\Webhook;

use App\Actions\HandleWeb2MWebhookAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Webhook\Web2MRequest;
use Illuminate\Http\JsonResponse;

class Web2MController extends Controller
{
    public function __invoke(
        Web2MRequest $request,
        HandleWeb2MWebhookAction $handleWeb2MWebhookAction,
    ): JsonResponse {
        $handleWeb2MWebhookAction($request);

        return response()->json([
            'status' => true,
            'msg' => 'Ok',
        ]);
    }
}

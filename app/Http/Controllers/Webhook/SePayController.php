<?php

declare(strict_types=1);

namespace App\Http\Controllers\Webhook;

use App\Actions\HandleSePayWebhookAction;
use App\Http\Controllers\Controller;
use App\Http\Requests\Webhook\SePayRequest;
use Illuminate\Http\JsonResponse;

class SePayController extends Controller
{
    public function __invoke(
        SePayRequest $request,
        HandleSePayWebhookAction $handleSePayWebhookAction,
    ): JsonResponse {
        $handleSePayWebhookAction($request);

        return response()->json(['success' => true]);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Actions\ApplyAccountDiscountCode;
use App\Actions\PurchaseAccountAction;
use App\Enums\AccountStatus;
use App\Http\Requests\PurchaseAccountRequest;
use App\Http\Resources\AccountResource;
use App\Http\Resources\FlashSaleResource;
use App\Models\Account;
use App\Models\Discount;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Illuminate\Validation\ValidationException;
use Inertia\Inertia;
use Inertia\Response;

class AccountController extends Controller
{
    public function show(string $id): Response
    {
        $account = Account::query()
            ->whereIn('status', [AccountStatus::Selling, AccountStatus::Sold])
            ->with([
                'category',
                'attributes.attributeSet',
                'flashSales' => fn(BelongsToMany $query) => $query->available(),
            ])
            ->findOrFail($id);

        $flashSale = $account->flashSales->first();

        $relatedAccounts = Account::query()
            ->whereNot('id', $account->getKey())
            ->withWhereHas('category', fn($query) => $query->whereKey($account->category->getKey()))
            ->with([
                'attributes.attributeSet',
                'flashSales' => fn(BelongsToMany $query) => $query->available(),
            ])
            ->where('status', AccountStatus::Selling)
            ->take(12)
            ->inRandomOrder()
            ->get();

        return Inertia::render('Accounts/Show', [
            'account' => new AccountResource($account),
            'flashSale' => $flashSale ? new FlashSaleResource($flashSale) : null,
            'relatedAccounts' => AccountResource::collection($relatedAccounts),
        ]);
    }

    public function purchase(
        string $id,
        PurchaseAccountRequest $request,
        PurchaseAccountAction $purchaseAccountAction,
    ): RedirectResponse {
        $account = Account::query()
            ->where('status', AccountStatus::Selling)
            ->with(['category', 'flashSales' => fn(BelongsToMany $query) => $query->available()])
            ->findOrFail($id);

        try {
            $purchaseAccountAction($request->user(), $account, $request->input('discount_code'));
        } catch (Exception $e) {
            return back()->with('error', $e->getMessage());
        }

        return to_route('user.purchased-accounts.index')
            ->with('success', "Mua tài khoản {$account->category->name} #$id thành công!");
    }

    public function applyDiscount(string $id, Request $request, ApplyAccountDiscountCode $applyAccountDiscountCode)
    {
        $account = Account::query()
            ->where('status', AccountStatus::Selling)
            ->findOrFail($id);

        $validation = Validator::make($request->all(), [
            'code' => ['required', Rule::exists(Discount::class, 'code')],
        ]);

        if ($validation->fails()) {
            return response()->json($validation->errors(), 422);
        }

        try {
            $discountAmount = $applyAccountDiscountCode($account, $request->input('code'));
        } catch (ValidationException $e) {
            return response()->json([
                'message' => $e->getMessage(),
            ], 422);
        }

        $discountedAmount = max(0, $account->final_price - $discountAmount);

        return response()->json([
            'data' => [
                'total_amount' => $discountedAmount,
                'discount_amount' => $discountAmount > $account->final_price ? $account->final_price : $discountAmount,
            ],
        ]);
    }
}

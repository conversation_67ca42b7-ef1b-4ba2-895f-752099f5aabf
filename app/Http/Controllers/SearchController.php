<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Http\Requests\SearchRequest;
use App\Services\Search\SearchService;
use Illuminate\Http\JsonResponse;

class SearchController extends Controller
{
    public function __construct(
        private readonly SearchService $searchService,
    ) {}

    public function search(SearchRequest $request): JsonResponse
    {
        $query = $request->validated('query');
        $limit = $request->integer('limit', 20);

        $results = $this->searchService->search($query, $limit);

        return response()->json([
            'success' => true,
            'data' => [
                'query' => $query,
                'results' => $results->map(fn($result) => $result->toArray()),
                'total' => $results->count(),
            ],
        ]);
    }
}

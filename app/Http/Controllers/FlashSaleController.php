<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\AccountStatus;
use App\Http\Resources\FlashSaleResource;
use App\Models\FlashSale;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class FlashSaleController extends Controller
{
    public function index(Request $request): Response
    {
        $query = FlashSale::query()
            ->with([
                'accounts.account',
                'accounts.account.attributes.attributeSet',
                'accounts.account.category',
                'accounts.account.flashSales' => fn(BelongsToMany $query) => $query->available(),
            ])
            ->whereHas('accounts', function (Builder $query) {
                $query->whereHas('account', function (Builder $accountQuery) {
                    $accountQuery->where('status', AccountStatus::Selling);
                });
            });

        // Filter theo trạng thái
        $filter = $request->get('filter', 'all');
        
        switch ($filter) {
            case 'available':
                $query->available();
                break;
            case 'ending_soon':
                $query->endingSoon(24); // Flash sale sắp hết hạn trong 24 giờ
                break;
            case 'upcoming':
                $query->upcoming();
                break;
            case 'all':
            default:
                $query->where('is_active', true);
                break;
        }

        // Sắp xếp theo thời gian bắt đầu mới nhất
        $query->orderBy('start_at', 'desc');

        $flashSales = $query->paginate(12);

        return Inertia::render('FlashSales/Index', [
            'flashSales' => FlashSaleResource::collection($flashSales),
            'filters' => [
                'filter' => $filter,
            ],
        ]);
    }

    public function show(FlashSale $flashSale): Response
    {
        $flashSale->load([
            'accounts.account',
            'accounts.account.attributes.attributeSet',
            'accounts.account.category',
            'accounts.account.flashSales' => fn(BelongsToMany $query) => $query->available(),
        ]);

        return Inertia::render('FlashSales/Show', [
            'flashSale' => new FlashSaleResource($flashSale),
        ]);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\AccountStatus;
use App\Enums\DepositStatus;
use App\Http\Resources\AccountCategoryResource;
use App\Http\Resources\FlashSaleResource;
use App\Http\Resources\GameResource;
use App\Http\Resources\TopDepositorResource;
use App\Http\Resources\WheelResource;
use App\Models\Deposit;
use App\Models\AccountCategory;
use App\Models\FlashSale;
use App\Models\Game;
use App\Models\Wheel;
use App\Settings\GeneralSettings;
use App\Settings\HomeSettings;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class HomeController extends Controller
{
    public function __invoke(GeneralSettings $generalSettings, HomeSettings $homeSettings): Response
    {
        $categories = AccountCategory::query()
            ->where('is_visible', true)
            ->orderBy('order')
            ->withCount([
                'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
            ])
            ->with('children', function (HasMany $query) {
                $query
                    ->orderBy('order')
                    ->withCount([
                        'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                        'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
                    ])
                    ->with('children', function (HasMany $query) {
                        $query
                            ->orderBy('order')
                            ->withCount([
                                'accounts as remaining_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Selling),
                                'accounts as sold_accounts_count' => fn(Builder $query) => $query->where('status', AccountStatus::Sold),
                            ]);
                    });
            })
            ->get();

        $games = Game::query()
            ->where('is_visible', true)
            ->withCount('categories')
            ->get();

        $wheels = Wheel::query()
            ->where('is_visible', true)
            ->get();

        $bannerSliders = collect($generalSettings->banner_sliders)
            ->transform(fn($slider) => [
                ...$slider,
                'image' => Storage::url($slider['image']),
            ]);

        $flashSale = FlashSale::query()
            ->with([
                'accounts.account',
                'accounts.account.attributes.attributeSet',
                'accounts.account.category',
                'accounts.account.flashSales' => fn(BelongsToMany $query) => $query->available(),
            ])
            ->available()
            ->whereHas('accounts', function (Builder $query) {
                $query->whereHas('account', function (Builder $accountQuery) {
                    $accountQuery->where('status', AccountStatus::Selling);
                });
            })
            ->first();

        $topDepositors = Deposit::query()
            ->select('user_id', DB::raw('SUM(amount) as total_amount'))
            ->where('status', DepositStatus::Completed)
            ->whereDate('created_at', '>=', now()->subMonth())
            ->groupBy('user_id')
            ->orderByDesc('total_amount')
            ->limit(5)
            ->with('user')
            ->get();

        $homeSectionsOrder = $homeSettings->home_sections_order ?? [
            ['section' => 'banner_sliders', 'is_visible' => true],
            ['section' => 'ad_home_banner', 'is_visible' => true],
            ['section' => 'flash_sale', 'is_visible' => true],
            ['section' => 'wheels', 'is_visible' => true],
            ['section' => 'account_categories', 'is_visible' => true],
        ];

        return Inertia::render('Home', [
            'categories' => AccountCategoryResource::collection($categories),
            'games' => GameResource::collection($games),
            'wheels' => WheelResource::collection($wheels),
            'bannerSliders' => $bannerSliders,
            'flashSale' => $flashSale ? new FlashSaleResource($flashSale) : null,
            'topDepositors' => TopDepositorResource::collection($topDepositors),
            'homeSectionsOrder' => $homeSectionsOrder,
        ]);
    }
}

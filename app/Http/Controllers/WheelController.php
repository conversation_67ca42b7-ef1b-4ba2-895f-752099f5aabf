<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\RewardType;
use App\Enums\TransactionType;
use App\Http\Resources\WheelResource;
use App\Models\Wheel;
use App\Models\WheelSegment;
use App\Randomizer\Element;
use App\Randomizer\Randomizer;
use App\Support\Helper;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class WheelController extends Controller
{
    public function show(string $slug): Response
    {
        $wheel = Wheel::query()
            ->where('is_visible', true)
            ->where('slug', $slug)
            ->with(['wheelSpins' => fn(HasMany $query): HasMany => $query->limit(5)->latest(), 'wheelSpins.user'])
            ->firstOrFail();

        return Inertia::render('Wheels/Show', [
            'wheel' => new WheelResource($wheel),
            'position' => fn() => session()->pull('position'),
        ]);
    }

    public function spin(string $slug, Request $request)
    {
        $wheel = Wheel::query()
            ->where('is_visible', true)
            ->where('slug', $slug)
            ->with(
                'wheelSegments',
                fn(HasMany $query) => $query
                    ->where('probability', '>', 0)
                    ->orderBy('order'),
            )
            ->firstOrFail();

        $user = $request->user();

        if ($user->balance < $wheel->price) {
            return back()->with('error', 'Bạn không đủ tiền để quay. Vui lòng nạp thêm tiền vào tài khoản.');
        }

        if ($wheel->wheelSegments->isEmpty()) {
            return back()->with('error', 'Vòng quay chưa được cấu hình. Vui lòng liên hệ quản trị viên.');
        }

        $randomizer = new Randomizer();

        $wheel->wheelSegments->each(
            fn(WheelSegment $segment): \App\Randomizer\Randomizer => $randomizer->add(new Element($segment, $segment->probability)),
        );

        /** @var WheelSegment $segment */
        $segment = $randomizer->get();

        $segmentCount = $wheel->wheelSegments->count();
        $prizePerSegment = 360 / $segmentCount;

        $position = ($segment->order >= 1 && $segment->order <= $segmentCount)
            ? 360 - ($segment->order - 1) * $prizePerSegment
            : 0;

        $message = match ($segment->reward_type) {
            RewardType::Money => sprintf('Trúng %s', Helper::formatCurrency($segment->value)),
            RewardType::Item => sprintf('Trúng %s %s', number_format($segment->value), $segment->gameItem->name),
            RewardType::Account => 'Trúng tài khoản game',
            RewardType::None => $segment->name,
        };

        switch ($segment->reward_type) {
            case RewardType::Money:
                $user->recordTransaction(
                    TransactionType::WheelReward,
                    $wheel->price,
                    $wheel,
                    "$message từ $wheel->name",
                );
                break;

            case RewardType::Item:
                $user->userItem()
                    ->updateOrCreate(['game_item_id' => $segment->game_item_id])
                    ->increment('quantity', $segment->value);
                break;

            case RewardType::None:
            case RewardType::Account:
                break;
        }

        $wheel->wheelSpins()->create([
            'user_id' => $user->getKey(),
            'result' => $message,
            'price' => $wheel->price,
        ]);

        $user->recordTransaction(
            TransactionType::SpinWheel,
            $wheel->price,
            $wheel,
            "Quay $wheel->name",
        );

        return back()->with([
            'success' => $message,
            'position' => $position,
        ]);
    }
}

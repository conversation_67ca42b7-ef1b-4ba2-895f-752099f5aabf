<?php

declare(strict_types=1);

namespace App\Http\Controllers;

use App\Enums\PostStatus;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\PostResource;
use App\Models\Category;
use App\Models\Post;
use Inertia\Inertia;
use Inertia\Response;

class BlogController extends Controller
{
    public function index(): Response
    {
        $posts = Post::query()
            ->where('status', PostStatus::Published)
            ->latest()
            ->with(['category', 'user'])
            ->paginate();

        $categories = Category::query()
            ->where('is_visible', true)
            ->withCount('posts')
            ->get();

        return Inertia::render('Blog/Index', [
            'posts' => PostResource::collection($posts),
            'categories' => CategoryResource::collection($categories),
            'filters' => request()->all(),
        ]);
    }

    public function show(string $slug): Response
    {
        $post = Post::query()
            ->where('slug', $slug)
            ->where('status', PostStatus::Published)
            ->with(['category', 'user'])
            ->first();


        $categories = Category::query()
            ->where('is_visible', true)
            ->withCount('posts')
            ->get();

        if ($post) {
            return Inertia::render('Blog/Show', [
                'post' => new PostResource($post),
                'categories' => CategoryResource::collection($categories),
            ]);
        }

        $category = Category::query()
            ->where('slug', $slug)
            ->first();

        if (! $category) {
            abort(404);
        }

        $posts = Post::query()
            ->where('category_id', $category->getKey())
            ->where('status', PostStatus::Published)
            ->latest()
            ->with(['category', 'user'])
            ->paginate();

        return Inertia::render('Blog/Index', [
            'category' => $category,
            'categories' => CategoryResource::collection($categories),
            'posts' => PostResource::collection($posts),
        ]);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Enums\AffiliateStatus;
use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\User;
use App\Models\UserSocial;
use App\Services\DepositCodeService;
use App\Settings\SocialLoginSettings;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Auth;
use Laravel\Socialite\Facades\Socialite;
use Illuminate\Support\Str;
use Laravel\Socialite\Two\InvalidStateException;
use Symfony\Component\HttpFoundation\RedirectResponse;

class SocialiteController extends Controller
{
    public function redirect(string $provider): RedirectResponse
    {
        $this->redirectProvider($provider);

        return Socialite::driver($provider)->redirect();
    }

    public function callback(string $provider): Response
    {
        $this->redirectProvider($provider);

        try {
            /** @var \Laravel\Socialite\Two\User $providerUser */
            $providerUser = Socialite::driver($provider)->user();
        } catch (InvalidStateException) {
            return new Response(
                <<<HTML
                    <script>
                        window.close();
                    </script>
                HTML,
            );
        }

        $userSocial = UserSocial::query()->firstOrNew(
            ['provider_id' => $providerUser->getId(), 'provider' => $provider],
            ['token' => $providerUser->token],
        );

        $user = User::query()->where('email', $providerUser->getEmail())->first();

        if (! $user) {
            $referralCode = session('referral_code');
            $referredBy = null;

            if ($referralCode) {
                $affiliate = Affiliate::query()
                    ->where('referral_code', $referralCode)
                    ->where('status', AffiliateStatus::Approved)
                    ->first();

                if ($affiliate) {
                    $referredBy = $affiliate->getKey();
                }
            }

            $user = User::query()->create([
                'email' => $providerUser->getEmail(),
                'name' => $providerUser->getName() ?: $providerUser->getNickname(),
                'username' => $providerUser->getNickname() ?: $this->generateUsername($providerUser->getName()),
                'avatar' => $providerUser->getAvatar(),
                'email_verified_at' => now(),
                'password' => Str::random(32),
                'deposit_code' => app(DepositCodeService::class)->generateDepositCode(),
                'referral_code' => $referralCode,
                'referred_by' => $referredBy,
                'referred_at' => $referredBy ? now() : null,
            ]);

            if ($referredBy) {
                if ($affiliate = Affiliate::find($referredBy)) {
                    $affiliate->increment('total_referrals');
                }
            }

            session()->forget('referral_code');
        }

        $userSocial->user()->associate($user);
        $userSocial->save();

        Auth::login($user, true);

        return new Response(
            <<<HTML
                <script>
                    window.opener.location.reload();
                    window.close();
                </script>
            HTML,
        );
    }

    protected function redirectProvider(string $provider): void
    {
        if (! app(SocialLoginSettings::class)->{"{$provider}_enabled"}) {
            abort(404);
        }

        config(["services.{$provider}.redirect" => route('socialite.callback', $provider)]);
    }

    protected function generateUsername(string $name): string
    {
        $name = str($name)->slug('');

        while (User::query()->where('username', $name)->exists()) {
            $name = $name->append((string) mt_rand(1, 9));
        }

        return $name->toString();
    }
}

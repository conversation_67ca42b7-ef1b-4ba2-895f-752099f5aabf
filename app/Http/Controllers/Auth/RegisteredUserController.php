<?php

declare(strict_types=1);

namespace App\Http\Controllers\Auth;

use App\Enums\AffiliateStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\RegisterRequest;
use App\Models\Affiliate;
use App\Models\User;
use App\Services\DepositCodeService;
use Illuminate\Auth\Events\Registered;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class RegisteredUserController extends Controller
{
    public function create(): Response
    {
        return Inertia::render('Auth/Register');
    }

    public function store(RegisterRequest $request): RedirectResponse
    {
        $referralCode = session('referral_code');
        $referredBy = null;

        if ($referralCode) {
            $affiliate = Affiliate::query()
                ->where('referral_code', $referralCode)
                ->where('status', AffiliateStatus::Approved)
                ->first();

            if ($affiliate) {
                $referredBy = $affiliate->getKey();
            }
        }

        $userData = [
            'name' => $request->input('name'),
            'username' => $request->input('username'),
            'email' => $request->input('email'),
            'password' => $request->input('password'),
            'deposit_code' => app(DepositCodeService::class)->generateDepositCode(),
            'referral_code' => $referralCode,
            'referred_by' => $referredBy,
            'referred_at' => $referredBy ? now() : null,
        ];

        $user = User::create($userData);

        if ($referredBy) {
            if ($affiliate = Affiliate::find($referredBy)) {
                $affiliate->increment('total_referrals');
            }
        }

        event(new Registered($user));

        Auth::login($user);

        session()->forget('referral_code');

        return redirect(route('home', absolute: false));
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Enums\TransactionType;
use App\Http\Controllers\Controller;
use App\Http\Resources\TransactionResource;
use App\Models\Transaction;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class TransactionController extends Controller
{
    public function __invoke(Request $request): Response
    {
        $request->validate([
            'type' => ['nullable', 'string'],
            'from_date' => ['nullable', 'date'],
            'to_date' => ['nullable', 'date', 'after_or_equal:from_date'],
        ]);

        $q = $request->input('q');

        $transactions = Transaction::query()
            ->whereBelongsTo($request->user())
            ->when(
                $request->input('type'),
                fn(Builder $query, string $type) => $query->where('type', $type),
            )
            ->when(
                $q,
                fn(Builder $query, string $q) => $query
                    ->where(function (Builder $query) use ($q): void {
                        $query->where('id', 'like', "%{$q}%")
                            ->orWhere('amount', 'like', "%{$q}%")
                            ->orWhere('description', 'like', "%{$q}%");
                    }),
            )
            ->when(
                $request->input('from_date'),
                fn(Builder $query, string $fromDate) => $query->whereDate('created_at', '>=', $fromDate),
            )
            ->when(
                $request->input('to_date'),
                fn(Builder $query, string $toDate) => $query->whereDate('created_at', '<=', $toDate),
            )
            ->latest()
            ->paginate();

        return Inertia::render('User/Transactions', [
            'transactions' => TransactionResource::collection($transactions),
            'transactionTypes' => collect(TransactionType::cases())->mapWithKeys(fn(TransactionType $type) => [$type->value => $type->getLabel()]),
            'filters' => $request->all(),
        ]);
    }
}

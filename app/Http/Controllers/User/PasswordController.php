<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\PasswordRequest;
use Illuminate\Http\RedirectResponse;
use Inertia\Inertia;
use Inertia\Response;

class PasswordController extends Controller
{
    public function edit(): Response
    {
        return Inertia::render('User/ChangePassword');
    }

    public function update(PasswordRequest $request): RedirectResponse
    {
        $request->user()->update([
            'password' => $request->input('password'),
        ]);

        return back();
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\AuthenticationLogResource;
use Inertia\Inertia;
use Inertia\Response;

class ProfileController extends Controller
{
    public function __invoke(): Response
    {
        $authenticationLog = auth()->user()
            ->authentications()
            ->whereNull('logout_at')
            ->latest('id')
            ->paginate();

        return Inertia::render('User/Profile', [
            'authenticationLog' => AuthenticationLogResource::collection($authenticationLog),
        ]);
    }
}

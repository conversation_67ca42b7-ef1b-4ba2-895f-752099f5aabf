<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\WheelResource;
use App\Http\Resources\WheelSpinResource;
use App\Models\Wheel;
use App\Models\WheelSpin;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class WheelSpinController extends Controller
{
    public function __invoke(Request $request): Response
    {
        $wheelSpins = WheelSpin::query()
            ->whereBelongsTo(auth()->user())
            ->when($request->input('q'), function (Builder $query, string $q): void {
                $query
                    ->where('id', 'like', "%$q%")
                    ->orWhere('result', 'like', "%$q%");
            })
            ->when(
                $request->input('from_date'),
                fn(Builder $query, string $fromDate) => $query->whereDate('created_at', '>=', $fromDate),
            )
            ->when(
                $request->input('to_date'),
                fn(Builder $query, string $toDate) => $query->whereDate('created_at', '<=', $toDate),
            )
            ->with('user')
            ->withWhereHas(
                'wheel',
                fn(Builder|BelongsTo $query) => $query->when(
                    $request->input('wheel'),
                    fn(Builder $query) => $query->whereKey($request->input('wheel')),
                ),
            )
            ->latest()
            ->paginate();

        return Inertia::render('User/WheelSpins', [
            'wheels' => WheelResource::collection(Wheel::all()),
            'wheelSpins' => WheelSpinResource::collection($wheelSpins),
            'filters' => $request->all(),
        ]);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Actions\UpdateRechargeAction;
use App\Actions\StoreRechargeAction;
use App\DataTransferObjects\RechargeResponse;
use App\Enums\RechargeStatus;
use App\Facades\Recharge as RechargeFacade;
use App\Http\Controllers\Controller;
use App\Http\Requests\RechargeCallbackRequest;
use App\Http\Requests\User\RechargeRequest;
use App\Http\Resources\RechargeResource;
use App\Models\Recharge;
use App\Settings\DepositSettings;
use App\Support\Helper;
use Closure;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class RechargeController extends Controller
{
    public function __construct()
    {
        $this->middleware(function (Request $request, Closure $next) {
            $depositSettings = app(DepositSettings::class);

            if ($depositSettings->recharge_type === 'off') {
                abort(404);
            }

            return $next($request);
        });
    }

    public function index(Request $request, DepositSettings $depositSettings): Response
    {
        $recharges = Recharge::query()
            ->whereBelongsTo($request->user())
            ->latest()
            ->when($request->input('search'), function (Builder $query, string $search): void {
                $query
                    ->where('id', 'like', "%$search%")
                    ->orWhere('serial', 'like', "%$search%");
            })
            ->when(
                $request->input('type'),
                fn(Builder $query, string $type) => $query->where('type', $type),
            )
            ->when(
                $request->integer('amount'),
                fn(Builder $query, int $amount) => $query->where('declared_amount', $amount),
            )
            ->when(
                $request->input('from_date'),
                fn(Builder $query, string $fromDate) => $query->whereDate('created_at', '>=', $fromDate),
            )
            ->when(
                $request->input('to_date'),
                fn(Builder $query, string $toDate) => $query->whereDate('created_at', '<=', $toDate),
            )
            ->paginate();

        $telecoms = collect($depositSettings->recharge_telecoms)->pluck('name', 'value');
        $amounts = collect([10_000, 20_000, 30_0000, 50_000, 100_000, 200_000, 300_000, 500_000, 1_000_000, 2_000_000, 5_000_000, 10_000_000])
            ->mapWithKeys(fn(int $item): array => [$item => Helper::formatCurrency($item)])
            ->all();

        return Inertia::render('User/Deposit/Recharges', [
            'recharges' => RechargeResource::collection($recharges),
            'telecoms' => $telecoms,
            'amounts' => $amounts,
            'filters' => $request->all(),
        ]);
    }

    public function store(RechargeRequest $request, StoreRechargeAction $storeRechargeAction): RedirectResponse
    {
        try {
            $response = $storeRechargeAction(
                $request->input('type'),
                $request->integer('amount'),
                $request->input('code'),
                $request->input('serial'),
            );

            if (! in_array($response->getStatus(), [RechargeStatus::Pending, RechargeStatus::Completed])) {
                return back()->with(['error' => $response->getMessage()]);
            }

            return back()->with('success', match ($response->getStatus()) {
                RechargeStatus::Pending => 'Thẻ của bạn đang được xử lý, vui lòng chờ trong giây lát!',
                RechargeStatus::Completed => sprintf('Nạp thẻ %s mệnh giá %s thành công!', $request->input('type'), Helper::formatCurrency($request->float('amount'))),
            });
        } catch (Exception $e) {
            return back()->with('error', $e->getMessage());
        }
    }

    public function callback(
        RechargeCallbackRequest $request,
        UpdateRechargeAction $updateRechargeAction,
    ) {
        $sign = RechargeFacade::generateSignature($request->input('code'), $request->input('serial'));

        if ($sign !== $request->input('callback_sign')) {
            return response()->json(['status' => 'error']);
        }

        $recharge = Recharge::query()
            ->where('request_id', $request->input('request_id'))
            ->where('status', RechargeStatus::Pending)
            ->first();

        if (! $recharge) {
            return response()->json(['status' => 'error']);
        }

        $response = new RechargeResponse(
            status: $request->input('status'),
            requestId: $request->input('request_id'),
            providerTransactionId: $request->input('trans_id'),
            amount: $request->integer('amount'),
            message: $request->input('message'),
        );

        $updateRechargeAction(
            $recharge,
            $response->getStatus(),
            $response->amount,
            $response->providerTransactionId,
            $response->toArray(),
        );

        return response()->json(['status' => 'success']);
    }
}

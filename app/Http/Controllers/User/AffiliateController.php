<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Enums\AffiliateStatus;
use App\Http\Controllers\Controller;
use App\Models\Affiliate;
use App\Models\AffiliateTier;
use App\Notifications\AffiliateRegistrationNotification;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Notification;
use Inertia\Inertia;
use Inertia\Response;
use Closure;
use Filament\Facades\Filament;

class AffiliateController extends Controller
{
    public function __construct()
    {
        $this->middleware(function (Request $request, Closure $next) {
            $affiliate = Affiliate::query()->firstWhere('user_id', Auth::id());

            if (! $affiliate || $affiliate->status !== AffiliateStatus::Approved) {
                return $next($request);
            }

            return redirect(Filament::getPanel('affiliate')->getUrl());
        });
    }

    public function index(): Response
    {
        $user = Auth::user();
        $affiliate = Affiliate::query()
            ->where('user_id', $user->id)
            ->with('tier')
            ->first();

        return Inertia::render('User/Affiliate/Index', [
            'affiliate' => $affiliate ? [
                'id' => $affiliate->id,
                'full_name' => $affiliate->full_name,
                'phone' => $affiliate->phone,
                'email' => $affiliate->email,
                'bank_name' => $affiliate->bank_name,
                'bank_account_number' => $affiliate->bank_account_number,
                'bank_account_name' => $affiliate->bank_account_name,
                'status' => $affiliate->status->value,
                'status_label' => $affiliate->status->getLabel(),
                'referral_code' => $affiliate->referral_code,
                'referral_url' => $affiliate->referral_url,
                'total_commission' => $affiliate->total_commission,
                'available_commission' => $affiliate->available_commission,
                'total_clicks' => $affiliate->total_clicks,
                'total_conversions' => $affiliate->total_conversions,
                'conversion_rate' => $affiliate->conversion_rate,
                'created_at' => $affiliate->created_at->format('d/m/Y H:i'),
                'approved_at' => $affiliate->approved_at?->format('d/m/Y H:i'),
                'tier' => [
                    'name' => $affiliate->tier->name,
                    'commission_rate' => $affiliate->tier->commission_rate,
                ],
            ] : null,
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'full_name' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'email' => 'required|email|max:255',
        ]);

        $user = Auth::user();
        $defaultTier = AffiliateTier::query()
            ->where('is_default', true)
            ->first();

        if (! $defaultTier) {
            return back()->with('error', 'Không tìm thấy cấp độ tiếp thị liên kết mặc định.');
        }

        $existingAffiliate = Affiliate::query()
            ->where('user_id', $user->id)
            ->first();

        if ($existingAffiliate) {
            return back()->with('error', 'Bạn đã đăng ký tiếp thị liên kết.');
        }

        $databaseData = [
            'user_id' => $user->id,
            'tier_id' => $defaultTier->id,
            'status' => AffiliateStatus::Pending,
        ];

        $affiliate = Affiliate::query()->create($databaseData);

        Notification::route('telegram', config('services.telegram.chat_id'))
            ->notify(new AffiliateRegistrationNotification($user, $affiliate, $request->only(['full_name', 'phone', 'email'])));

        return back()->with('success', 'Đăng ký affiliate thành công! Vui lòng chờ admin duyệt và tham gia nhóm Zalo để nhận thông báo sớm nhất.');
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\DepositGenerateQrCodeRequest;
use App\Http\Resources\BankAccountResource;
use App\Models\BankAccount;
use App\Settings\DepositSettings;
use App\Support\Helper;
use Illuminate\Support\Facades\Storage;
use Inertia\Inertia;
use Inertia\Response;

class DepositController extends Controller
{
    public function index(DepositSettings $depositSettings): Response
    {
        abort_if(
            $depositSettings->recharge_type === 'off' && ! $depositSettings->auto_pay_enabled,
            404,
        );

        $rechargeTelecoms = array_map(fn($telecom) => [
            ...$telecom,
            'logo' => $telecom['logo'] ? Storage::url($telecom['logo']) : null,
            'amounts' => array_map(fn($amount) => [
                ...$amount,
                'formatted_value' => Helper::formatCurrency((int) $amount['value']),
            ], $telecom['amounts']),
        ], $depositSettings->recharge_telecoms ?? []);

        return Inertia::render('User/Deposit/Index', [
            'recharge_telecoms' => $rechargeTelecoms,
            'recharge_type' => $depositSettings->recharge_type,
            'auto_pay_enabled' => $depositSettings->auto_pay_enabled,
            'data' => fn() => session()->pull('data'),
        ]);
    }

    public function generateQrCode(DepositGenerateQrCodeRequest $request, DepositSettings $depositSettings)
    {
        abort_if(! $depositSettings->auto_pay_enabled, 404);

        $bankAccount = BankAccount::query()
            ->whereKey($depositSettings->auto_pay_default_bank_account)
            ->first();

        if (! $bankAccount) {
            return back()->withErrors([
                'amount' => 'Không tìm thấy tài khoản ngân hàng mặc định.',
            ]);
        }

        $description = trim(sprintf(
            '%s %s',
            $depositSettings->auto_pay_code_prefix,
            $request->user()->deposit_code,
        ));

        return back()->with([
            'data' => [
                'image' => sprintf(
                    'https://qr.sepay.vn/img?acc=%s&bank=%s&amount=%s&des=%s',
                    $bankAccount->account_number,
                    $bankAccount->bank_name->value,
                    $request->integer('amount'),
                    $description,
                ),
                'amount' => Helper::formatCurrency($request->integer('amount')),
                'bank_account' => new BankAccountResource($bankAccount),
                'description' => $description,
            ],
        ]);
    }
}

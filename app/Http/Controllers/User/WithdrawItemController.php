<?php

declare(strict_types=1);

namespace App\Http\Controllers\User;

use App\Http\Controllers\Controller;
use App\Http\Requests\User\WithdrawItemRequest;
use App\Http\Resources\GameItemResource;
use App\Http\Resources\WithdrawItemResource;
use App\Models\GameItem;
use App\Models\ItemWithdraw;
use App\Notifications\GameItemWithdrawn;
use Illuminate\Support\Facades\Notification;
use Inertia\Inertia;
use Inertia\Response;

class WithdrawItemController extends Controller
{
    public function index(?string $gameItem = null): Response
    {
        $gameItems = GameItem::query()
            ->with(['userItem', 'customFields'])
            ->get();

        $withdraws = ItemWithdraw::query()
            ->whereBelongsTo(auth()->user())
            ->with('gameItem')
            ->latest()
            ->paginate();

        abort_if($gameItem && ! $gameItems->contains('id', $gameItem), 404);

        return Inertia::render('User/WithdrawItem/Index', [
            'gameItems' => GameItemResource::collection($gameItems),
            'withdraws' => WithdrawItemResource::collection($withdraws),
        ]);
    }

    public function store(string $gameItem, WithdrawItemRequest $request)
    {
        $gameItem = GameItem::query()
            ->with('userItem')
            ->findOrFail($gameItem);

        $withdraw = ItemWithdraw::query()->create([
            'game_item_id' => $gameItem->getKey(),
            'user_id' => $request->user()->getKey(),
            'quantity' => $request->integer('value'),
        ]);

        $withdraw->saveCustomFields($request->input('custom_fields', []));

        $gameItem->userItem()->decrement('quantity', $withdraw->quantity);

        Notification::route('telegram', config('services.telegram.chat_id'))
            ->notify(new GameItemWithdrawn($withdraw));

        return back()->with('success', 'Rút vật phẩm thành công');
    }
}

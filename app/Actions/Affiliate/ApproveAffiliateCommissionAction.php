<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Enums\AffiliateCommissionStatus;
use App\Models\AffiliateCommission;
use Illuminate\Support\Facades\DB;

class ApproveAffiliateCommissionAction
{
    public function __construct(protected AutoUpgradeAffiliateTierAction $autoUpgradeAffiliateTierAction) {}

    public function __invoke(AffiliateCommission $commission): void
    {
        DB::transaction(function () use ($commission): void {
            $commission->update([
                'status' => AffiliateCommissionStatus::Approved,
                'approved_at' => now(),
            ]);

            $affiliate = $commission->affiliate;
            $affiliate->increment('total_commission', $commission->commission_amount);
            $affiliate->increment('available_commission', $commission->commission_amount);

            ($this->autoUpgradeAffiliateTierAction)($affiliate);
        });
    }
}

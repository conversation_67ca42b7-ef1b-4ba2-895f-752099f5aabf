<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use App\Notifications\AffiliateRejectedNotification;

class RejectAffiliateAction
{
    public function __invoke(Affiliate $affiliate, array $data): void
    {
        $affiliate->update([
            'status' => AffiliateStatus::Rejected,
            'rejection_reason' => $data['rejection_reason'],
        ]);

        $affiliate->user->notify(new AffiliateRejectedNotification($affiliate, $data['rejection_reason']));
    }
}

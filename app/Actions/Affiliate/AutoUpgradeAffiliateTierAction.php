<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Enums\AffiliateCommissionStatus;
use App\Models\Affiliate;
use App\Models\AffiliateTier;
use App\Notifications\AffiliateTierUpgraded;
use Illuminate\Support\Facades\DB;

class AutoUpgradeAffiliateTierAction
{
    public function __invoke(Affiliate $affiliate): void
    {
        $totalApprovedCommission = $affiliate
            ->commissions()
            ->where('status', AffiliateCommissionStatus::Approved)
            ->sum('commission_amount');

        $newTier = AffiliateTier::query()
            ->where('min_commission_required', '<=', $totalApprovedCommission)
            ->orderByDesc('min_commission_required')
            ->first();

        if (! $newTier) {
            return;
        }

        if ($newTier->id === $affiliate->tier_id) {
            return;
        }

        DB::transaction(function () use ($affiliate, $newTier): void {
            $oldTier = $affiliate->tier;

            $affiliate->update([
                'tier_id' => $newTier->id,
            ]);

            $affiliate->user->notify(new AffiliateTierUpgraded($affiliate, $oldTier, $newTier));
        });
    }
}

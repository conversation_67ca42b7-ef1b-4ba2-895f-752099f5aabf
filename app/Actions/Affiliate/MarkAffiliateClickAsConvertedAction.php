<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Models\Affiliate;
use App\Models\User;

class MarkAffiliateClickAsConvertedAction
{
    public function __invoke(User $buyer): void
    {
        $affiliate = null;

        if ($buyer->referred_by) {
            $affiliate = Affiliate::query()->find($buyer->referred_by);
        }

        if (! $affiliate) {
            $referralCode = session('referral_code');

            if (! $referralCode) {
                return;
            }

            $affiliate = Affiliate::query()
                ->where('referral_code', $referralCode)
                ->first();
        }

        if (! $affiliate) {
            return;
        }

        $affiliate->clicks()
            ->where('referral_code', $affiliate->referral_code)
            ->where('ip_address', request()->ip())
            ->where('converted', false)
            ->update([
                'converted' => true,
                'converted_at' => now(),
            ]);
    }
}

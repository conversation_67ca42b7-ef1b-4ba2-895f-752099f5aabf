<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Enums\AffiliateCommissionStatus;
use App\Models\AffiliateCommission;

class RejectAffiliateCommissionAction
{
    public function __invoke(AffiliateCommission $commission, ?string $reason = null): void
    {
        $commission->update([
            'status' => AffiliateCommissionStatus::Rejected,
            'description' => $reason ? "Từ chối: {$reason}" : $commission->description,
        ]);
    }
}

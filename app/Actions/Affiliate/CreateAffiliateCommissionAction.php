<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Enums\AffiliateCommissionStatus;
use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use App\Models\AffiliateCommission;
use App\Models\PurchasedAccount;
use App\Models\User;
use App\Notifications\AffiliateCommissionCreated;
use Illuminate\Support\Facades\DB;

class CreateAffiliateCommissionAction
{
    public function __construct(
        protected MarkAffiliateClickAsConvertedAction $markAffiliateClickAsConvertedAction,
    ) {}

    public function __invoke(PurchasedAccount $purchasedAccount, User $buyer): void
    {
        $affiliate = null;

        if ($buyer->referred_by) {
            $affiliate = Affiliate::query()
                ->where('status', AffiliateStatus::Approved)
                ->find($buyer->referred_by);
        }

        if (! $affiliate) {
            $referralCode = session('referral_code');

            if (! $referralCode) {
                return;
            }

            $affiliate = Affiliate::query()
                ->where('referral_code', $referralCode)
                ->where('status', AffiliateStatus::Approved)
                ->first();
        }

        if (! $affiliate) {
            return;
        }

        if ($affiliate->user_id === $purchasedAccount->account->user_id) {
            return;
        }

        $existingCommission = AffiliateCommission::query()
            ->where('purchased_account_id', $purchasedAccount->id)
            ->where('affiliate_id', $affiliate->id)
            ->first();

        if ($existingCommission) {
            return;
        }

        DB::transaction(function () use ($purchasedAccount, $affiliate, $buyer): void {
            $commissionRate = $affiliate->tier->commission_rate;
            $commissionAmount = ($purchasedAccount->total * $commissionRate) / 100;

            $commission = AffiliateCommission::query()->create([
                'affiliate_id' => $affiliate->id,
                'purchased_account_id' => $purchasedAccount->id,
                'referral_code' => $affiliate->referral_code,
                'order_amount' => $purchasedAccount->total,
                'commission_rate' => $commissionRate,
                'commission_amount' => $commissionAmount,
                'status' => AffiliateCommissionStatus::Pending,
                'description' => "Hoa hồng từ đơn hàng #{$purchasedAccount->id}",
            ]);

            $affiliate->increment('total_sales', $purchasedAccount->total);
            $affiliate->increment('total_conversions');

            ($this->markAffiliateClickAsConvertedAction)($buyer);

            $affiliate->user->notify(new AffiliateCommissionCreated($commission));
        });

        session()->forget('referral_code');
    }
}

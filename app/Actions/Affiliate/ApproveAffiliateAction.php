<?php

declare(strict_types=1);

namespace App\Actions\Affiliate;

use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use App\Notifications\AffiliateApprovedNotification;

class ApproveAffiliateAction
{
    public function __invoke(Affiliate $affiliate): void
    {
        $affiliate->update([
            'status' => AffiliateStatus::Approved,
            'approved_at' => now(),
        ]);

        $affiliate->user->notify(new AffiliateApprovedNotification($affiliate));
    }
}

<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\DepositStatus;
use App\Enums\RechargeStatus;
use App\Enums\TransactionType;
use App\Events\DepositCompleted;
use App\Models\Recharge;
use App\Settings\DepositSettings;
use App\Support\Helper;

class UpdateRechargeAction
{
    public function __construct(
        protected DepositSettings $depositSettings,
    ) {}

    public function __invoke(Recharge $recharge, RechargeStatus $status, int|float $amount, ?string $transactionId = null, ?array $extra = null): void
    {
        $data = ['status' => $status];

        $finalAmount = $recharge->declared_amount;
        $promotionPercentage = $this->depositSettings->recharge_promotion;

        if ($promotionPercentage !== 0) {
            $finalAmount = $recharge->declared_amount + ($recharge->declared_amount * $promotionPercentage / 100);
        }

        if ($status === RechargeStatus::Completed) {
            $data['amount'] = $amount;

            $description = sprintf('Nạp thẻ %s %s', $recharge->type, Helper::formatCurrency($recharge->declared_amount));

            if ($promotionPercentage < 0) {
                $description .= sprintf(' (Chiết khấu %s%%)', $promotionPercentage);
            } elseif ($promotionPercentage > 0) {
                $description .= sprintf(' (Khuyến mãi %s%%)', abs($promotionPercentage));
            }

            $recharge->user->recordTransaction(
                TransactionType::Deposit,
                $finalAmount,
                $recharge,
                $description,
            );

            $deposit = $recharge->deposit()->create([
                'user_id' => $recharge->user_id,
                'provider' => $recharge->provider?->value,
                'transaction_id' => $transactionId,
                'amount' => $recharge->declared_amount,
                'content' => $description,
                'status' => DepositStatus::Completed,
                'transacted_at' => now(),
                'raw' => $extra,
            ]);

            DepositCompleted::dispatch($deposit);
        }

        $recharge->update($data);
    }
}

<?php

declare(strict_types=1);

namespace App\Actions;

use Exception;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use ZipArchive;

class ReleaseUpdateAction
{
    public function __construct(
        private readonly string $version,
        private readonly string $description,
        private readonly bool $isRequired,
        private readonly bool $shouldUpload,
        private readonly ?string $token = null,
    ) {}

    public function execute(): array
    {
        $this->validateVersion();

        $outputPath = $this->getOutputPath();
        $this->handleExistingFile($outputPath);

        $tempDir = $this->createTempDirectory();

        try {
            $this->copyFiles($tempDir);
            $this->buildAssets($tempDir);
            $this->createZip($tempDir, $outputPath);

            $metadata = $this->createMetadata($outputPath);
            $this->saveMetadata($metadata);

            $this->cleanup($tempDir);

            $result = [
                'success' => true,
                'file_path' => $outputPath,
                'file_size' => File::size($outputPath),
                'checksum' => $metadata['checksum'],
                'metadata_path' => $this->getMetadataPath(),
            ];

            if ($this->shouldUpload) {
                $this->uploadPackage($outputPath, $metadata);
                $result['uploaded'] = true;
            }

            return $result;
        } catch (Exception $e) {
            $this->cleanup($tempDir);
            throw $e;
        }
    }

    private function validateVersion(): void
    {
        if (! preg_match('/^\d+\.\d+\.\d+$/', $this->version)) {
            throw new Exception('Version must be in format: x.y.z (e.g., 1.0.1)');
        }
    }

    private function getOutputPath(): string
    {
        return storage_path("app/updates/update-{$this->version}.zip");
    }

    private function getMetadataPath(): string
    {
        return storage_path("app/updates/update-{$this->version}.json");
    }

    private function handleExistingFile(string $outputPath): void
    {
        if (File::exists($outputPath)) {
            $outputDir = dirname($outputPath);
            if (! File::exists($outputDir)) {
                File::makeDirectory($outputDir, 0755, true);
            }
        }
    }

    private function createTempDirectory(): string
    {
        $tempDir = storage_path("app/updates/temp_build_{$this->version}");

        if (File::exists($tempDir)) {
            File::deleteDirectory($tempDir);
        }

        File::makeDirectory($tempDir, 0755, true);

        return $tempDir;
    }

    private function copyFiles(string $tempDir): void
    {
        $basePath = base_path();

        $directories = [
            'app',
            'config',
            'database',
            'resources',
            'routes',
            'public',
        ];

        $files = [
            'composer.json',
            'composer.lock',
            'package.json',
            'package-lock.json',
            '.env.example',
            'bootstrap/app.php',
            'bootstrap/providers.php',
        ];

        foreach ($directories as $directory) {
            $this->copyDirectory($basePath, $tempDir, $directory);
        }

        foreach ($files as $file) {
            $this->copyFile($basePath, $tempDir, $file);
        }
    }

    private function copyDirectory(string $basePath, string $tempDir, string $directory): void
    {
        $sourcePath = $basePath . '/' . $directory;
        $destPath = $tempDir . '/' . $directory;

        if (! File::exists($sourcePath)) {
            return;
        }

        try {
            if ($directory === 'database') {
                File::copyDirectory($sourcePath, $destPath);
                $filesDir = $destPath . '/seeders/files';
                if (File::exists($filesDir)) {
                    File::deleteDirectory($filesDir);
                }
            } elseif ($directory === 'public') {
                File::copyDirectory($sourcePath, $destPath);
                $storageDir = $destPath . '/storage';
                if (File::exists($storageDir)) {
                    File::deleteDirectory($storageDir);
                }
            } else {
                File::copyDirectory($sourcePath, $destPath);
            }
        } catch (Exception $e) {
            Log::warning("Failed to copy directory {$directory}: {$e->getMessage()}");
        }
    }

    private function copyFile(string $basePath, string $tempDir, string $file): void
    {
        $sourcePath = $basePath . '/' . $file;
        $destPath = $tempDir . '/' . $file;

        if (! File::exists($sourcePath)) {
            return;
        }

        try {
            $destDir = dirname($destPath);
            if (! File::exists($destDir)) {
                File::makeDirectory($destDir, 0755, true);
            }

            File::copy($sourcePath, $destPath);
        } catch (Exception $e) {
            Log::warning("Failed to copy file {$file}: {$e->getMessage()}");
        }
    }

    private function buildAssets(string $tempDir): void
    {
        $output = shell_exec("cd {$tempDir} && npm run build 2>&1");

        if ($output === null) {
            throw new Exception('Failed to run npm run build');
        }

        $tailwindAdminOutput = shell_exec("cd {$tempDir} && npm_config_yes=true npx tailwindcss@3 --input ./resources/css/filament/admin/theme.css --output ./public/css/filament/admin/theme.css --config ./resources/css/filament/admin/tailwind.config.js --minify 2>&1");
        $tailwindAffiliateOutput = shell_exec("cd {$tempDir} && npm_config_yes=true npx tailwindcss@3 --input ./resources/css/filament/affiliate/theme.css --output ./public/css/filament/affiliate/theme.css --config ./resources/css/filament/affiliate/tailwind.config.js --minify 2>&1");

        if ($tailwindAdminOutput === null || $tailwindAffiliateOutput === null) {
            throw new Exception('Failed to run Tailwind CSS build');
        }
    }

    private function createZip(string $sourceDir, string $outputPath): void
    {
        $zip = new ZipArchive();

        if ($zip->open($outputPath, ZipArchive::CREATE | ZipArchive::OVERWRITE) !== true) {
            throw new Exception('Could not create ZIP file');
        }

        $files = File::allFiles($sourceDir);

        foreach ($files as $file) {
            $relativePath = $file->getRelativePathname();
            $zip->addFile($file->getPathname(), $relativePath);
        }

        $zip->close();
    }

    private function createMetadata(string $outputPath): array
    {
        $checksum = hash_file('sha256', $outputPath);
        $fileSize = File::size($outputPath);

        return [
            'version' => $this->version,
            'description' => $this->description,
            'is_required' => $this->isRequired,
            'file_size' => $fileSize,
            'checksum' => $checksum,
            'created_at' => now()->toISOString(),
        ];
    }

    private function saveMetadata(array $metadata): void
    {
        $metadataPath = $this->getMetadataPath();
        File::put($metadataPath, json_encode($metadata, JSON_PRETTY_PRINT));
    }

    private function cleanup(string $tempDir): void
    {
        if (File::exists($tempDir)) {
            File::deleteDirectory($tempDir);
        }
    }

    private function uploadPackage(string $zipFile, array $metadata): void
    {
        if (!$this->token) {
            throw new Exception('Upload token is required for upload');
        }

        $fileSize = File::size($zipFile);
        $chunkSize = 1024 * 1024 * 2;
        $totalChunks = (int) ceil($fileSize / $chunkSize);
        $uploadId = uniqid('upload_', true);

        $this->initUpload($uploadId, $fileSize, $totalChunks, $chunkSize, $metadata, $zipFile);
        $this->uploadChunks($zipFile, $uploadId, $totalChunks, $chunkSize);
        $this->finalizeUpload($uploadId);
    }

    private function initUpload(string $uploadId, int $fileSize, int $totalChunks, int $chunkSize, array $metadata, string $zipFile): void
    {
        $initResponse = Http::timeout(60)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'Accept' => 'application/json',
            ])
            ->post(config('app.update_server_url') . '/api/v1/updates/init-upload', [
                'version' => $this->version,
                'description' => $metadata['description'] ?? "Update version {$this->version}",
                'is_required' => $metadata['is_required'] ?? false,
                'checksum' => $metadata['checksum'] ?? hash_file('sha256', $zipFile),
                'file_size' => $fileSize,
                'total_chunks' => $totalChunks,
                'chunk_size' => $chunkSize,
                'upload_id' => $uploadId,
                'product_slug' => config('app.product_slug', 'lashgame'),
                'override' => true,
            ]);

        if (! $initResponse->successful()) {
            $message = $initResponse->json('message');
            $message = json_decode($message, true)['error'] ?? $message;
            throw new Exception($message);
        }
    }

    private function uploadChunks(string $zipFile, string $uploadId, int $totalChunks, int $chunkSize): void
    {
        $handle = fopen($zipFile, 'rb');
        if (! $handle) {
            throw new Exception('Could not open file for reading');
        }

        try {
            for ($chunkIndex = 0; $chunkIndex < $totalChunks; $chunkIndex++) {
                $chunkData = fread($handle, $chunkSize);
                $chunkChecksum = hash('sha256', $chunkData);

                $chunkResponse = Http::timeout(120)
                    ->withHeaders([
                        'Authorization' => 'Bearer ' . $this->token,
                        'Accept' => 'application/json',
                    ])
                    ->post(config('app.update_server_url') . '/api/v1/updates/upload-chunk', [
                        'upload_id' => $uploadId,
                        'chunk_index' => $chunkIndex,
                        'chunk_checksum' => $chunkChecksum,
                        'is_last_chunk' => ($chunkIndex === $totalChunks - 1) ? 1 : 0,
                        'chunk_data' => base64_encode($chunkData),
                    ]);

                if (! $chunkResponse->successful()) {
                    $errorBody = $chunkResponse->body();
                    throw new Exception('Failed to upload chunk ' . ($chunkIndex + 1) . ': ' . $errorBody);
                }
            }
        } finally {
            fclose($handle);
        }
    }

    private function finalizeUpload(string $uploadId): void
    {
        $finalizeResponse = Http::timeout(60)
            ->withHeaders([
                'Authorization' => 'Bearer ' . $this->token,
                'Accept' => 'application/json',
            ])
            ->post(config('app.update_server_url') . '/api/v1/updates/finalize-upload', [
                'upload_id' => $uploadId,
            ]);

        if (! $finalizeResponse->successful()) {
            throw new Exception('Failed to finalize upload: ' . $finalizeResponse->body());
        }
    }
}

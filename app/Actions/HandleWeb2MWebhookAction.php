<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\DepositStatus;
use App\Enums\TransactionType;
use App\Events\DepositCompleted;
use App\Events\DepositWebhookProcessed;
use App\Http\Requests\Webhook\Web2MRequest;
use App\Models\BankAccount;
use App\Services\DepositCodeService;
use App\Settings\DepositSettings;
use App\Support\Helper;
use Carbon\Carbon;

class HandleWeb2MWebhookAction
{
    public function __construct(protected DepositSettings $depositSettings) {}

    public function __invoke(Web2MRequest $request): void
    {
        $depositCodeService = app(DepositCodeService::class);

        foreach ($request->validated('data', []) as $transaction) {
            if ($transaction['type'] !== 'IN') {
                continue;
            }

            $user = null;
            $description = $transaction['description'];

            $prefix = $this->depositSettings->auto_pay_code_prefix ?: '';
            $pattern = "/" . preg_quote($prefix, '/') . "\s*([A-Z]{4}\d{2})/";

            if (preg_match_all($pattern, $description, $matches)) {
                $depositCode = end($matches[1]);
                $user = $depositCodeService->findUserByCode($depositCode);
            }

            if (! $user) {
                continue;
            }

            $bankAccount = BankAccount::query()
                ->where('bank_name', $transaction['bank'])
                ->first();

            if (! $bankAccount) {
                continue;
            }

            $amount = (int) $transaction['amount'];
            $finalAmount = $amount;
            $promotionPercentage = $this->depositSettings->auto_pay_promotion;

            if ($promotionPercentage !== 0) {
                $finalAmount = $amount + ($amount * $promotionPercentage / 100);
            }

            $transactionDate = isset($transaction['date']) ? Carbon::createFromFormat('d/m/Y', $transaction['date']) : Carbon::now();

            $deposit = $user->deposits()->firstOrCreate([
                'source_type' => $bankAccount::class,
                'source_id' => $bankAccount->getKey(),
                'provider' => 'web2m',
                'transaction_id' => $transaction['transactionID'],
            ], [
                'amount' => $amount,
                'content' => $description,
                'status' => DepositStatus::Completed,
                'transacted_at' => $transactionDate,
                'raw' => $transaction,
            ]);

            $description = 'Nạp tiền tự động';

            if ($promotionPercentage < 0) {
                $description .= sprintf(' (Chiết khấu %s%%)', $promotionPercentage);
            } elseif ($promotionPercentage > 0) {
                $description .= sprintf(' (Khuyến mãi %s%%)', abs($promotionPercentage));
            }

            if ($deposit->wasRecentlyCreated) {
                $user->recordTransaction(
                    TransactionType::Deposit,
                    $finalAmount,
                    $deposit,
                    $description,
                );

                DepositCompleted::dispatch($deposit);

                DepositWebhookProcessed::dispatch(
                    $deposit,
                    sprintf('Chúc mừng bạn đã nạp %s vào tài khoản thành công!', Helper::formatCurrency($finalAmount)),
                );
            }
        }
    }
}

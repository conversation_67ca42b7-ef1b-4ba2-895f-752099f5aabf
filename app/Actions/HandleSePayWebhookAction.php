<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\DepositStatus;
use App\Enums\TransactionType;
use App\Events\DepositCompleted;
use App\Events\DepositWebhookProcessed;
use App\Http\Requests\Webhook\SePayRequest;
use App\Models\BankAccount;
use App\Services\DepositCodeService;
use App\Settings\DepositSettings;
use App\Support\Helper;

class HandleSePayWebhookAction
{
    public function __construct(protected DepositSettings $depositSettings) {}

    public function __invoke(SePayRequest $request): void
    {
        $content = $request->input('content');
        $depositCodeService = app(DepositCodeService::class);

        $user = null;

        $depositCodePattern = '/\b([A-Z]{4}\d{2})\b/';
        if (preg_match($depositCodePattern, $content, $matches)) {
            $depositCode = $matches[1];
            $user = $depositCodeService->findUserByCode($depositCode);
        }

        if (! $user) {
            return;
        }

        $bankAccount = BankAccount::query()
            ->where('bank_name', $request->input('gateway'))
            ->where('account_number', $request->input('accountNumber'))
            ->firstOrFail();

        $amount = $request->integer('transferAmount');
        $finalAmount = $amount;
        $promotionPercentage = $this->depositSettings->auto_pay_promotion;

        if ($promotionPercentage !== 0) {
            $finalAmount = $amount + ($amount * $promotionPercentage / 100);
        }

        $deposit = $user->deposits()->firstOrCreate([
            'source_type' => $bankAccount::class,
            'source_id' => $bankAccount->getKey(),
            'provider' => 'sepay',
            'transaction_id' => $request->input('id'),
        ], [
            'amount' => $amount,
            'content' => $request->input('content'),
            'status' => DepositStatus::Completed,
            'transacted_at' => $request->date('transactionDate'),
            'raw' => $request->all(),
        ]);

        $description = 'Nạp tiền tự động';

        if ($promotionPercentage < 0) {
            $description .= sprintf(' (Chiết khấu %s%%)', $promotionPercentage);
        } elseif ($promotionPercentage > 0) {
            $description .= sprintf(' (Khuyến mãi %s%%)', abs($promotionPercentage));
        }

        if ($deposit->wasRecentlyCreated) {
            $user->recordTransaction(
                TransactionType::Deposit,
                $finalAmount,
                $deposit,
                $description,
            );

            DepositCompleted::dispatch($deposit);

            DepositWebhookProcessed::dispatch(
                $deposit,
                sprintf('Chúc mừng bạn đã nạp %s vào tài khoản thành công!', Helper::formatCurrency($finalAmount)),
            );
        }
    }
}

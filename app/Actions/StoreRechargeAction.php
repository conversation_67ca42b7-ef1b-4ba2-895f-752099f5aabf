<?php

declare(strict_types=1);

namespace App\Actions;

use App\DataTransferObjects\RechargeResponse;
use App\Enums\RechargeStatus;
use App\Enums\TransactionType;
use App\Facades\Recharge;
use App\Settings\DepositSettings;
use App\Support\Helper;

class StoreRechargeAction
{
    public function __construct(protected DepositSettings $settings) {}

    public function __invoke(string $type, int $amount, string $code, string $serial): RechargeResponse
    {
        $data = [
            'type' => $type,
            'declared_amount' => $amount,
            'pin' => $code,
            'serial' => $serial,
        ];

        if ($this->settings->recharge_type === 'auto') {
            $response = Recharge::charge($type, $amount, $code, $serial);

            if (! in_array($response->getStatus(), [RechargeStatus::Pending, RechargeStatus::Completed])) {
                return $response;
            }

            $data = [
                ...$data,
                'provider' => Recharge::getDefaultDriver(),
                'request_id' => $response->requestId,
                'status' => $response->getStatus(),
            ];
        } else {
            $response = new RechargeResponse(
                status: 99,
            );
        }

        /** @var \App\Models\User $user */
        $user = auth()->user();

        $recharge = $user->recharges()->create($data);

        if (
            $this->settings->recharge_type === 'auto'
            && $response->getStatus() === RechargeStatus::Completed
        ) {
            $user->recordTransaction(
                TransactionType::Deposit,
                $amount,
                $recharge,
                sprintf('Nạp thẻ %s %s', $type, Helper::formatCurrency($amount)),
            );
        }

        return $response;
    }
}

<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\DepositStatus;
use App\Enums\TransactionType;
use App\Events\DepositCompleted;
use App\Events\DepositWebhookProcessed;
use App\Models\BankAccount;
use App\Models\Deposit;
use App\Models\User;
use App\Settings\DepositSettings;
use App\Support\Helper;
use Carbon\Carbon;
use Exception;

class HandleSieuthicodeTransactionAction
{
    public function __construct(private DepositSettings $settings) {}

    public function __invoke(array $transaction, BankAccount $bankAccount): void
    {
        if (($transaction['type'] ?? '') !== 'IN') {
            return;
        }

        $amount = (int) ($transaction['amount'] ?? 0);

        if (
            $amount < $this->settings->auto_pay_min_amount
            || ($this->settings->auto_pay_max_amount > 0 && $amount > $this->settings->auto_pay_max_amount)
        ) {
            return;
        }

        $depositCode = $this->extractDepositCode($transaction['description'] ?? '');

        if (! $depositCode) {
            return;
        }

        $user = User::query()->where('deposit_code', $depositCode)->first();

        if (! $user) {
            return;
        }

        $existingDeposit = Deposit::query()
            ->where('provider', 'sieuthicode')
            ->where('transaction_id', $transaction['transactionID'])
            ->first();

        if ($existingDeposit) {
            return;
        }

        $finalAmount = $amount;
        $promotionPercentage = $this->settings->auto_pay_promotion;

        if ($promotionPercentage !== 0) {
            $finalAmount = $amount + ($amount * $promotionPercentage / 100);
        }

        $transactionDate = $this->parseTransactionDate($transaction['transactionDate'] ?? '');

        $deposit = $user->deposits()->create([
            'source_type' => $bankAccount::class,
            'source_id' => $bankAccount->getKey(),
            'provider' => 'sieuthicode',
            'transaction_id' => $transaction['transactionID'],
            'amount' => $amount,
            'content' => $transaction['description'] ?? '',
            'status' => DepositStatus::Completed,
            'transacted_at' => $transactionDate,
            'raw' => $transaction,
        ]);

        $description = 'Nạp tiền tự động qua chuyển khoản ngân hàng';

        if ($promotionPercentage < 0) {
            $description .= sprintf(' (Chiết khấu %s%%)', $promotionPercentage);
        } elseif ($promotionPercentage > 0) {
            $description .= sprintf(' (Khuyến mãi %s%%)', abs($promotionPercentage));
        }

        $user->recordTransaction(
            TransactionType::Deposit,
            $finalAmount,
            $deposit,
            $description,
        );

        DepositCompleted::dispatch($deposit);

        DepositWebhookProcessed::dispatch(
            $deposit,
            sprintf('Chúc mừng bạn đã nạp %s vào tài khoản thành công!', Helper::formatCurrency($finalAmount)),
        );
    }

    /**
     * Extract the deposit code from a transaction description.
     */
    private function extractDepositCode(string $description): ?string
    {
        $prefix = $this->settings->auto_pay_code_prefix ?: '';
        $pattern = "/" . preg_quote($prefix, '/') . "\s*([A-Za-z]{4}\d{2})/";

        if (preg_match($pattern, $description, $matches)) {
            return $matches[1];
        }

        return null;
    }

    private function parseTransactionDate(string $dateString): Carbon
    {
        $formats = [
            'Y-m-d H:i:s',
            'd/m/Y H:i:s',
            'd/m/Y',
            'Y-m-d',
        ];

        foreach ($formats as $format) {
            try {
                return Carbon::createFromFormat($format, $dateString);
            } catch (Exception $e) {
                continue;
            }
        }

        return Carbon::now();
    }
}

<?php

declare(strict_types=1);

namespace App\Actions;

use App\Services\ReferralCodeService;
use Illuminate\Http\Request;

class HandleReferralCodeAction
{
    public function __construct(protected ReferralCodeService $referralCodeService) {}

    public function __invoke(Request $request): void
    {
        $referralCode = $request->get('ref');

        if (! $referralCode) {
            return;
        }

        $this->referralCodeService->processReferralCode($referralCode, $request);
    }
}

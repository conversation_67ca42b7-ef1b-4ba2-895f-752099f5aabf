<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\DiscountScope;
use App\Enums\DiscountType;
use App\Models\Account;
use App\Models\Discount;
use Illuminate\Validation\ValidationException;

class ApplyAccountDiscountCode
{
    public function __invoke(Account $account, string $code): float
    {
        $discount = Discount::query()
            ->with(['categories', 'accounts', 'users'])
            ->where('code', $code)
            ->where('is_active', true)
            ->first();

        if ($discount === null) {
            throw ValidationException::withMessages([
                'message' => 'Mã giảm giá không tồn tại hoặc đã hết hạn.',
            ]);
        }

        if (! $discount->exists || $discount->is_expired || (! $discount->is_unlimited && $discount->usage >= $discount->limit)) {
            throw ValidationException::withMessages([
                'message' => 'Mã giảm gi<PERSON> không tồn tại hoặc đã hết hạn.',
            ]);
        }

        switch ($discount->applied_to) {
            case DiscountScope::All:
                break;
            case DiscountScope::Category:
                if (! $discount->categories->contains($account->category_id)) {
                    throw ValidationException::withMessages([
                        'message' => 'Mã giảm giá không áp dụng cho danh mục này.',
                    ]);
                }
                break;
            case DiscountScope::Account:
                if (! $discount->accounts->contains($account->getKey())) {
                    throw ValidationException::withMessages([
                        'message' => 'Mã giảm giá không áp dụng cho tài khoản này.',
                    ]);
                }
                break;
            case DiscountScope::User:
                if (! auth()->check()) {
                    throw ValidationException::withMessages([
                        'message' => 'Vui lòng đăng nhập để sử dụng mã giảm giá này.',
                    ]);
                }

                if (! $discount->users->contains(auth()->id())) {
                    throw ValidationException::withMessages([
                        'message' => 'Mã giảm giá không áp dụng cho tài khoản của bạn.',
                    ]);
                }
                break;
        }

        return match ($discount->type) {
            DiscountType::Fixed => $discount->value,
            DiscountType::Percentage => $account->final_price * $discount->value / 100,
        };
    }
}

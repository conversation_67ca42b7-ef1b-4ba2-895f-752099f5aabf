<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\AffiliateStatus;
use App\Models\Affiliate;
use App\Models\User;

class ProcessUserReferralAction
{
    public function __invoke(User $user): void
    {
        if ($user->referred_by || $user->referral_code) {
            return;
        }

        $referralCode = session('referral_code');

        if (! $referralCode) {
            return;
        }

        $affiliate = Affiliate::query()
            ->where('referral_code', $referralCode)
            ->where('status', AffiliateStatus::Approved)
            ->first();

        if (! $affiliate) {
            return;
        }

        if ($affiliate->user_id === $user->getKey()) {
            return;
        }

        $user->update([
            'referral_code' => $referralCode,
            'referred_by' => $affiliate->getKey(),
            'referred_at' => now(),
        ]);

        session()->forget('referral_code');
    }
}

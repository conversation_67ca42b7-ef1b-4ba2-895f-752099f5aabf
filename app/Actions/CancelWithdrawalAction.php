<?php

declare(strict_types=1);

namespace App\Actions;

use App\Enums\TransactionType;
use App\Enums\WithdrawalStatus;
use App\Models\Affiliate;
use App\Models\User;
use App\Models\Withdrawal;
use Exception;
use Illuminate\Support\Facades\DB;

class CancelWithdrawalAction
{
    public function __invoke(Withdrawal $withdrawal): void
    {
        if ($withdrawal->status !== WithdrawalStatus::Pending) {
            return;
        }

        DB::transaction(function () use ($withdrawal): void {
            switch ($withdrawal->withdrawable_type) {
                case Affiliate::class:
                    $withdrawal->withdrawable->increment('available_commission', (int) $withdrawal->amount);
                    break;
                case User::class:
                    $withdrawal->withdrawable->increment('balance', (int) $withdrawal->amount);

                    $withdrawal->withdrawable->recordTransaction(
                        TransactionType::Refund,
                        (int) $withdrawal->amount,
                        $withdrawal,
                        'Hủy yêu cầu rút tiền #' . $withdrawal->id,
                    );
                    break;
                default:
                    throw new Exception('Invalid withdrawable type');
            }

            $withdrawal->update([
                'status' => WithdrawalStatus::Cancelled,
            ]);
        });
    }
}

<?php

declare(strict_types=1);

namespace App\Actions;

use App\Actions\Affiliate\CreateAffiliateCommissionAction;
use App\Enums\AccountStatus;
use App\Enums\TransactionType;
use App\Models\Account;
use App\Models\Discount;
use App\Models\User;
use App\Notifications\AccountPurchased;
use App\Support\Helper;
use Exception;
use Illuminate\Support\Facades\DB;

class PurchaseAccountAction
{
    public function __construct(
        protected ApplyAccountDiscountCode $applyAccountDiscountCode,
        protected CreateAffiliateCommissionAction $createAffiliateCommissionAction,
    ) {}

    /**
     * @throws Exception
     */
    public function __invoke(User $user, Account $account, ?string $discountCode = null): void
    {
        if ($user->getKey() === $account->user_id) {
            throw new Exception('Bạn không thể mua tài khoản của chính mình.');
        }

        if ($user->balance < $account->final_price) {
            $missingAmount = Helper::formatCurrency($account->final_price - $user->balance);

            throw new Exception("Bạn còn thiếu $missingAmount, vui lòng nạp thêm tiền để mua tài khoản này.");
        }

        DB::transaction(function () use ($user, $account, $discountCode): void {
            $discountAmount = 0;

            if ($discountCode) {
                $discountAmount = ($this->applyAccountDiscountCode)($account, $discountCode);
                Discount::query()->where('code', $discountCode)->increment('usage');
            }

            $total = $account->final_price - $discountAmount;

            $user->recordTransaction(
                TransactionType::BuyAccount,
                $total,
                $account,
                "Mua tài khoản game {$account->category->name} #{$account->getKey()}",
            );

            $account->user->recordTransaction(
                TransactionType::SellAccount,
                $account->final_price,
                $account,
                "Bán tài khoản game {$account->category->name} #{$account->getKey()}",
            );

            $account->update(['status' => AccountStatus::Sold]);

            $purchasedAccount = $user->purchasedAccounts()->create([
                'account_id' => $account->getKey(),
                'price' => $account->final_price,
                'total' => $total,
            ]);

            $account->user->notify(new AccountPurchased($purchasedAccount));

            ($this->createAffiliateCommissionAction)($purchasedAccount, $user);
        });
    }
}

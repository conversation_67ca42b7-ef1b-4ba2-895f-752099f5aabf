<?php

declare(strict_types=1);

namespace App\Logging;

use Monolog\Handler\MissingExtensionException;
use Monolog\Handler\TelegramBotHandler;
use Monolog\Logger;
use Monolog\Formatter\LineFormatter;
use Monolog\LogRecord;

class TelegramLogger
{
    /**
     * @throws MissingExtensionException
     */
    public function __invoke(array $config): Logger
    {
        $handler = new TelegramBotHandler(
            apiKey: config('services.telegram.bot_token'),
            channel: config('services.telegram.chat_id'),
            parseMode: 'MarkdownV2',
        );

        $formatter = new class extends LineFormatter {
            public function format(LogRecord $record): string
            {
                $message = parent::format($record);
                return $this->escapeMarkdownV2($message);
            }

            private function escapeMarkdownV2(string $text): string
            {
                $specialChars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!'];
                foreach ($specialChars as $char) {
                    $text = str_replace($char, '\\' . $char, $text);
                }
                return $text;
            }
        };

        $handler->setFormatter($formatter);

        return new Logger(config('app.name'), [$handler]);
    }
}

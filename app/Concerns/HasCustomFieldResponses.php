<?php

declare(strict_types=1);

namespace App\Concerns;

use App\Models\CustomField;
use App\Models\CustomFieldResponse;
use Illuminate\Database\Eloquent\Relations\MorphMany;

trait HasCustomFieldResponses
{
    public function customFieldResponses(): MorphMany
    {
        return $this->morphMany(CustomFieldResponse::class, 'respondable');
    }

    public function saveCustomFields(array $fields): void
    {
        $customFields = CustomField::query()->findMany(array_keys($fields));

        $responses = collect($fields)
            ->filter(fn($value, $key) => $customFields->contains('id', $key))
            ->map(function ($value, $key) {
                /** @var CustomFieldResponse $response */
                $response = $this->customFieldResponses()->firstOrNew([
                    'custom_field_id' => $key,
                ]);

                if (! $response->getKey()) {
                    $response->customField()->associate($key);
                    $response->respondable()->associate($this);
                }

                $response->value = $value;

                return $response;
            });

        $this->customFieldResponses()->saveMany($responses);
    }
}

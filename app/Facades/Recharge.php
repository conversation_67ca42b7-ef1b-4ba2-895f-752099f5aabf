<?php

declare(strict_types=1);

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

/**
 * @method static \App\Contracts\RechargeDriver driver(string $driver)
 * @method static \App\DataTransferObjects\RechargeResponse charge(string $telecom, int $amount, string $code, ?string $serial = null)
 * @method static \App\DataTransferObjects\RechargeResponse callback(\Illuminate\Http\Request $request)
 * @method static \Illuminate\Support\Collection getFees()
 * @method static string generateSignature(string $code, string $serial)
 *
 * @see \App\Managers\RechargeManager
 */
class Recharge extends Facade
{
    protected static function getFacadeAccessor(): string
    {
        return \App\Contracts\Recharge::class;
    }
}

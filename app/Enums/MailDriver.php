<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum MailDriver: string implements HasLabel
{
    case Array = 'array';

    case Log = 'log';

    case Smtp = 'smtp';

    case Sendmail = 'sendmail';

    case Mailgun = 'mailgun';

    case Ses = 'ses';

    case Postmark = 'postmark';

    case MailerSend = 'mailersend';

    case Resend = 'resend';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Array => 'Array',
            self::Log => 'Log',
            self::Smtp => 'SMTP',
            self::Sendmail => 'Sendmail',
            self::Mailgun => 'Mailgun',
            self::Ses => 'SES',
            self::Postmark => 'Postmark',
            self::MailerSend => 'MailerSend',
            self::Resend => 'Resend',
        };
    }
}

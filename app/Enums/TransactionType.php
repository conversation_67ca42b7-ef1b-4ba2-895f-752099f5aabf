<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum TransactionType: string implements HasLabel
{
    case Deposit = 'deposit';

    case Withdrawal = 'withdrawal';

    case AddBalance = 'add_balance';

    case SubtractBalance = 'subtract_balance';

    case BuyAccount = 'buy_account';

    case SellAccount = 'sell_account';

    case SpinWheel = 'spin_wheel';

    case WheelReward = 'wheel_reward';

    case Refund = 'refund';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Deposit => 'Nạp tiền',
            self::Withdrawal => 'Rút tiền',
            self::AddBalance => 'Cộng tiền',
            self::SubtractBalance => 'Trừ tiền',
            self::BuyAccount => 'Mua tài khoản game',
            self::SellAccount => 'Bán tài khoản game',
            self::SpinWheel => 'Quay vòng quay',
            self::WheelReward => 'Phần thưởng vòng quay',
            self::Refund => 'Hoàn tiền',
        };
    }

    public function isPositive(): bool
    {
        return in_array($this, [
            self::Deposit,
            self::AddBalance,
            self::SellAccount,
            self::WheelReward,
            self::Refund,
        ]);
    }

    public function isNegative(): bool
    {
        return ! $this->isPositive();
    }
}

<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum RewardType: string implements HasLabel
{
    case Money = 'money';

    case Item = 'item';

    case Account = 'account';

    case None = 'none';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Money => 'Tiền',
            self::Item => 'Vật phẩm',
            self::Account => 'Tài khoản game',
            self::None => 'Không có',
        };
    }

    public static function parse(RewardType| string $value): RewardType
    {
        if ($value instanceof RewardType) {
            return $value;
        }

        return self::tryFrom($value);
    }
}

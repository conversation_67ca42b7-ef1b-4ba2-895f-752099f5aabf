<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum AttributeType: string implements HasLabel
{
    case Dropdown = 'dropdown';

    case Text = 'text';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Dropdown => 'Dropdown (Chọn từ danh sách)',
            self::Text => 'Text (Nhập tự do)',
        };
    }
}

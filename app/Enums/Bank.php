<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum Bank: string implements HasLabel
{
    case VietinBank = 'VietinBank';

    case Vietcombank = 'Vietcombank';

    case MBBank = 'MBBank';

    case ACB = 'ACB';

    case VPBank = 'VPBank';

    case TPBank = 'TPBank';

    case MSB = 'MSB';

    case BIDV = 'BIDV';

    case Sacombank = 'Sacombank';

    case VIB = 'VIB';

    case HDBank = 'HDBank';

    case ShinhanBank = 'ShinhanBank';

    case Agribank = 'Agribank';

    case Techcombank = 'Techcombank';

    case ABBANK = 'ABBANK';

    case Eximbank = 'Eximbank';

    case PublicBank = 'PublicBank';

    case OCB = 'OCB';

    case KienLongBank = 'KienLongBank';

    public function getCode(): string
    {
        return match ($this) {
            self::VietinBank => 'ICB',
            self::Vietcombank => 'VCB',
            self::MBBank => 'MB',
            self::ACB => 'ACB',
            self::VPBank => 'VPB',
            self::TPBank => 'TPB',
            self::MSB => 'MSB',
            self::BIDV => 'BIDV',
            self::Sacombank => 'STB',
            self::VIB => 'VIB',
            self::HDBank => 'HDB',
            self::ShinhanBank => 'SHB',
            self::Agribank => 'VBA',
            self::Techcombank => 'TCB',
            self::ABBANK => 'ABB',
            self::Eximbank => 'EIB',
            self::PublicBank => 'PBVN',
            self::OCB => 'OCB',
            self::KienLongBank => 'KLB',
        };
    }

    public function getLabel(): ?string
    {
        $label = match ($this) {
            self::VietinBank => 'Ngân hàng TMCP Công thương Việt Nam',
            self::Vietcombank => 'Ngân hàng TMCP Ngoại Thương Việt Nam',
            self::MBBank => 'Ngân hàng TMCP Quân đội',
            self::ACB => 'Ngân hàng TMCP Á Châu',
            self::VPBank => 'Ngân hàng TMCP Việt Nam Thịnh Vượng',
            self::TPBank => 'Ngân hàng TMCP Tiên Phong',
            self::MSB => 'Ngân hàng TMCP Hàng Hải',
            self::BIDV => 'Ngân hàng TMCP Đầu tư và Phát triển Việt Nam',
            self::Sacombank => 'Ngân hàng TMCP Sài Gòn Thương Tín',
            self::VIB => 'Ngân hàng TMCP Quốc tế',
            self::HDBank => 'Ngân hàng TMCP Phát triển Thành phố Hồ Chí Minh',
            self::ShinhanBank => 'Ngân hàng TNHH MTV Shinhan Việt Nam',
            self::Agribank => 'Ngân hàng Nông nghiệp và Phát triển Nông thôn Việt Nam',
            self::Techcombank => 'Ngân hàng TMCP Kỹ Thương Việt Nam',
            self::ABBANK => 'Ngân hàng TMCP An Bình',
            self::Eximbank => 'Ngân hàng TMCP Xuất Nhập khẩu Việt Nam',
            self::PublicBank => 'Ngân hàng TNHH MTV Public Việt Nam',
            self::OCB => 'Ngân hàng TMCP Phương Đông',
            self::KienLongBank => 'Ngân hàng TMCP Kiên Long',
        };

        return "{$label} ({$this->getCode()})";
    }
}

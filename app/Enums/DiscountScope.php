<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum DiscountScope: string implements HasLabel
{
    case All = 'all';

    case Category = 'category';

    case Account = 'account';

    case User = 'user';

    public static function parse(mixed $value): self
    {
        if ($value instanceof self) {
            return $value;
        }

        return self::tryFrom($value);
    }

    public function getLabel(): ?string
    {
        return match ($this) {
            self::All => 'Tất Cả',
            self::Category => '<PERSON><PERSON>',
            self::Account => 'T<PERSON><PERSON>',
            self::User => 'Người <PERSON>',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum PriceRange: string implements HasLabel
{
    case BELOW_50K = '0-50000';

    case FROM_50K_TO_200K = '50000-200000';

    case FROM_200K_TO_500K = '200000-500000';

    case FROM_500K_TO_1M = '500000-1000000';

    case FROM_1M_TO_5M = '1000000-5000000';

    case FROM_5M_TO_10M = '5000000-10000000';

    case ABOVE_10M = '10000000';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::BELOW_50K => 'Dưới 50k',
            self::FROM_50K_TO_200K => 'Từ 50k đến 200k',
            self::FROM_200K_TO_500K => 'Từ 200k đến 500k',
            self::FROM_500K_TO_1M => 'Từ 500k đến 1tr',
            self::FROM_1M_TO_5M => 'Từ 1tr đến 5tr',
            self::FROM_5M_TO_10M => 'Từ 5tr đến 10tr',
            self::ABOVE_10M => 'Trên 10 triệu',
        };
    }
}

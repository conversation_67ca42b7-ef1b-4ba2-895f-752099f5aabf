<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum PostStatus: string implements HasLabel, HasColor
{
    case Draft = 'draft';

    case Published = 'published';

    case Pending = 'pending';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Draft => 'Nháp',
            self::Published => 'Đã xuất bản',
            self::Pending => 'Đang chờ',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::Draft => 'gray',
            self::Published => 'success',
            self::Pending => 'warning',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum RechargeStatus: string implements HasLabel, HasColor
{
    case Pending = 'pending';

    case Completed = 'completed';

    case Failed = 'failed';

    case Invalid = 'invalid';

    case WrongAmount = 'wrong-amount';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Pending => 'Chờ xử lý',
            self::Completed => 'Thành công',
            self::Failed => 'Thất bại',
            self::Invalid => 'Thẻ lỗi',
            self::WrongAmount => 'Sai mệnh giá',
        };
    }

    public function getColor(): string | array | null
    {
        return match ($this) {
            self::Pending => 'warning',
            self::Completed => 'success',
            self::Failed, self::Invalid => 'danger',
            self::WrongAmount => 'gray',
        };
    }
}

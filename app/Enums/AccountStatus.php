<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasIcon;
use Filament\Support\Contracts\HasLabel;

enum AccountStatus: string implements HasLabel, HasColor, HasIcon
{
    case Selling = 'selling';

    case Sold = 'sold';

    case Pending = 'pending';

    case Deleted = 'deleted';

    case Rejected = 'rejected';

    public function getColor(): string|array|null
    {
        return match ($this) {
            self::Selling => 'success',
            self::Sold => 'info',
            self::Pending => 'warning',
            self::Deleted, self::Rejected => 'danger',
        };
    }

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Selling => 'Đang bán',
            self::Sold => 'Đã bán',
            self::Pending => 'Chờ duyệt',
            self::Deleted => 'Đã xóa',
            self::Rejected => 'Từ chối',
        };
    }

    public function getIcon(): ?string
    {
        return match ($this) {
            self::Selling => 'heroicon-o-currency-dollar',
            self::Sold => 'heroicon-o-check-circle',
            self::Pending => 'heroicon-o-clock',
            self::Deleted => 'heroicon-o-trash',
            self::Rejected => 'heroicon-o-x-circle',
        };
    }
}

<?php

declare(strict_types=1);

namespace App\Enums;

use Filament\Support\Contracts\HasLabel;

enum RechargeProvider: string implements HasLabel
{
    case Thesieure = 'thesieure';

    case Cardvip = 'cardvip';

    case Thecaosieure = 'thecaosieure';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::Thesieure => 'Thesieure',
            self::Cardvip => 'Cardvip ',
            self::Thecaosieure => 'Thecaosieure',
        };
    }
}

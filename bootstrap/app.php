<?php

declare(strict_types=1);

use App\Http\Middleware\HandleInertiaRequests;
use App\Http\Middleware\HandleReferralCode;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Log;
use Illuminate\Console\Scheduling\Schedule;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        channels: __DIR__ . '/../routes/channels.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->web(HandleInertiaRequests::class);
        $middleware->web(HandleReferralCode::class);
    })
    ->withSchedule(function (Schedule $schedule): void {
        $schedule->command('queue:work --once')->everySecond();

        $schedule
            ->command('affiliate:process-commissions --auto-approve')
            ->hourly()
            ->withoutOverlapping();

        $schedule
            ->command('affiliate:upgrade-tiers')
            ->daily()
            ->withoutOverlapping();
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $exceptions->report(function (Throwable $e): void {
            if (app()->isLocal() || app()->runningInConsole()) {
                return;
            }

            if (! config('services.telegram.bot_token') || ! config('services.telegram.chat_id')) {
                return;
            }

            $key = sprintf('exception.%s', sha1($e->getMessage()));

            if (cache()->has($key)) {
                return;
            }

            cache()->put($key, true, 60);

            Log::channel('telegram')->critical($e->getMessage(), [
                'url' => request()->fullUrl(),
                'method' => request()->method(),
                'ip' => request()->ip(),
                'data' => request()->all(),
                'exception' => $e::class,
                'file' => sprintf('%s:%s', $e->getFile(), $e->getLine()),
            ]);
        });
    })->create();
